'use client';

import clsx from 'clsx';
import Image from 'next/image';

import { useOrdinalFormatter } from '@/hooks/i18n/useOrdinalFormatter';
import { JsonFieldType } from '@/strapi/types/helper';
import PlaceholderImage from '@/ui/assets/images/ewc-placeholder.png';
import { formatToCurrency } from '@/utils/format';

import { GameWinner } from '../../../fetchCompletedGamesData';

type Placement = 'first' | 'second' | 'third';

interface WinnerRowProps {
  placement: Placement;
  winners: GameWinner[];
  translations: JsonFieldType;
}

export const WinnerRow = ({ placement, winners, translations }: WinnerRowProps) => {
  const winner = winners.at(0);
  const otherWinner = winners.at(1);
  const isSharedThirdPlacement = placement === 'third' && otherWinner;
  const sharedThirdStyles = isSharedThirdPlacement && 'align-top py-[3px] md:py-[5px]';
  return (
    <tr
      className={clsx(
        'font-primary leading-none font-bold',
        'ltr:max-md:first:[&>td:first-child]:rounded-tl-sm ltr:max-md:first:[&>td:nth-child(2)]:rounded-tr-sm',
        'rtl:max-md:first:[&>td:first-child]:rounded-tr-sm rtl:max-md:first:[&>td:nth-child(2)]:rounded-tl-sm',
        'ltr:max-md:last:[&>td:first-child]:rounded-bl-sm ltr:max-md:last:[&>td:nth-child(2)]:rounded-br-sm',
        'rtl:max-md:last:[&>td:first-child]:rounded-br-sm rtl:max-md:last:[&>td:nth-child(2)]:rounded-bl-sm',
        placement === 'first' ? 'bg-gold-primary text-white' : 'text-dark-default',
        placement === 'second' && 'border-gray border-b',
      )}
    >
      <td
        className={clsx(
          'ms-px w-6 ps-1.5 text-[10px] md:rounded-s-[3px]',
          { 'text-[#727272]': placement === 'second', 'text-[#683C13]': placement === 'third' },
          sharedThirdStyles,
        )}
      >
        <PlacementCellContents placement={isSharedThirdPlacement ? 'sharedThird' : placement} />
      </td>
      <td className="max-w-[100px] py-[3px] ps-2 max-md:pe-1.5 md:py-[5px] xl:max-2xl:max-w-[80px]">
        <div className="flex flex-col">
          <TeamCellContents {...winner} translations={translations} />
          {isSharedThirdPlacement && <TeamCellContents {...otherWinner} translations={translations} />}
        </div>
      </td>
      <td className={clsx('ps-1 text-center text-xs max-md:hidden', sharedThirdStyles)}>
        {winner?.points ? formatToCurrency(winner.points) : '-'}
      </td>
      <td className={clsx('ps-1 pe-1.5 text-center text-xs max-md:hidden md:rounded-e-[3px]', sharedThirdStyles)}>
        {winner?.prize ? `$${formatToCurrency(winner.prize)}` : '-'}
      </td>
    </tr>
  );
};

type ExpandedPlacement = Placement | 'sharedThird';

const PlacementCellContents = ({ placement }: { placement: ExpandedPlacement }) => {
  const format = useOrdinalFormatter({ arSimplified: true });
  if (placement === 'first') {
    return <p>{format(1)}</p>;
  }
  if (placement === 'second') {
    return <p>{format(2)}</p>;
  }
  if (placement === 'third') {
    return <p>{format(3)}</p>;
  }
  if (placement === 'sharedThird') {
    return (
      <div className="flex flex-col">
        <p>{format(3)}</p>
        <p>-{format(4)}</p>
      </div>
    );
  }
};

const TeamCellContents = ({ logoUrl, name, translations }: Partial<GameWinner> & { translations: JsonFieldType }) => {
  return (
    <div className="flex w-full items-center gap-1">
      <Image alt="" className="size-[18px] object-contain" height={18} src={logoUrl ?? PlaceholderImage} width={18} />
      <p className="grow truncate text-[10px] uppercase md:text-xs">{name ?? translations.tbd ?? 'tbd'}</p>
    </div>
  );
};
