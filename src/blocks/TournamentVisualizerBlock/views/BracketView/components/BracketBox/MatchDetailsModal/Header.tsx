import clsx from 'clsx';
import Image from 'next/image';

import PlaceholderLogo from '@/ui/assets/images/ewc-placeholder.png';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { MatchDetailsTeam } from './types';

interface Props {
  teams: MatchDetailsTeam[];
  status: LocalMatchStatus;
}

export const Header = ({ teams, status }: Props) => {
  const [teamA, teamB] = teams;
  return (
    <div
      className={clsx(
        'flex items-center gap-1 rounded-sm p-1',
        status === LocalMatchStatus.LIVE ? 'bg-[#F40F30]' : 'bg-dark-default',
      )}
    >
      <TeamDescriptor {...teamA} />
      <div className="flex gap-[5px]">
        <ScoreBox score={teamA.score} />
        <ScoreBox score={teamB.score} />
      </div>
      <TeamDescriptor isReversed {...teamB} />
    </div>
  );
};

interface TeamDescriptorProps {
  name: string;
  logoUrl: string | null;
  isReversed?: boolean;
}

const TeamDescriptor = ({ name, logoUrl, isReversed }: TeamDescriptorProps) => {
  return (
    <div className={clsx('flex flex-1 items-center gap-1', isReversed && 'flex-row-reverse')}>
      <Image alt="" className="size-8 rounded-xs bg-white" height={32} src={logoUrl ?? PlaceholderLogo} width={32} />
      <p className="font-primary text-xs leading-none font-bold text-white uppercase">{name}</p>
    </div>
  );
};

interface ScoreBoxProps {
  score: number | null;
}

const ScoreBox = ({ score }: ScoreBoxProps) => {
  return (
    <div className="flex size-6 items-center justify-center rounded-xs bg-white">
      <p className="font-primary text-dark-default text-lg leading-none font-bold uppercase">{score ?? '-'}</p>
    </div>
  );
};
