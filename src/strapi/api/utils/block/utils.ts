import { DynamicZoneBlock } from '@/strapi/types/block';
import {
  FESTIVAL_FILTER_BLOCK_KEY,
  FESTIVAL_LIST_BLOCK_KEY,
  FestivalFilterBlockType,
  FestivalListBlockType,
} from '@/strapi/types/festivalBlock';
import { GAME_SCHEDULE_BLOCK_KEY, GameScheduleBlockType } from '@/strapi/types/gamePageBlock';

export function assembleGameScheduleBlocks(blocks: DynamicZoneBlock[], data?: any) {
  const gameScheduleBlocks = blocks.filter((b) => b.__component === GAME_SCHEDULE_BLOCK_KEY) as GameScheduleBlockType[];
  gameScheduleBlocks.forEach((b) => (b.competitions = data?.competitionSlugs ?? []));
}

export function assembleFestivalListBlocks(blocks: DynamicZoneBlock[], data?: any) {
  const festivalListBlocks = blocks.filter((b) => b.__component === FESTIVAL_LIST_BLOCK_KEY) as FestivalListBlockType[];
  festivalListBlocks.forEach((b) => (b.festival = data.festivalData.festival ?? []));
}

export function assembleFestivalFilterBlocks(blocks: DynamicZoneBlock[], data?: any) {
  const festivalFilterBlocks = blocks.filter(
    (b) => b.__component === FESTIVAL_FILTER_BLOCK_KEY,
  ) as FestivalFilterBlockType[];
  festivalFilterBlocks.forEach((b) => (b.venues = data.venuesData.venues ?? []));
}
