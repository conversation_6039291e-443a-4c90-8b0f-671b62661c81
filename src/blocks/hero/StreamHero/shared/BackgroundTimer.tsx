import Image from 'next/image';
import { memo } from 'react';

import { StrapiImage } from '@/components/StrapiImage';
import { Timer } from '@/components/Timer';
import { JsonFieldType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import DefaultBackgroundImage from '@/ui/assets/images/intermission-background.png';

interface Props {
  backgroundImage?: MediaType | null;
  startTime: string;
  translations: JsonFieldType;
}

export const BackgroundTimer = memo(({ backgroundImage, startTime, translations }: Props) => {
  return (
    <>
      <div className="absolute inset-0 size-full">
        {backgroundImage ? (
          <StrapiImage className="size-full object-cover" image={backgroundImage} />
        ) : (
          <Image alt="" className="size-full object-cover" src={DefaultBackgroundImage} />
        )}
      </div>
      <div className="relative">
        <Timer targetDatetime={startTime} translations={translations} />
      </div>
    </>
  );
});

BackgroundTimer.displayName = 'BackgroundTimer';
