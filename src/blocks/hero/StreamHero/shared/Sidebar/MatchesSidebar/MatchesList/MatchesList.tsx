import { forwardRef } from 'react';

import { LocalMatchStatus } from '@/utils/matchSeries';

import { JsonFieldType } from '../../../../../../../strapi/types/helper';
import { GameStream } from '../../../types/GameStream';
import { StreamHeroMatch } from '../../../types/StreamHeroMatch';
import { Header } from './Header';
import { MatchBox } from './MatchBox';

interface Props {
  matches: StreamHeroMatch[];
  startTime?: string;
  status: LocalMatchStatus;
  selectedMatchId: string;
  onMatchSelect: (id: string) => void;
  translations?: JsonFieldType;
  apiTranslations?: JsonFieldType;
  selectedGameStream: GameStream | null;
  slug: string | null;
}

export const MatchesList = forwardRef<HTMLDivElement, Props>(
  (
    {
      matches,
      startTime,
      selectedMatchId,
      onMatchSelect,
      status,
      translations,
      apiTranslations,
      selectedGameStream,
      slug,
    }: Props,
    ref,
  ) => {
    return (
      <div className="flex flex-col-reverse gap-2 lg:flex-col lg:gap-1">
        <Header startTime={startTime} status={status} translations={translations} />
        <div className="flex h-full gap-2 lg:flex-col">
          {matches.map((m, i) => (
            <MatchBox
              key={m.id}
              {...m}
              apiTranslations={apiTranslations}
              isSelected={m.id === selectedMatchId}
              ref={i === 0 ? ref : null}
              selectedGameStream={selectedGameStream}
              slug={slug}
              status={status}
              stream={m.stream}
              translations={translations}
              onSelect={() => onMatchSelect(m.id)}
            />
          ))}
        </div>
      </div>
    );
  },
);

MatchesList.displayName = 'MatchesList';
