'use client';

import _ from 'lodash';

import { RichTextContent } from '@/blocks/RichTextBlock/RichTextContent';
import { convertToStandingsData } from '@/blocks/StandingsTableBlock/utils';
import { StrapiImage } from '@/components/StrapiImage';
import { useGameTournamentsData } from '@/services/graphql/hooks/tournaments';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { GameType } from '@/strapi/types/collection/game';
import { HERO_WINNER_HIGHLIGHT_BANNER, HeroWinnerHighlightBannerType } from '@/strapi/types/hero';

interface Props extends Partial<GameType>, Pick<SiteConfigData, 'translations'> {}

export function WinnerHighlightBanner(props: Props) {
  const { keyArt, logoLight, gradientHex, hero, translations } = props;
  const isWinnerBanner = hero?.__component === HERO_WINNER_HIGHLIGHT_BANNER;
  const heroContent = isWinnerBanner ? (hero as HeroWinnerHighlightBannerType) : null;
  const { data } = useGameTournamentsData({ isSubscriptionDisabled: true });
  const standings = data?.tournaments?.result ? convertToStandingsData(data.tournaments.result) : null;
  const topEntry = _.chain(standings)
    .filter((entry) => typeof entry.points === 'number')
    .orderBy('points', 'desc')
    .find((entry) => !!entry.clubs?.[0])
    .value();

  const ccPoints = topEntry?.points;
  const ccPointsString = ccPoints != null ? ccPoints.toString() : '-';
  const subtitleString =
    translations?.winnerSubtitle?.replace('{0}', ccPointsString) ?? `${ccPointsString} CC Points Achieved`;
  return (
    <section className="relative w-full bg-[#f5f5f5] text-white">
      <div className="relative h-[480px] w-full md:h-[414px]">
        {keyArt && <StrapiImage className="h-full w-full object-cover contrast-75 grayscale" image={keyArt} />}
        {gradientHex && <div className="absolute bottom-0 h-[4px] w-full" style={{ background: gradientHex }} />}
        {logoLight && (
          <div className="absolute start-[50px] top-[44px] hidden h-[76px] w-fit lg:block">
            <StrapiImage className="h-full w-full object-contain" image={logoLight} />
          </div>
        )}
      </div>

      <div className="relative z-10 mx-auto mt-[-150px] h-[500px] w-[90%] max-w-[1280px] overflow-hidden rounded-4xl bg-[#f5f5f5] shadow-xl sm:mt-[-270px] md:mt-[-210px] lg:h-[700px]">
        {heroContent?.heroWinnerImage && (
          <StrapiImage className="h-full w-full object-cover" image={heroContent.heroWinnerImage} />
        )}
        <div className="absolute inset-0 bg-[linear-gradient(180deg,rgba(21,21,21,0)_0%,rgba(21,21,21,0.9)_100%)]" />
        <div className="absolute inset-0 flex flex-col items-center justify-end gap-4 p-6 text-center">
          {heroContent?.winnerLogo && (
            <StrapiImage
              className="h-[106px] w-[106px] rounded-full object-contain md:h-[124px] md:w-[124px] lg:h-[180px] lg:w-[180px]"
              image={heroContent.winnerLogo}
            />
          )}
          {heroContent?.ewcLogo && (
            <div className="flex items-center justify-center">
              <StrapiImage className="h-16 w-auto object-contain md:h-18 lg:h-20" image={heroContent.ewcLogo} />
            </div>
          )}
          {heroContent?.heroWinnerSubtitle && (
            <p className="font-primary text-[14px] font-bold uppercase drop-shadow md:text-[18px] lg:text-[28px]">
              {heroContent.heroWinnerSubtitle}
            </p>
          )}
        </div>
      </div>

      <div className="mx-auto mt-10 flex w-full max-w-[1200px] flex-col items-center gap-4 bg-[#f5f5f5] text-center">
        {heroContent?.heroWinnerTitle && <h2 className="text-gold-primary text-h1">{heroContent.heroWinnerTitle}</h2>}

        {ccPoints && (
          <div className="bg-radial-gold font-base bg-gold-primary rounded-sm px-4 py-1 text-sm text-[14px] font-extrabold text-white md:text-[18px] lg:text-[21px]">
            {subtitleString}
          </div>
        )}

        {heroContent?.heroWinnerDescription && (
          <RichTextContent
            content={heroContent.heroWinnerDescription}
            paragraphClassName="prose prose-invert font-base px-5 text-[14px] font-normal text-[#151515] md:text-[18px] lg:text-[20px] text-justify"
          />
        )}
      </div>
    </section>
  );
}
