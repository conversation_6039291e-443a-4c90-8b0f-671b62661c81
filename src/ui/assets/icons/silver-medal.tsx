import React, { useId } from 'react';

type Props = React.SVGProps<SVGSVGElement>;

/**
 * React component rendering a silver medal SVG icon with unique gradient and clipPath IDs.
 *
 * @remarks
 * This component should be used instead of embedding a regular SVG file directly.
 * The SVG uses internal references (IDs) for gradients and clip paths. When the same SVG is rendered
 * multiple times on a page, static IDs can cause conflicts—subsequent instances may reference the
 * definitions of the first rendered SVG. If the first instance is hidden or removed, the others may break
 * due to missing references.
 *
 * By generating unique IDs for each instance using React's `useId`, this component ensures that
 * gradients and clip paths are correctly scoped and do not interfere with each other, maintaining
 * visual integrity regardless of how many times the icon is rendered or manipulated in the DOM.
 *
 * @param props - Standard SVG and React component props.
 * @returns A silver medal SVG icon with isolated definitions for safe multiple usage.
 */
const SilverMedal: React.FC<Props> = (props) => {
  const id = useId();
  const clipPathId = `silver-medal-clip-${id}`;
  const gradientId = `silver-medal-gradient-${id}`;

  return (
    <svg fill="none" height="100%" viewBox="0 0 33 32" width="100%" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath={`url(#${clipPathId})`}>
        <path
          clipRule="evenodd"
          d="M17.0925 2.37036L12.9443 10.0741L17.0925 19.5555H26.574L17.0925 2.37036ZM27.7592 21.926H16.4999L12.9443 29.6297H32.4999L27.7592 21.926ZM0.5 29.6296L11.1667 12.4444L14.7222 20.7407L9.98148 29.6296H0.5Z"
          fill={`url(#${gradientId})`}
          fillRule="evenodd"
        />
      </g>
      <defs>
        <linearGradient gradientUnits="userSpaceOnUse" id={gradientId} x1="16.5" x2="17.5" y1="37" y2="6">
          <stop stopColor="#5F5F5F" />
          <stop offset="0.49" stopColor="#8F8C8C" />
          <stop offset="0.995" stopColor="#C6C6C6" />
        </linearGradient>
        <clipPath id={clipPathId}>
          <rect fill="white" height="32" transform="translate(0.5)" width="32" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default SilverMedal;
