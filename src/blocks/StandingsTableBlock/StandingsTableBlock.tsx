'use client';

import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { useGameTournamentsData } from '@/services/graphql/hooks/tournaments';
import { StandingsTableBlockType } from '@/strapi/types/gamePageBlock';

import { StandingsTable } from './StandingsTable';
import { convertToStandingsData } from './utils';

export const StandingsTableBlock = ({ section, translations }: StandingsTableBlockType) => {
  const { data, loading, error } = useGameTournamentsData({ isSubscriptionDisabled: true });
  const rankedData = data?.tournaments?.result ? convertToStandingsData(data.tournaments.result) : null;

  return (
    <BlockSectionWrapper {...section}>
      <StandingsTable isLoading={loading} standings={rankedData} translations={translations ?? {}} />
      {error && <div>error: {error.message}</div>}
    </BlockSectionWrapper>
  );
};
