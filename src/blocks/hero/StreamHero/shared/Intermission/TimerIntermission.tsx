import { BackgroundTimer } from '../BackgroundTimer';
import { IntermissionProps } from './Intermission';

export const TimerIntermission = ({ stream, translations }: Omit<IntermissionProps, 'matches'>) => {
  const now = new Date().toISOString();
  const isValidLocalStartTime = stream.startTime && stream.startTime > now;

  return (
    isValidLocalStartTime && (
      <BackgroundTimer
        backgroundImage={stream.backgroundImage}
        key={stream.startTime}
        startTime={stream.startTime as string}
        translations={translations}
      />
    )
  );
};
