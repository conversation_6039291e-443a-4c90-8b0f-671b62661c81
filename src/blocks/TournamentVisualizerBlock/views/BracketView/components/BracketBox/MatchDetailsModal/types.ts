import { MatchSeriesGameResult } from '@/services/graphql/types/matchSeries';
import { LocalMatchStatus } from '@/utils/matchSeries';

export interface MatchDetails {
  id: string;
  teams: MatchDetailsTeam[];
  results: MatchDetailsResult[];
  status: LocalMatchStatus;
  startTime: string;
}

export interface MatchDetailsTeam {
  id: string;
  name: string;
  logoUrl: string | null;
  score: number | null;
  players: MatchDetailsPlayer[];
}

export interface MatchDetailsPlayer {
  name: string;
  isCoach?: boolean;
}

export interface MatchDetailsResult {
  map: string;
  teams: { id: string; score: number | null; result: MatchSeriesGameResult }[];
}
