'use client';

import clsx from 'clsx';
import capitalize from 'lodash/capitalize';
import { useState } from 'react';

import { SearchInput } from '@/components/SearchInput';
import { StreamProviderType } from '@/strapi/types/collection/streamProvider';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';

import { BlockSectionTitle } from '../shared/BlockSectionWrapper/BlockSectionTitle';
import { StreamsByLanguage } from './hooks';
import { LanguageStreamsList } from './LanguageStreamsList';

interface Props {
  title?: string | null;
  subtitle?: string | null;
  translations: JsonFieldType;
  streams: StreamsByLanguage[];
  providers: StreamProviderType[];
  onClose: () => void;
}

export const WhereToWatchModal = ({ title, subtitle, translations, streams, providers, onClose }: Props) => {
  const [searchValue, setSearchValue] = useState('');

  const normalizedSearch = searchValue.length >= 2 ? searchValue.toLowerCase() : '';
  const filteredStreams = streams.filter((sl) => sl.language.toLowerCase().includes(normalizedSearch));
  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center">
      <div className="bg-dark-default absolute inset-0 opacity-90" />
      <div
        className={clsx(
          'bg-white-dirty relative rounded-lg md:rounded-2xl',
          'px-2 pt-4 pb-3 md:px-4 md:pb-4',
          'lg:ms-sidebar-width max-h-[80vh] w-full max-lg:mx-4 lg:max-w-[80vw]',
          'flex flex-col-reverse gap-3 overflow-y-auto md:flex-col md:gap-[15px]',
        )}
      >
        <div className="md:w-fit md:self-end">
          <Button
            brazeEventProperties={{
              button_name: 'Close button',
              location: 'Where To Watch Block - All Languages Modal',
            }}
            isFullWidth
            text="Close"
            onClick={onClose}
          />
        </div>
        <div className="flex flex-col gap-4 md:px-4 md:pb-4">
          {title && <BlockSectionTitle subtitle={subtitle} title={title} />}
          <div className="flex flex-col gap-2.5 md:gap-4">
            <SearchInput
              placeholder={capitalize(translations?.['searchLanguages'] ?? 'Search Languages')}
              value={searchValue}
              onChange={setSearchValue}
            />
            <div className="flex flex-col gap-1 md:gap-2" key={searchValue}>
              {filteredStreams.map((l, i) => (
                <LanguageStreamsList
                  isExpanded={i === 0}
                  key={l.languageCode}
                  language={l.language}
                  providers={providers}
                  streams={l.streamUrls}
                  translations={translations ?? {}}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
