import { NextRequest, NextResponse } from 'next/server';

import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';
import { getMobileProvisioningData } from '@/strapi/api/single/mobileApp';

export * from '@/config/page-cache';

function isValidLocale(locale: string): locale is Locale {
  return (ALL_LOCALES as readonly string[]).includes(locale);
}

function getLocaleFromRequest(request: NextRequest) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  return pathname.split('/')[1];
}

export async function GET(req: NextRequest) {
  const locale = getLocaleFromRequest(req);

  if (!isValidLocale(locale)) {
    return NextResponse.json({ error: `Unsupported locale: ${locale}` }, { status: 404 });
  }

  try {
    const data = await getMobileProvisioningData(locale);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Failed to fetch mobile app data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch mobile app data' },
      {
        status: 500,
      },
    );
  }
}
