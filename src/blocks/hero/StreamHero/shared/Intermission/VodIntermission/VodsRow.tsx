import { VodCard } from '@/blocks/VodBlock/VodCard';
import { VodEnrichedItem } from '@/blocks/VodBlock/VodGrid';

import { IntermissionProps } from '../Intermission';
import { RowContainer } from './RowContainer';

type Props = Omit<IntermissionProps, 'isLoading'>;

export const VodsRow = ({ stream, translations }: Props) => {
  if (stream.vods.length === 0) {
    return null;
  }

  const vods = stream.vods.slice(0, 4) as VodEnrichedItem[];
  return (
    <RowContainer
      subtitle={translations.watchNow ?? 'watch now'}
      title={translations.thisWeeksHighlights ?? 'this weeks highlights'}
    >
      {vods.map((v) => (
        <div className="max-2xl:nth-4:hidden" key={v.id}>
          <VodCard {...v} isSliderMode showLanguageBadge translations={translations} />
        </div>
      ))}
    </RowContainer>
  );
};
