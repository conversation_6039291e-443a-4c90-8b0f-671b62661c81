import clsx from 'clsx';
import React from 'react';

import { JsonFieldType } from '@/strapi/types/helper';

import { Round } from '../../../utils/brackets/types';
import { BracketBox } from './BracketBox';
import { getBracketWrapperHeightForTier } from './const';

type RoundColumnProps = Round & { isFunnelled: boolean; tier: number; translations: JsonFieldType };

export const RoundColumn = ({ name, matches, isFunnelled, tier, translations }: RoundColumnProps) => {
  return (
    <div className="flex flex-col gap-4 first:ps-4 last:pe-4 md:first:ps-8 md:last:pe-8">
      <div className="bg-gray flex h-[17px] items-center justify-center rounded-sm">
        <p className="text-dark-default font-primary text-[10px] leading-[1.1] font-bold uppercase">{name}</p>
      </div>
      <div className="flex flex-1 flex-col gap-4">
        {matches.map((m) => (
          <div
            className={clsx(
              'flex flex-col justify-center',
              isFunnelled ? getBracketWrapperHeightForTier(tier) : 'h-full',
            )}
            key={m.id}
          >
            <BracketBox {...m} translations={translations} />
          </div>
        ))}
      </div>
    </div>
  );
};
