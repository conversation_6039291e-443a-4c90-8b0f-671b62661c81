'use client';

type VenueTooltipProps = {
  text: string;
  subtext: string;
  x: number;
  y: number;
  venueId: string;
  onTooltipClickAction: (venueId: string) => void;
  onHoverStartAction: (venueId: string) => void;
  onHoverEndAction: (venueId: string) => void;
};

export default function VenueTooltip({
  text,
  x,
  y,
  venueId,
  onTooltipClickAction,
  onHoverStartAction,
  onHoverEndAction,
  subtext,
}: VenueTooltipProps) {
  const handleMouseEnter = () => onHoverStartAction(venueId);
  const handleMouseLeave = () => onHoverEndAction(venueId);
  const handleClick = () => onTooltipClickAction(venueId);

  return (
    <div
      className="font-primary pointer-events-auto absolute z-50 text-white"
      style={{
        top: `calc(${y}%)`,
        left: `${x}%`,
        transform: 'translate(-30%, -70%)',
      }}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className="max-w-[160px] rounded-lg px-3 py-2 text-center break-words"
        style={{
          background: '#FFFFFF03',
          border: '1px solid #FFFFFF1A',
          boxShadow: '0px 6px 32px 0px #00000080',
          backdropFilter: 'blur(44px)',
          WebkitBackdropFilter: 'blur(44px)',
        }}
      >
        <div className="line-clamp-2 overflow-hidden text-sm leading-tight font-bold tracking-wide uppercase">
          {text}
        </div>
        <div className="text-xs">{subtext}</div>
      </div>

      <div
        className="pointer-events-none mx-auto h-0 w-0"
        style={{
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderTop: '8px solid #E5CA49',
        }}
      />
    </div>
  );
}
