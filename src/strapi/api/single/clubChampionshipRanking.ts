import { SeoType } from '@/strapi/types/shared/seo';

import { Locale } from '../../../hooks/i18n/const';
import { StrapiBase } from '../../types/base';
import { DynamicZoneBlock } from '../../types/block';
import { RichTextContentType } from '../../types/helper';
import { MediaType } from '../../types/media';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils/block';

interface ClubChampionshipRankingPageData extends StrapiBase {
  title: string | null;
  subtitle: RichTextContentType | null;
  bgMedia: MediaType | null;
  topExplainerText: RichTextContentType | null;
  middleExplainerText: RichTextContentType | null;
  bottomExplainerText: RichTextContentType | null;
  grayedOutText: RichTextContentType | null;
  leaderboardTitle: string | null;
  leaderboardSubtitle: string | null;
  blocks: DynamicZoneBlock[];
  tournamentStart: string | null;
  tournamentEnd: string | null;
  leaderboardButtonText: string | null;
  simulatorTitle: string | null;
  simulatorSubtitle: string | null;
  simulatorButtonResetText: string | null;
  simulatorButtonExitText: string | null;
  simulatorWarningText: RichTextContentType | null;
  enableSimulator: boolean;
  logoOverride: MediaType | null;
  year: string | null;
}

export async function getClubChampionshipRankingPageData(locale: Locale) {
  const pageData = await strapiClient.single('club-championship-ranking-page').find({
    populate: { bgMedia: { populate: '*' }, logoOverride: { populate: '*' }, blocks: getDynamicZoneBlocks() },
    ...getStrapiDefaultParams(locale),
  });

  const data = pageData.data as ClubChampionshipRankingPageData;
  if (!data) {
    return null;
  }

  await assembleBlocksWithAdditionalData(data.blocks, locale);
  return data;
}

export async function getClubChampionshipRankingPageSeo(locale: Locale) {
  try {
    const clubChampionshipRankingPage = await strapiClient
      .single('club-championship-ranking-page')
      .find({ populate: 'seo', ...getStrapiDefaultParams(locale) });
    return clubChampionshipRankingPage.data.seo as SeoType | null;
  } catch (error) {
    console.error('Club Championship Rankings Page SEO not fetched!', error);
    return null;
  }
}
