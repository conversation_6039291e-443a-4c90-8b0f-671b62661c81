import clsx from 'clsx';
import Image from 'next/image';

import { LocalizedLink } from '@/components/LocalizedLink';
import { JsonFieldType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import TeamLogoPlaceholder from '@/ui/assets/images/ewc-placeholder.png';

import { BottomSection } from './BottomSection';
import { Loader } from './Loader';

export interface WinnerCardProps {
  teamName: string;
  teamLogoUrl: string | null;
  gameLogo: MediaType | null;
  gameId: string;
  slug: string | null;
  isLoading: boolean;
  translations: JsonFieldType;
}

export const WinnerCard = ({
  teamName,
  teamLogoUrl,
  gameLogo,
  gameId,
  slug,
  isLoading,
  translations,
}: WinnerCardProps) => {
  return (
    <LocalizedLink
      brazeEventProperties={{
        button_name: `Visit Game Page Link (${slug})`,
        location: 'Competition Winners Grid Block - Winner Card',
      }}
      className={clsx(
        'group h-full w-full max-w-[210px] min-w-[156px]',
        'flex flex-col',
        'rounded-sm bg-white shadow-[0px_16px_32px_0px_#00000014] md:rounded-lg',
        slug && 'cursor-pointer',
      )}
      href={slug ? `/competitions/${slug}` : '#'}
    >
      <div className="flex grow flex-col items-center gap-[9px] px-4 pt-[9px] pb-[3px] md:gap-3 md:px-[23px] md:pt-[17px] md:pb-3">
        {isLoading ? (
          <div className="h-[18px] w-full">
            <Loader uniqueKey={`name-${gameId}`} />
          </div>
        ) : (
          <p className="font-primary text-gold-primary max-w-full truncate text-sm leading-none font-bold uppercase md:text-lg">
            {teamName}
          </p>
        )}
        {isLoading ? (
          <div className="size-[120px]">
            <Loader uniqueKey={`logo-${gameId}`} />
          </div>
        ) : (
          <Image
            alt="team logo"
            className="aspect-square w-full object-contain"
            height={164}
            src={teamLogoUrl ?? TeamLogoPlaceholder}
            width={164}
          />
        )}
      </div>
      <BottomSection gameLogo={gameLogo} isVisitGamePageVisible={!!slug} translations={translations} />
    </LocalizedLink>
  );
};
