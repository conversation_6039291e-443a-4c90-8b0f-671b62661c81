import { VodEnrichedItem } from '@/blocks/VodBlock/VodGrid';
import { CustomStreamSchedule, StreamIntermissionType } from '@/strapi/types/collection/stream';
import { MediaType } from '@/strapi/types/media';

export interface GameStream {
  id: number;
  title: string | null | undefined;
  darkLogo: MediaType | null | undefined;
  lightLogo: MediaType | null | undefined;
  backgroundImage: MediaType | null | undefined;
  isGameStream: boolean;
  isCustomStream: boolean;
  tournamentIds: string[];
  startTime: string | null;
  endTime: string | null;
  tag: string | null;
  customSchedule: CustomStreamSchedule[] | null;
  streamUrl: string | null;
  slug: string | null;
  intermissionText: string | null;
  buttonUrl: string | null;
  buttonText: string | null;
  intermissionType: StreamIntermissionType;
  vods: VodEnrichedItem[];
}
