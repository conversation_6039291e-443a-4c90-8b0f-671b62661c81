import { GRAPH_API_URL } from '@/config/env/client';

import { ImageType, TeamImage } from './types/shared';

const IMAGES_URL = GRAPH_API_URL.replace('graphql', 'images');

/**
 * Constructs a CDN image URL from the GraphQL API image response
 * @param images - Array of image objects from the GraphQL API
 * @param type - The desired image type (e.g., 'logo_transparent_whitebg', 'logo_transparent')
 * @returns The constructed CDN URL or null if image not found
 */
export const constructImageUrl = (images?: TeamImage[], type?: ImageType) => {
  if (!images?.length) return null;

  const getImageUrl = (imageId: string) => `${IMAGES_URL}/${imageId}/400x400`;
  const findImageByType = (imageType: ImageType) => images.find((img) => img.type === imageType);

  if (!type) return getImageUrl(images[0].id);

  const typeImage = findImageByType(type);
  if (typeImage?.id) return getImageUrl(typeImage.id);

  const fallbackImage = findImageByType('logo_transparent');
  if (fallbackImage?.id) return getImageUrl(fallbackImage.id);

  return null;
};
