import { ButtonType } from '@/strapi/types/shared';
import { Button } from '@/ui/components/Button';

interface Props {
  buttons?: ButtonType[];
  isEmbedded?: boolean;
}

export function ButtonsSection({ buttons, isEmbedded }: Props) {
  if (!buttons || buttons.length === 0) {
    return <div className="h-[54px] w-full animate-pulse rounded bg-gray-200" />;
  }

  return buttons.map((button, index) => (
    <div
      className="[&>a>button]:min-h-[54px] [&>a>button]:rounded-none! [&>a>button]:px-4 [&>a>button]:py-2.5 [&>a>button]:text-center [&>a>button]:text-[10px] [&>a>button>p]:leading-none"
      key={button?.id ?? `placeholder-${index}`}
    >
      <Button
        {...button}
        brazeEventProperties={{
          button_name: `Button (${button.text})`,
          location: `Desktop Sidebar Navigation`,
        }}
        isEmbedded={isEmbedded}
        isFullWidth
      />
    </div>
  ));
}
