import clsx from 'clsx';

interface MetricSectionProps {
  metric: string;
  description: string;
  labelAlignment?: 'top' | 'bottom';
}

export const MetricSection = ({ metric, description, labelAlignment = 'bottom' }: MetricSectionProps) => (
  <div className="flex items-center md:px-8 lg:flex-1">
    <div className={clsx('flex flex-col gap-2 max-md:items-center', labelAlignment === 'top' && 'lg:flex-col-reverse')}>
      <p
        className={clsx(
          'font-primary text-radial-gold leading-[1] font-bold',
          'tracking-[-4%] whitespace-nowrap uppercase',
          'text-[21px] md:text-[32px] lg:text-[48px] xl:text-[64px]',
        )}
      >
        {metric}
      </p>
      <p className="text-xs leading-normal font-normal capitalize lg:text-sm">{description}</p>
    </div>
  </div>
);
