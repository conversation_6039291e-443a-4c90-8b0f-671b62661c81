'use client';

import clsx from 'clsx';
import { format, Locale } from 'date-fns';
import Image from 'next/image';
import { RefObject, useRef, useState } from 'react';
import { useOnClickOutside } from 'usehooks-ts';

import { GameStream } from '@/blocks/hero/StreamHero/shared/types/GameStream';
import { Contestant, MapResult } from '@/blocks/hero/StreamHero/shared/types/StreamHeroMatch';
import MatchStatusPill from '@/blocks/shared/MatchStatusPill';
import { useGameStatsData } from '@/services/graphql/hooks/gameData';
import { MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';
import EWC from '@/ui/assets/icons/logos/ewc-acronym.svg';
import { Button } from '@/ui/components/Button';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { PlayerStatsTable } from './PlayerStatsTable';

interface Props {
  onClose: () => void;
  mapResults: MapResult[];
  status: LocalMatchStatus;
  startTime: string;
  contestants: Contestant[];
  translations?: JsonFieldType;
  selectedGameStream: GameStream | null;
  locale: Locale;
  slug: string | null;
  tournamentId: string;
}

export const statusMap = {
  [LocalMatchStatus.COMPLETED]: MatchSeriesStatus.FINISHED,
  [LocalMatchStatus.UPCOMING]: MatchSeriesStatus.OPEN,
  [LocalMatchStatus.LIVE]: MatchSeriesStatus.LIVE,
};

export const StatsModal = ({
  onClose,
  mapResults,
  status,
  startTime,
  contestants,
  translations,
  locale,
  selectedGameStream,
  slug,
  tournamentId,
}: Props) => {
  const [selectedMatch, setSelectedMatch] = useState<string | null>(() => {
    const liveMatch = mapResults.find((m) => m.status === LocalMatchStatus.LIVE);
    if (liveMatch) return liveMatch.matchId;
    return mapResults.length > 0 ? mapResults[0].matchId : null;
  });

  const ref = useRef<HTMLDivElement>(null);

  const isSelectedMatchCompleted =
    mapResults.find((m) => m.matchId === selectedMatch)?.status === LocalMatchStatus.COMPLETED;

  const { data } = useGameStatsData(selectedMatch, tournamentId, { isSubscriptionDisabled: isSelectedMatchCompleted });
  useOnClickOutside(ref as RefObject<HTMLElement>, onClose);

  return (
    <div className="fixed inset-0 z-500 flex items-center justify-center p-8 max-md:p-4">
      <div aria-hidden="true" className="absolute inset-0 bg-black/80" />
      <div
        className={clsx(
          'bg-white-dirty relative max-h-[800px] w-[900px] overflow-auto rounded-2xl p-5 shadow-sm max-md:h-[80vh] max-md:p-2',
          'flex flex-col gap-4',
        )}
        ref={ref}
      >
        <div className="flex justify-between">
          <div className="flex flex-col gap-2">
            <MatchStatusPill status={statusMap[status]} />
            <span className="font-riforma text-sm font-bold uppercase opacity-80">
              {format(new Date(startTime), 'eee, MMM d - h:mm a', { locale })}
            </span>
          </div>
          <Button
            brazeEventProperties={{
              button_name: 'Close Button',
              location: 'Live State Hero - Stats Modal',
            }}
            text={translations?.['close'] ?? 'Close'}
            variant="secondary"
            onClick={onClose}
          />
        </div>
        <div className="flex items-stretch gap-2.5">
          <div className="flex min-h-[75px] flex-1/2 items-center justify-end rounded-md bg-white p-4 shadow-md">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-4 max-md:flex-col-reverse max-md:gap-1">
                <span className="text-dark-default font-riforma text-[16px] font-bold max-md:text-center max-md:text-[10px]">
                  {contestants[0]?.name}
                </span>
                {contestants[0]?.logoUrl ? (
                  <Image alt="" className="object-cover" height={40} src={contestants[0]?.logoUrl ?? ''} width={40} />
                ) : (
                  <EWC className="size-10" />
                )}
              </div>
              <div className="bg-white-dirty flex h-full min-w-[48px] items-center justify-center rounded-[3px] p-2">
                <span className="text-dark-default text-center text-2xl font-bold">{contestants[0].score ?? 0}</span>
              </div>
            </div>
          </div>
          <div className="flex min-h-[75px] flex-1/2 items-center justify-start rounded-md bg-white p-4 shadow-md">
            <div className="flex flex-row-reverse items-center gap-4">
              <div className="flex flex-row-reverse items-center gap-4 max-md:flex-col-reverse max-md:gap-1">
                <span className="text-dark-default font-riforma text-[16px] font-bold max-md:text-center max-md:text-[10px]">
                  {contestants[1]?.name}
                </span>
                {contestants[1]?.logoUrl ? (
                  <Image alt="" className="object-cover" height={40} src={contestants[1]?.logoUrl ?? ''} width={40} />
                ) : (
                  <EWC className="size-10" />
                )}
              </div>
              <div className="bg-white-dirty flex h-full min-w-[48px] items-center justify-center rounded-[3px] p-2">
                <span className="text-dark-default text-center text-2xl font-bold">{contestants[1]?.score ?? 0}</span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-wrap items-stretch gap-2 max-md:flex-col">
          {mapResults
            .sort((a, b) => (a.sequence ?? 0) - (b.sequence ?? 0))
            .map((m, i) => (
              <div
                className={clsx(
                  'bg-gray-easy flex flex-1 cursor-pointer items-center justify-between rounded-md p-1.5 pl-3',
                  {
                    'bg-white!': m.matchId === selectedMatch,
                    'shadow-md': m.matchId === selectedMatch,
                  },
                )}
                key={i}
                onClick={() => setSelectedMatch(m.matchId)}
              >
                <div className="flex flex-col">
                  <span className="text-gray-dark text-[9px] font-extrabold">{translations?.['map'] ?? 'Map'}</span>
                  <span className="text-sm font-extrabold text-black">{m.name ?? '-'}</span>
                </div>
                <div className="flex items-center gap-1">
                  {m.status === LocalMatchStatus.LIVE ? (
                    <MatchStatusPill status={statusMap[m.status]} />
                  ) : (
                    <>
                      {m.results.map((result, i) => {
                        const contestant = contestants.find((c) => c.id === result.contestantId);
                        return (
                          <div
                            className={clsx(
                              'bg-white-dirty flex aspect-square h-full min-w-[34px] flex-col items-center justify-center rounded-sm px-0.5 py-1',
                              {
                                'bg-dark-default color-white': result.isWinner,
                              },
                            )}
                            key={i}
                          >
                            <span className="font-riforma text-center text-sm font-bold">
                              {m.status === LocalMatchStatus.UPCOMING ? '0' : (result.score ?? 0)}
                            </span>
                            {contestant?.logoUrl ? (
                              <Image alt="" className="object-cover" height={14} src={contestant.logoUrl} width={14} />
                            ) : (
                              <EWC className="mb-1 size-5" />
                            )}
                          </div>
                        );
                      })}
                    </>
                  )}
                </div>
              </div>
            ))}
        </div>
        <div className="flex w-full flex-col gap-3 rounded-2xl bg-white p-4 max-md:p-1">
          {contestants.map((c) => {
            return (
              <PlayerStatsTable
                data={data}
                key={c.id}
                logoUrl={c.logoUrl}
                members={c.members}
                selectedGameStream={selectedGameStream}
                slug={slug}
                teamName={c.name}
                translations={translations}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};
