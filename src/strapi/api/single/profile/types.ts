import { SocialProvider } from '@/services/auth/types';
import { StrapiBase } from '@/strapi/types/base';
import { SeoType } from '@/strapi/types/shared/seo';

export interface ProfilePageData extends StrapiBase {
  title: string | null;
  subtitle: string | null;
  signoutButtonText: string | null;
  basicInfoToggleText: string | null;
  additionalInfoToggleText: string | null;
  linkAccountsToggleText: string | null;
  notificationsToggleText: string | null;
  basicInfo: BasicInfoSectionData | null;
  deleteAccount: DeleteAccountSectionData | null;
  additionalInfo: AdditionalInfoSectionData | null;
  linkedAccounts: LinkedAccountsSectionData | null;
  notifications: NotificationsSectionData | null;
  seo: SeoType | null;
}

interface SectionBase {
  id: number;
  title: string | null;
  subtitle: string | null;
}

interface BasicInfoSectionData extends SectionBase {
  emailFieldLabel: string | null;
  usernameFieldLabel: string | null;
  nameFieldLabel: string | null;
  surnameFieldLabel: string | null;
  birthdateFieldLabel: string | null;
  genderFieldLabel: string | null;
  saveButtonText: string | null;
  changePasswordButtonText: string | null;
}

interface DeleteAccountSectionData extends SectionBase {
  subtitleDeleteCtaText: string | null;
  body: string | null;
  alertTitle: string | null;
  alertBody: string | null;
  deleteInputInfoText: string | null;
  deleteButtonText: string | null;
  deleteConfirmationValue: string | null;
}

interface AdditionalInfoSectionData extends SectionBase {
  address1FieldLabel: string | null;
  address2FieldLabel: string | null;
  countryFieldLabel: string | null;
  zipCodeFieldLabel: string | null;
  cityFieldLabel: string | null;
  stateFieldLabel: string | null;
  saveButtonText: string | null;
  alertBody: string | null;
  favoriteGamesFieldLabel: string | null;
  favoriteClubsFieldLabel: string | null;
  favoritePlayersFieldLabel: string | null;
  favoriteGamesFieldPlaceholder: string | null;
  favoriteClubsFieldPlaceholder: string | null;
  favoritePlayersFieldPlaceholder: string | null;
}

interface LinkedAccountsSectionData extends SectionBase {
  linkedAccountsListTitle: string | null;
  availableAccountsListTitle: string | null;
  footerText: string | null;
  buttons: AccountButton[];
}

export interface AccountButton {
  id: number;
  linkedAccountSubtitle: string | null;
  availableAccountTitle: string | null;
  availableAccountSubtitle: string | null;
  type: SocialProvider | null;
}

interface NotificationsSectionData extends SectionBase {
  channelSectionTitle: string | null;
  channelSectionSubtitle: string | null;
}
