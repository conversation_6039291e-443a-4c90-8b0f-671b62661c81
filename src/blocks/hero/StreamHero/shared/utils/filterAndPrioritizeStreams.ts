import { StreamType } from '@/strapi/types/collection/stream';

export function filterAndPrioritizeStreams(duplicatedStreams: StreamType[] | null | undefined): StreamType[] | null {
  if (!duplicatedStreams) return null;

  const gameStreamMap = new Map<string, StreamType>();
  for (const stream of duplicatedStreams) {
    const gameKey = stream.game?.slug || `custom-${stream.id}`;

    if (!gameStreamMap.has(gameKey)) {
      gameStreamMap.set(gameKey, stream);
      continue;
    }

    const { startTime: streamStartTime, endTime: streamEndTime } = getStartEndTime(stream.startTime, stream.endTime);
    const { isLive: isStreamLive, isUpcoming: isStreamUpcoming } = getStatusBasedOnTime(streamStartTime, streamEndTime);

    const existingStream = gameStreamMap.get(gameKey)!;

    const { startTime: existingStartTime, endTime: existingEndTime } = getStartEndTime(
      existingStream.startTime,
      existingStream.endTime,
    );
    const { isLive: isExistingLive, isUpcoming: isExistingUpcoming } = getStatusBasedOnTime(
      existingStartTime,
      existingEndTime,
    );

    // Priority rules:
    // 1. Live streams take precedence
    // 2. If no live stream, take upcoming over completed
    // 3. For same status, take the one starting sooner
    if (isStreamLive && !isExistingLive) {
      // Replace non-live with live
      gameStreamMap.set(gameKey, stream);
    } else if (!isExistingLive && !isStreamLive) {
      // Neither is live, prefer upcoming over completed
      if (isStreamUpcoming && !isExistingUpcoming) {
        gameStreamMap.set(gameKey, stream);
      } else if (isStreamUpcoming === isExistingUpcoming) {
        // Both upcoming or both completed, take the one starting sooner
        if (streamStartTime < existingStartTime) {
          gameStreamMap.set(gameKey, stream);
        }
      }
    }
  }

  return Array.from(gameStreamMap.values());
}

function getStartEndTime(startDateTime: string | null, endDateTime: string | null) {
  return {
    startTime: startDateTime ? new Date(startDateTime).getTime() : 0,
    endTime: endDateTime ? new Date(endDateTime).getTime() : Infinity,
  };
}

function getStatusBasedOnTime(startTime: number, endTime: number) {
  const now = Date.now();

  return {
    isLive: startTime <= now && endTime > now,
    isUpcoming: startTime > now,
  };
}
