import { Locale } from '@/hooks/i18n/const';
import { DynamicZoneBlock } from '@/strapi/types/block';
import { SeoType } from '@/strapi/types/shared/seo';

import { StrapiBase } from '../../types/base';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { fetchGames } from '../collection/game';
import { assembleBlocksWithAdditionalData } from '../utils/block';

interface CompetitionsPageData extends StrapiBase {
  title: string | null;
  subtitle: string | null;
  year: string | null;
  blocks: DynamicZoneBlock[];
}

export async function getCompetitionsPageData(locale: Locale) {
  try {
    const competitionsPage = await strapiClient.single('competitions-page').find({
      populate: { blocks: getDynamicZoneBlocks() },
      ...getStrapiDefaultParams(locale),
    });

    const pageData = competitionsPage.data as CompetitionsPageData;
    if (!pageData) {
      return null;
    }

    await assembleBlocksWithAdditionalData(competitionsPage.data.blocks, locale);
    const gamesData = await fetchGames(locale, pageData.year);

    return { pageData, gamesData };
  } catch (error) {
    console.error('Competitions Page data not fetched!', error);
    throw new Error('Competitions Page data not fetched!');
  }
}

export async function getCompetitionsPageSeo(locale: Locale) {
  try {
    const competitionsPage = await strapiClient
      .single('competitions-page')
      .find({ populate: 'seo', ...getStrapiDefaultParams(locale) });
    return competitionsPage.data.seo as SeoType | null;
  } catch (error) {
    console.error('Competitions Page SEO not fetched!', error);
    return null;
  }
}
