import { IntermissionProps } from '../../Intermission';
import { RowContainer } from '../RowContainer';
import { MatchCard } from './MatchCard';

type Props = Omit<IntermissionProps, 'stream' | 'isLoading'>;

export const UpcomingMatchesRow = ({ matches, translations, apiTranslations }: Props) => {
  if (matches.length === 0) {
    return null;
  }

  const firstUpcomingMatchTournamentName = matches[0].tournamentName.replace('Esports World Cup - 2025 -', '');
  return (
    <RowContainer
      subtitle={translations.upcomingMatches ?? 'upcoming matches'}
      title={firstUpcomingMatchTournamentName}
    >
      {matches.map((m) => (
        <div className="max-2xl:nth-4:hidden" key={m.id}>
          <MatchCard {...m} {...{ translations, apiTranslations }} />
        </div>
      ))}
    </RowContainer>
  );
};
