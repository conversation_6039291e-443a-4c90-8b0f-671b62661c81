import clsx from 'clsx';

import { NavigationProps } from './Navigation';

export const MainNavigation = ({ items, onSelect, selectedItemId }: Omit<NavigationProps, 'type'>) => {
  return (
    <div className="flex gap-2 overflow-x-auto">
      {items.map((i) => (
        <button
          className={clsx(
            'font-primary text-dark-default rounded-t-lg px-4 py-2 text-base leading-[1.1] font-bold whitespace-nowrap transition-colors md:rounded-t-2xl md:px-8 md:py-[14px] md:text-xl',
            i.id === selectedItemId ? 'bg-white' : 'cursor-pointer',
          )}
          key={i.id}
          onClick={i.id !== selectedItemId ? () => onSelect(i.id) : undefined}
        >
          {i.name}
        </button>
      ))}
    </div>
  );
};
