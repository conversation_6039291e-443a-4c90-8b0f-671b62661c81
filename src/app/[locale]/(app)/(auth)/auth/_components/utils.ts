import { AuthSession, decodeJWT } from 'aws-amplify/auth';

import { TTL_1_HOUR } from '@/config/cache';
import { fetchAuthSession } from '@/services/auth/cognito';
import { AuthMethod } from '@/services/auth/types';
import { changeBrazeUser } from '@/services/braze';
import { constructUrl } from '@/utils/browser';

import { OauthQueryParams } from '../../_utils';

export function getInvalidQueryParams(params: OauthQueryParams) {
  const invalidParams = [];

  for (const key in params) {
    if (!(params as any)[key]) {
      invalidParams.push(key);
    }
  }

  return invalidParams;
}

interface OauthTokens {
  access_token: string;
  id_token: string;
}

export function getTokensFromSession(session: AuthSession) {
  const { tokens } = session;

  const access_token = tokens?.accessToken.toString();
  if (!access_token) {
    throw new Error('Error: access_token not available in user session!');
  }

  const id_token = tokens?.idToken?.toString();
  if (!id_token) {
    throw new Error('Error: id_token not available in user session!');
  }

  return { access_token, id_token } as OauthTokens;
}

export function constructRedirectUrl(params: OauthQueryParams, tokens: OauthTokens) {
  const { redirect_uri, state } = params;

  const {
    payload: { exp, iat },
  } = decodeJWT(tokens.access_token);

  const expires_in = exp && iat ? exp - iat : TTL_1_HOUR;
  const url = constructUrl(redirect_uri, {
    ...tokens,
    state,
    token_type: 'Bearer',
    expires_in: expires_in.toString(),
  });

  const redirectUrl = url.toString().replace('?', '#');
  return redirectUrl;
}

export async function redirectWithTokens(params: OauthQueryParams) {
  const session = await fetchAuthSession({ forceRefresh: true });
  const tokens = getTokensFromSession(session);

  await changeBrazeUser(session.tokens?.idToken?.payload.sub as string, AuthMethod.USERNAME_PASSWORD);

  const redirectUrl = constructRedirectUrl(params, tokens);
  window.location.assign(redirectUrl);
}
