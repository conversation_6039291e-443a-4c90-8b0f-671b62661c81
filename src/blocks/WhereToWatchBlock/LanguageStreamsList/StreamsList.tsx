import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';
import { StreamProviderType } from '@/strapi/types/collection/streamProvider';
import { MediaType } from '@/strapi/types/media';
import PlaceholderLogo from '@/ui/assets/images/ewc-placeholder-white.png';

interface Props {
  streams: string[];
  providers: StreamProviderType[];
}

export const StreamsList = ({ streams, providers }: Props) => {
  return (
    <div className="rounded-b-lg bg-white p-3 shadow-md md:px-6 md:py-4">
      <div className="-mx-3 flex gap-2 overflow-x-auto px-3 md:-mx-6 md:px-6">
        {streams.map((s) => (
          <StreamBox key={s} url={s} {...resolveStreamFromProvider(s, providers)} />
        ))}
      </div>
    </div>
  );
};

function resolveStreamFromProvider(url: string, providers: StreamProviderType[]) {
  return providers.find((p) => p.regex && new RegExp(p.regex).test(url));
}

interface StreamBoxProps {
  url: string;
  title?: string | null;
  bgColor?: string | null;
  icon?: MediaType | null;
}

const StreamBox = ({ url, title, bgColor, icon }: StreamBoxProps) => {
  const parsedUrl = new URL(url);
  const resolvedTitle = title ?? parsedUrl.hostname.replace('www.', '');
  return (
    <a
      className="flex cursor-pointer flex-col gap-2 rounded-sm p-0.5 pb-[10.25px]"
      href={url}
      style={{ backgroundColor: bgColor ?? '#151515' }}
      target="_blank"
    >
      <div className="relative flex items-center justify-center p-[17px]">
        <div className="bg-dark-default absolute inset-0 size-full rounded-sm opacity-30" />
        <div className="relative size-9">
          {icon ? (
            <StrapiImage alt={`${resolvedTitle} stream logo`} className="size-full object-contain" image={icon} />
          ) : (
            <Image alt={`${resolvedTitle} stream logo`} className="size-full object-contain" src={PlaceholderLogo} />
          )}
        </div>
      </div>
      <p className="font-primary text-center text-[10px] leading-[1.1] font-bold text-white">{resolvedTitle}</p>
    </a>
  );
};
