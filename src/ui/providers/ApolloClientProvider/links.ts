import { ApolloLink, HttpLink, split } from '@apollo/client';
import { createPersistedQueryLink } from '@apollo/client/link/persisted-queries';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { getMainDefinition } from '@apollo/client/utilities';
import { generatePersistedQueryIdsFromManifest } from '@apollo/persisted-query-lists';
import { createClient } from 'graphql-ws';

import { GRAPH_API_URL } from '@/config/env/client';

const WS_URL = `${GRAPH_API_URL.replace('https', 'wss')}/ws`;

const removeExtensionsLink = new ApolloLink((operation, forward) => {
  if (operation.extensions) {
    (operation.extensions as unknown) = undefined;
  }
  return forward(operation);
});

const wsLink = removeExtensionsLink.concat(new GraphQLWsLink(createClient({ url: WS_URL })));

const persistedQueryLink = createPersistedQueryLink(
  generatePersistedQueryIdsFromManifest({
    loadManifest: () => import('./persistedQueries/persisted-query-manifest.json'),
  }),
);

const persistedQueryLinkWithUrl = persistedQueryLink.concat(
  new HttpLink({ uri: GRAPH_API_URL, useGETForQueries: true, headers: { 'Content-Type': 'text/plain' } }),
);

export const splitLink = split(
  ({ query }) => {
    const definition = getMainDefinition(query);
    return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
  },
  wsLink,
  persistedQueryLinkWithUrl,
);
