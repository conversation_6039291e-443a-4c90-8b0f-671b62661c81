'use client';

import { useState } from 'react';

import { ToggleGroup } from '@/components/ToggleGroup';
import { useCurrentUser } from '@/services/auth/hooks';
import { ProfilePageData } from '@/strapi/api/single/profile/types';
import { JsonFieldType } from '@/strapi/types/helper';

import { AdditionalInfoSection } from './AdditionalInfoSection';
import { BasicDataSection } from './BasicDataSection';
import { DeleteAccountSection } from './DeleteAccountSection';
import { LinkedAccountsSection } from './LinkedAccountsSection';
import { NotificationsSection } from './NotificationsSection';

enum SectionFilter {
  BASIC_INFO = 'basic_info',
  ADDITIONAL_INFO = 'additional_info',
  LINK_ACCOUNTS = 'link_accounts',
  NOTIFICATIONS = 'notifications',
}

type Props = ProfilePageData & { translations?: JsonFieldType | null; games: string[]; clubs: string[] };

export const ProfileSections = (props: Props) => {
  const { user } = useCurrentUser();
  const [selectedFilter, setSelectedFilter] = useState<SectionFilter>(SectionFilter.BASIC_INFO);

  if (!user) {
    return null;
  }

  const Section = sectionMap[selectedFilter];
  const { basicInfoToggleText, additionalInfoToggleText, linkAccountsToggleText, notificationsToggleText } = props;
  return (
    <div className="flex max-w-full flex-col gap-4 md:gap-8">
      <div className="max-w-full overflow-x-auto px-4 md:self-start lg:px-8">
        <ToggleGroup
          filters={[
            { value: SectionFilter.BASIC_INFO, label: basicInfoToggleText ?? 'Basic info' },
            { value: SectionFilter.ADDITIONAL_INFO, label: additionalInfoToggleText ?? 'Additional info' },
            { value: SectionFilter.LINK_ACCOUNTS, label: linkAccountsToggleText ?? 'Link/Unlink accounts' },
            { value: SectionFilter.NOTIFICATIONS, label: notificationsToggleText ?? 'Notifications' },
          ]}
          selectedFilter={selectedFilter}
          onFilterSelect={(f) => setSelectedFilter(f as SectionFilter)}
        />
      </div>
      <div className="px-4 lg:px-8">
        <Section user={user} {...props} />
      </div>
      {selectedFilter === SectionFilter.BASIC_INFO && (
        <div className="px-4 lg:px-8">
          <DeleteAccountSection {...props} />
        </div>
      )}
    </div>
  );
};

const sectionMap = {
  [SectionFilter.BASIC_INFO]: BasicDataSection,
  [SectionFilter.ADDITIONAL_INFO]: AdditionalInfoSection,
  [SectionFilter.LINK_ACCOUNTS]: LinkedAccountsSection,
  [SectionFilter.NOTIFICATIONS]: NotificationsSection,
};
