'use client';

import { FormProvider } from 'react-hook-form';
import { MdOutlineWarningAmber } from 'react-icons/md';

import { FormCountrySelect } from '@/components/form/FormCountrySelect';
import { FormMultiSelect } from '@/components/form/FormMultiSelect';
import { FormTextInput } from '@/components/form/FormTextInput';
import { useUpdateUser } from '@/services/auth/hooks';
import { User } from '@/services/auth/types';
import { ProfilePageData } from '@/strapi/api/single/profile/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { Alert } from '@/ui/components/Alert';
import { Button } from '@/ui/components/Button';
import { showErrorToast } from '@/ui/components/Toast';
import { containsProfanities, getProfaneAttributes } from '@/utils/profanity';

import { ProfilePageSectionContainer } from '../ProfilePageSectionContainer';
import { AdditionalProfileFormValues, useAdditionalProfileForm } from './hooks';

interface Props extends ProfilePageData {
  user: User;
  games: string[];
  clubs: string[];
  translations?: JsonFieldType | null;
}

export const AdditionalInfoSection = ({ user, additionalInfo, games, clubs, translations }: Props) => {
  const formMethods = useAdditionalProfileForm(user);
  const { handleSubmit, formState, reset, setError } = formMethods;

  const { mutateAsync } = useUpdateUser();

  const profanityErrorMessage =
    translations?.['profanityErrorMessage'] ??
    'This input contains inappropriate language. Please remove any profanity.';

  async function validateAndUpdateUser(values: AdditionalProfileFormValues) {
    const profaneAttributes = getProfaneAttributes(values);
    const isGamesFieldsProfane = containsProfanities(values.favoriteGames);
    const isClubsFieldProfane = containsProfanities(values.favoriteClubs);
    const isPlayersFieldProfane = containsProfanities(values.favoritePlayers);

    if (profaneAttributes.length === 0 && !isGamesFieldsProfane && !isClubsFieldProfane && !isPlayersFieldProfane) {
      return updateUser(values);
    }

    for (const attr of profaneAttributes) {
      setError(attr, { message: profanityErrorMessage });
    }

    if (isGamesFieldsProfane) {
      setError('favoriteGames', { message: profanityErrorMessage });
    }
    if (isClubsFieldProfane) {
      setError('favoriteClubs', { message: profanityErrorMessage });
    }
    if (isPlayersFieldProfane) {
      setError('favoritePlayers', { message: profanityErrorMessage });
    }
  }

  async function updateUser(values: AdditionalProfileFormValues) {
    const { address1, address2, ...rest } = values;
    const address = address2 ? `${address1}\n${address2}` : address1;
    try {
      await mutateAsync({ address, ...rest });
      reset(values);
    } catch (error: any) {
      showErrorToast({ title: 'User update error!', description: error.message });
    }
  }

  const { isDirty, isValid, isSubmitting } = formState;

  const gameOptions = games.map((g) => ({ label: g, value: g }));
  const clubOptions = clubs.map((c) => ({ label: c, value: c }));
  return (
    <FormProvider {...formMethods}>
      <div className="flex flex-col gap-3">
        {additionalInfo?.alertBody && (
          <Alert
            iconLeft={<MdOutlineWarningAmber className="size-full" />}
            text={additionalInfo.alertBody}
            type="warning"
          />
        )}
        <form onSubmit={handleSubmit(validateAndUpdateUser)}>
          <ProfilePageSectionContainer subtitle={additionalInfo?.subtitle} title={additionalInfo?.title}>
            <div className="grid gap-2 md:grid-cols-2 md:gap-x-4 md:gap-y-4 lg:gap-x-8">
              <FormTextInput label={additionalInfo?.address1FieldLabel ?? 'Address Line 1'} name="address1" />
              <FormTextInput label={additionalInfo?.address2FieldLabel ?? 'Address Line 2'} name="address2" />
              <FormCountrySelect label={additionalInfo?.countryFieldLabel ?? 'Country'} />
              <FormTextInput label={additionalInfo?.zipCodeFieldLabel ?? 'Zip Code'} name="zipcode" />
              <FormTextInput label={additionalInfo?.cityFieldLabel ?? 'City'} name="city" />
              <FormTextInput label={additionalInfo?.stateFieldLabel ?? 'State'} name="state" />
            </div>
            <FormMultiSelect
              label={additionalInfo?.favoriteGamesFieldLabel ?? 'Favorite Game/s'}
              name="favoriteGames"
              options={gameOptions}
              placeholder={additionalInfo?.favoriteGamesFieldPlaceholder ?? 'Select favorite games'}
            />
            <FormMultiSelect
              label={additionalInfo?.favoriteClubsFieldLabel ?? 'Favorite Club/s'}
              name="favoriteClubs"
              options={clubOptions}
              placeholder={additionalInfo?.favoriteClubsFieldPlaceholder ?? 'Select favorite clubs'}
            />
            <FormMultiSelect
              label={additionalInfo?.favoritePlayersFieldLabel ?? 'Favorite Player/s'}
              name="favoritePlayers"
              options={[]}
              placeholder={additionalInfo?.favoritePlayersFieldPlaceholder ?? 'Select favorite clubs'}
            />
            <div className="bg-gray h-px" />
            <div className="md:w-fit">
              <Button
                brazeEventProperties={{
                  button_name: `Save Changes Button`,
                  location: `Additional Info Section - Update Additional Info Form`,
                }}
                isDisabled={!isDirty || !isValid || isSubmitting}
                isFullWidth
                text={additionalInfo?.saveButtonText ?? 'save'}
                type="submit"
              />
            </div>
          </ProfilePageSectionContainer>
        </form>
      </div>
    </FormProvider>
  );
};
