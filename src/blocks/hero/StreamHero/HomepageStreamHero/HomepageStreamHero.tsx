'use client';

import { useCallback, useMemo } from 'react';

import { VideoEmbed } from '@/components/VideoEmbed';
import { useTodaysCcRankings } from '@/hooks/tournaments/useTodaysCcRankings';
import { JsonFieldType } from '@/strapi/types/helper';
import { HomepageStreamHeroType } from '@/strapi/types/hero';
import SpinnerIcon from '@/ui/assets/icons/spinner.svg';

import { useCountdown } from '../shared/hooks/useCountdown';
import { useSelectedMatch } from '../shared/hooks/useSelectedMatch';
import { Intermission } from '../shared/Intermission';
import { GameStream } from '../shared/types/GameStream';
import { filterAndPrioritizeStreams } from '../shared/utils/filterAndPrioritizeStreams';
import { GamePicker } from './GamePicker/GamePicker';
import { useMatchesData } from './hooks/useMatchesData';
import { useSelectedGameStream } from './hooks/useSelectedGameStream';
import { Sidebar } from './Sidebar';
import { StreamDetails } from './StreamDetails';

type Props = HomepageStreamHeroType & {
  translations: JsonFieldType;
  apiTranslations: JsonFieldType;
};

export function HomepageStreamHero({ games, translations, apiTranslations, weekStream: duplicatedStreams }: Props) {
  const streams = useMemo(() => filterAndPrioritizeStreams(duplicatedStreams?.streams), [duplicatedStreams]);
  const { selectedGameStream, handleGameStreamSelect } = useSelectedGameStream(streams);

  const { todaysCCRankings } = useTodaysCcRankings(games);

  const { matches, loading, errorMessage } = useMatchesData(selectedGameStream?.tournamentIds ?? []);
  const { selectedMatchId, setSelectedMatchId } = useSelectedMatch(matches, selectedGameStream);

  const selectedMatch = matches.find((m) => m.id === selectedMatchId);
  const selectedStreamUrl = selectedMatch?.stream?.url ?? '';

  const { isUpcoming } = useCountdown(selectedGameStream?.startTime ?? null, selectedGameStream?.endTime ?? null);

  const handleGameSelect = useCallback(
    (game: GameStream) => {
      setSelectedMatchId('');
      handleGameStreamSelect(game);
    },
    [handleGameStreamSelect, setSelectedMatchId],
  );

  return (
    <section className="w-full">
      <div className="flex max-lg:flex-col lg:h-screen">
        <div className="flex min-w-0 flex-3/4 flex-col">
          <GamePicker
            selectedGameStream={selectedGameStream}
            streams={streams}
            translations={translations}
            onGameSelect={handleGameSelect}
          />
          <div className="bg-dark-default relative flex w-full items-center justify-center max-lg:aspect-video lg:h-full">
            {!selectedMatchId && selectedGameStream && isUpcoming ? (
              <Intermission
                apiTranslations={apiTranslations}
                isLoading={loading}
                matches={matches}
                stream={selectedGameStream}
                translations={translations}
              />
            ) : (
              <VideoEmbed
                autoplay
                controls
                fallback={<SpinnerIcon className="size-12 text-white" />}
                url={
                  selectedGameStream?.isCustomStream
                    ? isUpcoming
                      ? null
                      : selectedGameStream.streamUrl
                    : selectedStreamUrl
                }
              />
            )}
          </div>
          <StreamDetails
            apiTranslations={apiTranslations}
            selectedGameStream={selectedGameStream}
            selectedMatchSeries={selectedMatch}
            translations={translations}
          />
        </div>
        <Sidebar
          apiTranslations={apiTranslations}
          customSchedule={selectedGameStream?.customSchedule}
          customStreamTitle={selectedGameStream?.title ?? null}
          error={errorMessage}
          isCustomStream={!!selectedGameStream?.isCustomStream}
          isLoading={loading}
          matches={matches}
          selectedGameStream={selectedGameStream}
          selectedMatchId={selectedMatchId}
          todaysCCRankings={todaysCCRankings}
          translations={translations}
          onMatchSelect={setSelectedMatchId}
        />
      </div>
    </section>
  );
}
