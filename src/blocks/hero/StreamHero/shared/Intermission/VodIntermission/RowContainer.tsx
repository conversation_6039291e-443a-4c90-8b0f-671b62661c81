type Props = React.PropsWithChildren<{
  title: string | null;
  subtitle: string | null;
}>;

export const RowContainer = ({ title, subtitle, children }: Props) => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col gap-1.5 px-4 md:px-8 lg:px-20 2xl:px-30">
        {subtitle && <p className="text-tag text-gray uppercase">{subtitle}</p>}
        {title && <p className="text-h3 text-white">{title}</p>}
      </div>
      <div className="hide-scrollbar flex gap-4 px-4 max-md:overflow-x-auto md:px-8 lg:px-20 2xl:px-30">{children}</div>
    </div>
  );
};
