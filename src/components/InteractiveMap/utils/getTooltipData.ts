import { mapBase } from '@/components/InteractiveMap/venues';
import { VenueMapConfig } from '@/components/InteractiveMap/venues';
import { VenueData } from '@/strapi/types/collection/festival';

export type TooltipData = {
  id: string;
  x: number;
  y: number;
  label: string;
} | null;

export function getTooltipData(
  hoveredVenueId: string | null,
  venueData: VenueData[],
  venueConfigs: VenueMapConfig[],
): TooltipData {
  if (!hoveredVenueId) return null;

  const venue = venueData.find((v) => v.venueId === hoveredVenueId);
  const config = venueConfigs.find((v) => v.id === hoveredVenueId);
  if (!venue || !config) return null;

  const { overlayBox, interactionBox } = config;
  const offsetTop = interactionBox?.offsetTopPercent ?? 0;
  const offsetLeft = interactionBox?.offsetLeftPercent ?? 0;

  const x = ((overlayBox.left + (overlayBox.width * offsetLeft) / 100) / mapBase.width) * 100;
  const y = ((overlayBox.top + (overlayBox.height * offsetTop) / 100) / mapBase.height) * 100;

  return {
    id: hoveredVenueId,
    x,
    y,
    label: venue.name,
  };
}
