import { Prize, TournamentContestant } from '@/services/graphql/types/tournament';
import { constructImageUrl } from '@/services/graphql/utils';

import { StandingContestant } from '../types';

export function getContestantRankMap(contestants: TournamentContestant[]) {
  const contestantsRankMap: Record<number, StandingContestant[]> = {};
  contestants.forEach((c, i) => {
    if (!c?.rank) {
      return;
    }

    if (!contestantsRankMap[c.rank]) {
      contestantsRankMap[c.rank] = [];
    }

    contestantsRankMap[c.rank].push({
      rank: c.rank as number,
      name: c.team?.name ?? 'TBD',
      id: c.team?.id ?? i.toString(),
      logo: constructImageUrl(c.team?.images, 'logo_transparent_whitebg'),
    });
  });

  return contestantsRankMap;
}

export function getPrizeRankMap(prizepool: (Prize | null)[]) {
  const prizeRankMap: Record<number, number> = {};
  prizepool.forEach((p) => {
    if (!p?.rank) {
      return;
    }
    if (!prizeRankMap[p.rank]) {
      prizeRankMap[p.rank] = p.amount as number;
    }
  });

  return prizeRankMap;
}
