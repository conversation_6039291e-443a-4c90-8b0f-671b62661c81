import { StrapiImage } from '@/components/StrapiImage';
import { MediaType } from '@/strapi/types/media';

import { medalsMap, MedalType } from './const';
import { TableProps } from './Table';

export const PointsSection = ({ competitions, translations }: TableProps) => {
  return (
    <div className="px-4 pt-3 pb-[5px] md:px-6">
      <div className="divide-gray-easy flex flex-col justify-between divide-y-[1px]">
        {competitions.map((c) => (
          <GameWithPoints
            gameLogo={c.logoDark}
            key={c.slug}
            medal={c.rank[0].rank === 1 ? 'gold' : c.rank[0].rank === 2 ? 'silver' : 'bronze'}
            points={c.rank[0].prizes.XTS?.toLocaleString() ?? '0'}
            pointsLabel={translations?.['ccPoints']}
          />
        ))}
      </div>
    </div>
  );
};

interface Props {
  gameLogo: MediaType | null;
  medal: MedalType;
  points: string;
  pointsLabel?: string | null;
}

const GameWithPoints = ({ gameLogo, medal, points, pointsLabel }: Props) => {
  const MedalComponent = medalsMap[medal];
  return (
    <div className="flex items-center justify-between gap-2 py-3">
      {gameLogo && <StrapiImage alt="" className="h-8 w-[106px] object-contain" image={gameLogo} />}
      <div className="flex items-center gap-4">
        <div className="font-primary flex items-center gap-4 leading-[1] font-bold whitespace-nowrap uppercase">
          <p className="text-gray-dark text-[10px]">{pointsLabel ?? 'cc points'}</p>
          <p className="text-dark-default text-[20px]">{points}</p>
        </div>
        <MedalComponent className="size-6" />
      </div>
    </div>
  );
};
