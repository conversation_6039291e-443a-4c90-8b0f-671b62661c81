import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { ImageLinkType } from '@/strapi/types/shared';

interface Props {
  logo?: ImageLinkType;
  isEmbedded?: boolean;
}

export const TopLogo = ({ logo, isEmbedded }: Props) => {
  if (!logo) {
    return null;
  }

  return (
    <LocalizedLink
      brazeEventProperties={{
        location: 'Desktop Sidebar Navigation - Top Logo',
        button_name: `Logo Link (${logo.link})`,
      }}
      href={logo.link ?? '/'}
      target={isEmbedded ? '_parent' : undefined}
    >
      <StrapiImage alt={logo.title ?? 'EWC Logo'} className="h-auto w-full px-1.5 pt-8" image={logo.image} />
    </LocalizedLink>
  );
};
