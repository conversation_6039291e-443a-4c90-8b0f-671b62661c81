import Image from 'next/image';

import { constructImageUrl } from '@/services/graphql/utils';
import { Only } from '@/ui/components/Only';

import { TableProps } from './Table';

export const OverviewSection = (props: TableProps) => (
  <Only fallback={<DesktopOverviewSection {...props} />} for="sm">
    <MobileOverviewSection {...props} />
  </Only>
);

const DesktopOverviewSection = ({ winner, pointsWon, moneyWon, translations }: TableProps) => {
  const winnerLogoUrl = constructImageUrl(winner.images);
  const fixedMoneyWon = moneyWon ?? winner.prizes.USD ?? 0;
  const fixedPointsWon = pointsWon ?? winner.prizes.XTS ?? 0;
  return (
    <div className="flex items-center gap-6 px-4 py-11 lg:py-[27.5px]">
      <div className="flex flex-1 flex-col items-center gap-2">
        {winnerLogoUrl && (
          <Image alt="" className="size-[100px]" height={100} quality={25} src={winnerLogoUrl} width={100} />
        )}
        {winner.club.name && <p className="text-subtitle text-dark-default">{winner.club.name}</p>}
      </div>
      <div className="-ms-5 w-px self-stretch bg-[#D9D9D9] lg:ms-5" />
      <div className="flex flex-1 flex-col gap-4">
        <div>
          <div className="flex flex-col gap-1">
            <p className="text-button-default text-radial-gold">{translations?.['firstPlace'] ?? '1st place'}</p>
            <p className="font-primary text-radial-gold text-[64px] leading-[1] font-bold">
              {fixedPointsWon.toLocaleString()}
            </p>
          </div>
          <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
            {translations?.['ccPoints'] ?? 'cc points'}
          </p>
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-button-big text-radial-gold">${fixedMoneyWon.toLocaleString()}</p>
          <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
            {translations?.['won'] ?? 'won'}
          </p>
        </div>
      </div>
    </div>
  );
};

const MobileOverviewSection = ({ winner, moneyWon, pointsWon, translations }: TableProps) => {
  const winnerLogoUrl = constructImageUrl(winner.images);
  const fixedMoneyWon = moneyWon ?? winner.prizes.USD ?? 0;
  const fixedPointsWon = pointsWon ?? winner.prizes.XTS ?? 0;
  return (
    <div className="flex flex-col gap-4 px-6 pt-6 pb-4">
      <div className="flex flex-col items-center gap-1">
        {winnerLogoUrl && (
          <Image alt="" className="size-[100px]" height={100} quality={25} src={winnerLogoUrl} width={100} />
        )}
        {winner.club.name && <p className="text-subtitle text-dark-default">{winner.club.name}</p>}
        <p className="text-button-default text-radial-gold">{translations?.['firstPlace'] ?? '1st place'}</p>
      </div>
      <div className="flex justify-around gap-4">
        <div className="flex flex-col items-center">
          <p className="font-primary text-radial-gold text-2xl leading-[1] font-bold">
            {fixedPointsWon.toLocaleString()}
          </p>
          <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
            {translations?.['ccPoints'] ?? 'cc points'}
          </p>
        </div>
        <div className="flex flex-col items-center">
          <p className="font-primary text-radial-gold text-2xl leading-[1] font-bold">
            ${fixedMoneyWon.toLocaleString()}
          </p>
          <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
            {translations?.['won'] ?? 'won'}
          </p>
        </div>
      </div>
    </div>
  );
};
