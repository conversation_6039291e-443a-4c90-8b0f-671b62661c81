import { Locale } from '@/hooks/i18n/const';
import { VodItemType } from '@/strapi/types/block';

import { getStrapiDefaultParams, strapiClient } from '../client';

export const populateVods = { populate: { youtubeLink: { fields: ['url'] }, games: { fields: ['slug', 'title'] } } };

export async function getVodData(locale: Locale, gameSlug?: string) {
  try {
    const filters = gameSlug
      ? {
          games: {
            slug: {
              $eqi: gameSlug,
            },
          },
        }
      : undefined;

    const vods = await strapiClient
      .collection('vods')
      .find({ filters, sort: ['publishedAt:desc'], ...populateVods, ...getStrapiDefaultParams(locale) });

    return vods.data as unknown as VodItemType[];
  } catch (error) {
    console.error('VOD data not fetched!', error);
  }
}
