import clsx from 'clsx';

import { fetchTournamentsData } from '@/services/graphql/api';
import { constructImageUrl } from '@/services/graphql/utils';
import { GameWithCompetitions } from '@/strapi/api/collection/game';
import { CompetitionWinnersGridBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { WinnerCard } from './WinnerCard';

export const CompetitionWinnersGridBlock = async ({
  games,
  section,
  translations,
}: CompetitionWinnersGridBlockType) => {
  const gameWinnersData = await fetchGameWinnersData(games);

  return (
    <BlockSectionWrapper {...section}>
      <div
        className={clsx(
          'grid w-full',
          'md:grid-cols-[repeat(auto-fit,210px)] md:justify-center md:gap-4',
          'max-md:hide-scrollbar gap-2.5 max-md:grid-flow-col max-md:grid-rows-2 max-md:overflow-x-auto',
          'max-md:-mt-4 max-md:-mb-12 max-md:pt-4 max-md:pb-12',
        )}
      >
        {gameWinnersData.map((d) => (
          <WinnerCard
            gameId={d.game.id}
            gameLogo={d.game.logo}
            isLoading={false}
            key={d.game.id}
            slug={d.game.slug}
            teamLogoUrl={d.winner.logo}
            teamName={d.winner.name}
            translations={translations ?? {}}
          />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};

async function fetchGameWinnersData(games: GameWithCompetitions[]) {
  const tournamentIds = games.flatMap((g) => g.competitionSlugs.map((cs) => cs.competitionId));
  const data = await fetchTournamentsData(tournamentIds);

  const tournaments = data?.tournaments.result ?? [];
  const gameWinnersData = [];

  const sortedGames = games.toSorted((a, b) => (a.title ?? '').localeCompare(b.title ?? ''));
  for (const game of sortedGames) {
    const gameTournamentContestants = tournaments
      .filter((t) => game.competitionSlugs.some((cs) => cs.competitionId === t.id))
      .flatMap((t) => t.contestants);

    const winningContestant = gameTournamentContestants.find((c) => c?.rank === 1);

    gameWinnersData.push({
      game: { id: game.documentId, logo: game.schedulePopupLogo, slug: game.slug },
      winner: {
        name: winningContestant?.team?.club.name ?? 'TBD',
        logo: constructImageUrl(winningContestant?.team?.images, 'logo_transparent_whitebg'),
      },
    });
  }
  return gameWinnersData;
}
