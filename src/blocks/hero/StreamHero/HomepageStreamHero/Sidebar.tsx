import { memo } from 'react';

import { ClubData } from '@/app/[locale]/(app)/club-championship-ranking/_utils/types';
import { CustomStreamSchedule } from '@/strapi/types/collection/stream';
import { JsonFieldType } from '@/strapi/types/helper';

import { MatchesSidebar } from '../shared/Sidebar';
import { GameStream } from '../shared/types/GameStream';
import { StreamHeroMatch } from '../shared/types/StreamHeroMatch';
import { CustomScheduleSidebar } from './Sidebar/CustomScheduleSidebar';

interface SidebarProps {
  isCustomStream: boolean;
  customSchedule?: CustomStreamSchedule[] | null;
  matches: StreamHeroMatch[];
  todaysCCRankings: ClubData[] | null | undefined;
  selectedMatchId: string;
  onMatchSelect: (id: string) => void;
  translations?: JsonFieldType;
  customStreamTitle: string | null;
  apiTranslations?: JsonFieldType;
  isLoading: boolean;
  error?: string;
  selectedGameStream: GameStream | null;
}

export const Sidebar = memo(({ isCustomStream, customSchedule, ...props }: SidebarProps) => {
  if (isCustomStream) {
    return <CustomScheduleSidebar customSchedule={customSchedule ?? null} {...props} />;
  }

  return <MatchesSidebar {...props} slug={null} />;
});

Sidebar.displayName = 'Sidebar';
