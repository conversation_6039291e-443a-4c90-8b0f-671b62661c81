import { Locale } from '@/hooks/i18n/const';
import { RibbonType } from '@/strapi/types/collection/ribbon';

import { StrapiBase } from '../../../types/base';
import { SeoType } from '../../../types/shared/seo';
import { getStrapiDefaultParams, strapiClient } from '../../client';
import { PreviousTournamentHero, PreviousTournamentSection, PreviousTournamentWinners } from './types';

interface PreviousTournamentData extends StrapiBase {
  hero: PreviousTournamentHero | null;
  winners: PreviousTournamentWinners | null;
  clubChampionship: PreviousTournamentSection | null;
  news: PreviousTournamentSection | null;
  ribbon: RibbonType | null;
}

export async function getSinglePreviousTournamentPageData(locale: Locale) {
  try {
    const previousTournamentData = await strapiClient.single('page-previous-tournament').find({
      populate: {
        hero: { populate: '*' },
        winners: {
          populate: { cards: { populate: { thumbnailImage: { populate: '*' } } } },
        },
        clubChampionship: { populate: '*' },
        news: { populate: '*' },
        ribbon: { populate: '*' },
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = previousTournamentData.data as PreviousTournamentData;
    return data;
  } catch (error) {
    console.error('Previous Tournament Page data not fetched!', error);
    throw new Error('Previous Tournament Page data not fetched!');
  }
}

export async function getSinglePreviousTournamentPageSeo(locale: Locale) {
  try {
    const previousTournamentData = await strapiClient.single('page-previous-tournament').find({
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    return previousTournamentData.data.seo as SeoType;
  } catch (error) {
    console.error('Previous Tournament Page SEO not fetched!', error);
    return null;
  }
}
