import { RegularClubCompetition } from '@/app/[locale]/(app)/club-championship-ranking/_utils/types';
import { calculateRankingsFromTournamentsData } from '@/hooks/tournaments/useTodaysCcRankings';
import { fetchTournamentsData } from '@/services/graphql/api';
import { GameWithCompetitions } from '@/strapi/api/collection/game';

export async function fetchClubChampionshipWinner(games: GameWithCompetitions[], winnerClubId: string) {
  const tournamentIds = games.flatMap((g) => g.competitionSlugs.map((cs) => cs.competitionId));
  const data = await fetchTournamentsData(tournamentIds);

  const todaysCCrankings = calculateRankingsFromTournamentsData(data.tournaments.result ?? [], games);
  const winner = todaysCCrankings.find((r) => r.club.id === winnerClubId) ?? null;

  const competitions = getRankedCompetitions((winner?.competitions as Record<string, RegularClubCompetition>) ?? []);

  return { winner, competitions };
}

export type RankedCompetition = RegularClubCompetition & { competitionName: string };

function getRankedCompetitions(competitionsMap: Record<string, RegularClubCompetition>) {
  const rankedCompetitionsMap: [string, RegularClubCompetition][] = Object.entries(competitionsMap).filter(
    ([, c]) => c.isFinished && c.rank.some((r) => r.rank && [1, 2, 3].includes(r.rank)),
  );

  const rankedCompetitions = rankedCompetitionsMap.map(([competitionName, c]) => ({
    ...c,
    competitionName,
  })) as RankedCompetition[];

  rankedCompetitions.sort((a, b) => {
    return (a.rank[0].rank ?? 0) - (b.rank[0].rank ?? 0);
  });

  return rankedCompetitions;
}
