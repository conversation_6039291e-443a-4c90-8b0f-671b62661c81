import { Locale } from '@/hooks/i18n/const';

const displayNamesEn = new Intl.DisplayNames(['en'], { type: 'language' });
const displayNamesAr = new Intl.DisplayNames(['ar'], { type: 'language' });
const displayNamesZh = new Intl.DisplayNames(['zh'], { type: 'language' });

export function getLanguageFromCode(code: string, locale: Locale) {
  switch (locale) {
    case 'en':
      return displayNamesEn.of(code) ?? code;
    case 'ar':
      return displayNamesAr.of(code) ?? code;
    default:
      return displayNamesZh.of(code) ?? code;
  }
}
