import { useToggle } from 'usehooks-ts';

import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';

import { ClubData } from '../../../../../../app/[locale]/(app)/club-championship-ranking/_utils/types';
import { JsonFieldType } from '../../../../../../strapi/types/helper';
import { CCRankingsBar } from './CCRankingsBar';

interface CCRankingsSectionProps {
  todaysCCRankings: ClubData[] | null | undefined;
  translations?: JsonFieldType;
}

export const CCRankingsSection = ({ todaysCCRankings, translations }: CCRankingsSectionProps) => {
  const [showCCRankingsBar, toggleCCRankingsBar] = useToggle(false);

  return (
    <Only for="lgAndAbove">
      <div className="relative min-h-[54px] px-6">
        {!showCCRankingsBar && (
          <Button
            brazeEventProperties={{
              button_name: 'CTA Button - CC Rankings',
              location: 'Live State Hero - Sidebar',
            }}
            isFullWidth
            text={translations?.['ccRankings'] ?? 'Club championship rankings'}
            variant="gold"
            onClick={() => toggleCCRankingsBar()}
          />
        )}
        {showCCRankingsBar && (
          <CCRankingsBar
            todaysCCRankings={todaysCCRankings}
            translations={translations}
            onClose={() => toggleCCRankingsBar()}
          />
        )}
      </div>
    </Only>
  );
};
