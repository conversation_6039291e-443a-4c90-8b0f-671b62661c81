import { Locale } from '@/hooks/i18n/const';
import { DynamicComponentBase } from '@/strapi/types/base';
import {
  CUSTOM_STREAM_HERO_BLOCK_KEY,
  CustomStreamHeroBlockType,
  GAME_STREAM_HERO_BLOCK_KEY,
  GameStreamHeroType,
  HERO_CLUB_CHAMPION_OVERVIEW_BLOCK_KEY,
  HeroClubChampionOverviewType,
  HOMEPAGE_STREAM_HERO_BLOCK_KEY,
  HomepageStreamHeroType,
} from '@/strapi/types/hero';

import { getGameCompetitions } from '../../collection/game';
import { getGameStreams } from '../../collection/stream';
import { assembleStreamHeroBlock } from './assembleStreamHeroBlock';

export async function assembleHeroWithAdditionalData(heroBlock: DynamicComponentBase, locale: Locale, slug?: string) {
  if (isStreamHero(heroBlock)) {
    await assembleStreamHeroBlock(heroBlock, locale);
  }
  if (isGameStreamHeroBlock(heroBlock)) {
    const gameStreams = await getGameStreams(slug as string, locale);
    heroBlock.gameStreams = gameStreams;
  }
  if (isGameWithCompetitionsHero(heroBlock)) {
    const games = await getGameCompetitions(locale, heroBlock.year);
    heroBlock.games = games;
  }
}

function isStreamHero(block: DynamicComponentBase) {
  return isHomepageStreamHeroBlock(block) || isCustomStreamHeroBlock(block);
}

function isGameWithCompetitionsHero(block: DynamicComponentBase) {
  return (
    isHomepageStreamHeroBlock(block) ||
    isCustomStreamHeroBlock(block) ||
    isGameStreamHeroBlock(block) ||
    isClubChampionOverviewHeroBlock(block)
  );
}

const isHomepageStreamHeroBlock = (block: DynamicComponentBase): block is HomepageStreamHeroType =>
  block.__component === HOMEPAGE_STREAM_HERO_BLOCK_KEY;

const isCustomStreamHeroBlock = (block: DynamicComponentBase): block is CustomStreamHeroBlockType =>
  block.__component === CUSTOM_STREAM_HERO_BLOCK_KEY;

const isGameStreamHeroBlock = (block: DynamicComponentBase): block is GameStreamHeroType =>
  block.__component === GAME_STREAM_HERO_BLOCK_KEY;

const isClubChampionOverviewHeroBlock = (block: DynamicComponentBase): block is HeroClubChampionOverviewType =>
  block.__component === HERO_CLUB_CHAMPION_OVERVIEW_BLOCK_KEY;
