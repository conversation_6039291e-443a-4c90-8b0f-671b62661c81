'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';

export function AndroidInstallBanner() {
  const [show, setShow] = useState(false);

  useEffect(() => {
    const ua = navigator.userAgent.toLowerCase();
    const isAndroid = /android/.test(ua);
    const isInApp = /(wv|instagram|fb_iab|line|twitter|messenger|snapchat)/.test(ua);
    const dismissed = localStorage.getItem('androidInstallDismissed') === 'true';

    if (isAndroid && !isInApp && !dismissed) {
      setShow(true);
    }
  }, []);

  if (!show) return null;

  return (
    <div className="fixed top-0 right-0 left-0 z-50 flex items-center justify-between gap-4 bg-[#f2f2f7] p-3 shadow-md">
      <div className="flex items-center gap-3">
        <Image alt="EWC App Icon" className="rounded-lg" height={48} src="/assets/logos/logo-store.png" width={48} />
        <div className="flex flex-col text-left leading-tight">
          <span className="text-sm font-semibold text-black">Esports World Cup</span>
          <span className="text-xs text-gray-600">Entertainment</span>
        </div>
      </div>

      <div className="ml-auto flex items-center gap-2">
        <a
          className="rounded bg-[#007aff] px-4 py-1.5 text-sm font-medium text-white"
          href="https://play.google.com/store/apps/details?id=com.uxbert.ewc"
          rel="noopener noreferrer"
          target="_blank"
        >
          Download
        </a>
        <button
          aria-label="Close banner"
          className="text-xl text-gray-500"
          onClick={() => {
            localStorage.setItem('androidInstallDismissed', 'true');
            setShow(false);
          }}
        >
          ×
        </button>
      </div>
    </div>
  );
}
