'use client';

import 'aws-amplify/auth/enable-oauth-listener';

import { Hub } from 'aws-amplify/utils';
import { useEffect } from 'react';

import { changeBrazeUser, updateBrazeUser, updateBrazeUserNotificationPreferences } from '@/services/braze';

import { fetchCurrentUser, updateUser } from '../cognito';
import { useInvalidateCurrentUserQuery } from '../hooks';
import { AuthMethod } from '../types';

const USER_UPDATED_EVENT = 'user-updated';

Hub.listen('auth', async ({ payload }) => {
  if (payload.event === 'signInWithRedirect') {
    const user = await fetchCurrentUser();

    const isRegistrationSignIn =
      user['custom:notification_email'] === undefined && user['custom:notification_push'] === undefined;

    if (isRegistrationSignIn) {
      updateBrazeUser({ email: user.email });

      const notificationPreferencesData = { emailNotificationsPermitted: false, pushNotificationsPermitted: false };

      await updateUser(notificationPreferencesData);
      dispatchEvent(new Event(USER_UPDATED_EVENT));

      updateBrazeUserNotificationPreferences(notificationPreferencesData);
    }

    changeBrazeUser(user.sub, AuthMethod.SOCIAL);
  }
});

export const SocialLoginListener = () => {
  const invalidateCurrentUser = useInvalidateCurrentUserQuery();

  useEffect(() => {
    addEventListener(USER_UPDATED_EVENT, invalidateCurrentUser);
    return () => removeEventListener(USER_UPDATED_EVENT, invalidateCurrentUser);
  }, [invalidateCurrentUser]);

  return null;
};
