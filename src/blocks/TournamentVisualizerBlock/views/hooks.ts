import { useState } from 'react';

import { LocalMatchStatus } from '@/utils/matchSeries';

import { FfaMatch } from '../utils/ffa/types';
import { Group } from '../utils/types';

export function useSelectedGroup(groups: Group[]) {
  const [selectedGroupId, setSelectedGroupId] = useState(() => getSelectedGroupBasedOnStatus(groups));

  const selectedGroup = groups.find((g) => g.id === selectedGroupId) as Group;
  return { selectedGroup, setSelectedGroupId };
}

export function getSelectedGroupBasedOnStatus(groups: Group[]) {
  const firstLiveGroup = groups.find((g) => g.status === LocalMatchStatus.LIVE);
  if (firstLiveGroup) {
    return firstLiveGroup.id;
  }

  const firstOpenGroup = groups.find((g) => g.status === LocalMatchStatus.UPCOMING);
  if (firstOpenGroup) {
    return firstOpenGroup.id;
  }

  const lastGroup = groups.at(-1);
  if (lastGroup) {
    return lastGroup.id;
  }

  return groups[0].id;
}

export function useSelectedMatch(matches: FfaMatch[] | null) {
  const [selectedMatchId, setSelectedMatchId] = useState(matches?.[0]?.id ?? null);

  const selectedMatch = matches?.find((m) => m.id === selectedMatchId) ?? null;
  return { selectedMatch, setSelectedMatchId };
}
