import clsx from 'clsx';
import { format, parseISO } from 'date-fns';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';
import { FaChevronDown } from 'react-icons/fa';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import MatchStatusPill from '@/blocks/shared/MatchStatusPill';
import { Button } from '@/ui/components/Button';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { useDragToScroll } from '../../../../../../../../components/ScrollWithFadeWrapper/useDragToScroll';
import { useDateLocale } from '../../../../../../../../hooks/i18n/useDateLocale';
import { JsonFieldType } from '../../../../../../../../strapi/types/helper';
import { cn } from '../../../../../../../../utils/cn';
import { Contestant, MapResult } from '../../../../types/StreamHeroMatch';
import { statusMap } from './StatsModal/StatsModal';

export const FFAStatsModal = ({
  contestants,
  mapResults,
  startTime,
  status,
  onClose,
  title,
  translations,
  isRestrictedTable,
}: {
  contestants: Contestant[];
  mapResults: MapResult[];
  startTime: string;
  status: LocalMatchStatus;
  onClose: () => void;
  title: React.ReactNode;
  translations?: JsonFieldType;
  isRestrictedTable: boolean;
}) => {
  const [selectedMapIndex, setSelectedMapIndex] = useState(0);
  const locale = useDateLocale();

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  useDragToScroll(scrollContainerRef);

  // disables body scroll when modal is open
  useEffect(() => {
    // Save current scroll position and body styles
    const scrollY = window.scrollY;
    const originalStyles = {
      body: {
        overflow: window.getComputedStyle(document.body).overflow,
        position: window.getComputedStyle(document.body).position,
        width: window.getComputedStyle(document.body).width,
        top: window.getComputedStyle(document.body).top,
      },
      html: {
        overflow: window.getComputedStyle(document.documentElement).overflow,
      },
    };

    // Prevent scrolling
    document.documentElement.style.overflow = 'hidden';
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.top = `-${scrollY}px`;

    // Cleanup function
    return () => {
      // Restore original styles
      document.documentElement.style.overflow = originalStyles.html.overflow;
      document.body.style.overflow = originalStyles.body.overflow;
      document.body.style.position = originalStyles.body.position;
      document.body.style.width = originalStyles.body.width;
      document.body.style.top = originalStyles.body.top;

      // Restore scroll position
      window.scrollTo(0, scrollY);
    };
  }, []);

  const selectedMap = mapResults[selectedMapIndex];
  const sortedMapResults = selectedMap.results.sort((a, b) => (a.rank ?? 999) - (b.rank ?? 999));
  const outsideClickRef = useRef<HTMLDivElement | null>(null);

  useOnClickOutside(outsideClickRef as React.RefObject<HTMLDivElement>, onClose);
  return (
    <div className="bg-dark-default/95 fixed inset-0 z-100 flex items-center justify-center px-4 md:px-8">
      <div
        className="bg-white-dirty flex h-[90svh] max-h-[800px] w-full max-w-[800px] flex-col gap-2 rounded-2xl p-5 max-md:overflow-auto max-md:p-2"
        ref={outsideClickRef}
      >
        <div className="flex justify-between">
          <div className="flex flex-col gap-2 py-1.5">
            <MatchStatusPill status={statusMap[status]} />
            <span className="text-dark-default font-primary text-[14px] font-bold uppercase">
              {format(parseISO(startTime), 'EEE, MMM d - h:mm aa', { locale })}
            </span>
          </div>
          <Button
            brazeEventProperties={{
              button_name: 'Close Button',
              location: 'Live State Hero - FFA Stats Modal',
            }}
            text={translations?.['close'] ?? 'close'}
            variant="secondary"
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
          />
        </div>
        {title}
        <div
          className="hide-scrollbar flex min-h-max flex-col gap-2 md:flex-row md:overflow-x-auto"
          ref={scrollContainerRef}
        >
          {mapResults.map((map, index) => {
            return (
              <div
                className={cn(
                  'bg-gray-easy flex min-w-[160px] cursor-pointer flex-col gap-1 rounded-md p-1.5 ps-3',
                  selectedMapIndex === index && 'bg-white',
                )}
                key={index}
                onClick={() => setSelectedMapIndex(index)}
              >
                <span className="font-base text-gray-dark text-[9px] font-extrabold uppercase">
                  {translations?.['match'] ?? 'match'} #{index + 1}
                </span>
                <span className="font-base text-[14px] font-extrabold text-black">{map.name}</span>
              </div>
            );
          })}
        </div>
        <div className="flex flex-col rounded-2xl bg-white p-4 max-md:p-1.5 md:min-h-0">
          <div className="bg-gray-easy font-primary flex items-center rounded-t-md py-1 pe-[37px] pl-2 text-[12px] font-bold uppercase">
            <span className="min-w-[53px] max-md:text-[8px]">{translations?.['rank'] ?? 'rank'}</span>
            <span className="min-w-[36px] max-md:text-[8px]">{translations?.['team'] ?? 'team'}</span>
            {!isRestrictedTable && (
              <span className="ms-auto w-10 text-center uppercase max-md:text-[8px] md:w-15">
                {translations?.['placementPointsAbbr'] ?? 'PP'}
              </span>
            )}
            {!isRestrictedTable && (
              <span className="w-10 text-center uppercase max-md:text-[8px] md:w-15">
                {translations?.['killPointsAbbr'] ?? 'KP'}
              </span>
            )}
            <span className={clsx('w-15 text-center uppercase max-md:text-[8px]', isRestrictedTable && 'ms-auto')}>
              {translations?.['total'] ?? 'total'}
            </span>
          </div>
          <div className="hide-scrollbar min-h-0 flex-1 overflow-y-auto">
            {sortedMapResults.map((result) => {
              const contestant = contestants.find((c) => c.id === result.contestantId);
              return (
                <TableRow
                  contestant={contestant}
                  isRestrictedTable={isRestrictedTable}
                  key={result.contestantId}
                  result={result}
                />
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

const TableRow = ({
  contestant,
  result,
  isRestrictedTable,
}: {
  contestant: Contestant | undefined;
  result: MapResult['results'][number];
  isRestrictedTable: boolean;
}) => {
  const [isOpen, toggleIsOpen] = useToggle(false);
  if (!contestant) {
    return null;
  }

  const placementPoints = (result.points ?? 0) - (result.score ?? 0);
  return (
    <div
      className={clsx(
        'even:bg-white-dirty relative mb-1 flex cursor-pointer flex-col rounded-md border-l-[2px] border-transparent odd:bg-white',
        {
          'border-black!': isOpen,
        },
      )}
    >
      <div
        className="even:bg-white-dirty flex items-center rounded-md py-2 ps-4 pe-3 max-md:p-1.5"
        onClick={toggleIsOpen}
      >
        <div className="flex items-center gap-2">
          <span className="font-base text-dark-default line-clamp-1 min-w-[21px] text-[14px] font-extrabold max-md:text-[11px]">
            {result.rank ? `${result.rank}.` : '-'}
          </span>
          {contestant.logoUrl && (
            <Image alt={contestant.name} className="size-[24px]" height={24} src={contestant.logoUrl} width={24} />
          )}
          <span className="font-base text-dark-default min-w-[80px] truncate text-[14px] font-extrabold max-md:text-[11px]">
            {contestant.name}
          </span>
        </div>
        {!isRestrictedTable && (
          <span className="font-base text-dark-default ms-auto w-10 text-center font-extrabold max-md:text-[11px] md:w-15">
            {placementPoints}
          </span>
        )}
        {!isRestrictedTable && (
          <span className="font-base text-dark-default w-10 text-center font-extrabold max-md:text-[11px] md:w-15">
            {result.score ?? 0}
          </span>
        )}
        <span
          className={clsx(
            'font-base text-dark-default me-[5px] w-15 text-center font-extrabold max-md:text-[11px]',
            isRestrictedTable && 'ms-auto',
          )}
        >
          {result.points ?? 0}
        </span>
        <motion.div animate={{ rotate: isOpen ? 180 : 0 }} transition={{ duration: 0.2 }}>
          <FaChevronDown
            className={cn('bg-gray size-5 rounded-sm px-[5px]', { 'bg-dark-default text-white': isOpen })}
          />
        </motion.div>
      </div>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            animate={{ height: 'auto', opacity: 1 }}
            className="divide-gray flex flex-col divide-y overflow-hidden bg-white"
            exit={{ height: 0, opacity: 0 }}
            initial={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {contestant.members.map((member, index) => (
              <div
                className={clsx('even:bg-white-dirty py-2 ps-[18px] pe-8', {
                  'rounded-md': index === contestant.members.length - 1,
                })}
                key={`${member.player.name}-${index}`}
              >
                <span className="font-base text-[12px] leading-[110%] font-extrabold max-md:text-[11px]">
                  {member.player.name}
                </span>
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
