import { Locale } from '@/hooks/i18n/const';

import { NewsFilter } from '../../../../app/[locale]/(app)/news/_components/NewsAggregator';
import { NEWS_ARTICLES_COLLECTION_KEY, NewsArticleType } from '../../../types/collection/news';
import { getStrapiDefaultParams, strapiClient } from '../../client';

interface QueryParams {
  pagination?: { page?: number; pageSize?: number; withCount?: boolean; start?: number; limit?: number };
  search?: string;
  filter?: NewsFilter;
  additionalFilters?: any;
}

export async function fetchNewsArticles(locale: Locale, params: QueryParams) {
  try {
    if (params.filter === 'latest') {
      return fetchLatestNewsArticles(locale, params);
    }
    return fetchNewsArticlesBase(locale, params);
  } catch (error) {
    console.error('News Articles data not fetched!', error);
    throw new Error('News Articles data not fetched!');
  }
}

async function fetchNewsArticlesBase(locale: Locale, params: QueryParams) {
  const { pagination, search, filter, additionalFilters } = params;

  const isArchived = filter === 'all' ? undefined : filter === 'archived' ? true : false;
  const isPR = filter === 'pr' ? true : false;

  const { filters, ...baseParams } = getBaseFetchParams(locale, isArchived, search, additionalFilters);
  const newsArticles = await strapiClient.collection(NEWS_ARTICLES_COLLECTION_KEY).find({
    ...baseParams,
    pagination,
    filters: { ...filters, ...(isPR && { tags: { displayName: { $eq: 'PR' } } }) },
  });

  const { meta, data } = newsArticles;
  return { meta, articles: data as unknown as NewsArticleType[] };
}

/**
 * Fetches the latest news articles for a given locale, filtered to include only articles
 * that have the tag `2025` and do not have the tag `PR`. This function implements custom
 * filtering and pagination logic because Strapi does not natively support filtering to
 * return items that must have one tag (`2025`) but must not have another (`PR`).
 *
 * The function first fetches all news articles with the `2025` tag from Strapi, then
 * filters out any articles that also have the `PR` tag. Pagination is then applied to
 * the filtered results to ensure correct page size and count.
 *
 * @param locale - The locale for which to fetch news articles.
 * @param params - An object containing pagination, search, and additional filter parameters.
 * @returns An object containing paginated news articles and pagination metadata.
 */
async function fetchLatestNewsArticles(locale: Locale, { pagination, search, additionalFilters }: QueryParams) {
  const { filters, ...baseParams } = getBaseFetchParams(locale, false, search, additionalFilters);

  const newsArticles = await strapiClient.collection(NEWS_ARTICLES_COLLECTION_KEY).find({
    ...baseParams,
    pagination: { limit: Infinity },
    filters: { ...filters, tags: { displayName: { $eq: '2025' } } },
  });

  const articles = newsArticles.data as unknown as NewsArticleType[];
  const nonPRArticles = articles.filter((a) => !a.tags.some((t) => t.displayName === 'PR'));

  const pageSize = pagination?.pageSize ?? nonPRArticles.length;
  const page = pagination?.page ?? 1;
  const total = nonPRArticles.length;
  const pageCount = Math.ceil(total / pageSize);

  const paginatedArticles = nonPRArticles.slice(pageSize * (page - 1), pageSize * page);
  const meta = { pagination: { page, pageSize, pageCount, total } };

  return { meta, articles: paginatedArticles };
}

function getBaseFetchParams(locale: Locale, isArchived?: boolean, search?: string, additionalFilters?: any) {
  return {
    sort: ['isArchived:asc', 'date:desc', 'createdAt:desc'],
    populate: ['tags', 'cover'],
    filters: {
      isArchived,
      ...(search && {
        $or: [{ title: { $containsi: search.toLowerCase() } }, { summary: { $containsi: search.toLowerCase() } }],
      }),
      ...additionalFilters,
    },
    ...getStrapiDefaultParams(locale),
  };
}
