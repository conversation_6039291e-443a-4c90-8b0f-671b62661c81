import { StrapiBase } from '../base';
import { DynamicZoneBlock } from '../block';
import { CustomStreamHeroBlockType } from '../hero';
import { MediaType } from '../media';
import { PostPageHeaderType } from '../shared';
import { SeoType } from '../shared/seo';

export enum PageLayoutType {
  POST = 'Post',
  IMPACT = 'Impact',
}

export interface PageType extends StrapiBase {
  slug: string | null;
  type: PageLayoutType | null;
  background: MediaType | null;
  disableBgGradient: boolean;
  header: PostPageHeaderType | null;
  hero: CustomStreamHeroBlockType | null;
  blocks: DynamicZoneBlock[];
  seo: SeoType | null;
}
