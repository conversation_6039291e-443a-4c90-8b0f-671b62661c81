'use client';

import { APP_BASE_URL, COGNITO_CLIENT_ID } from '@/config/env/client';
import { signInWithProviderExternal } from '@/services/auth/cognito/utils';
import { SocialProvider } from '@/services/auth/types';

import { socialProviderIconMap } from '../../../../_components/SocialLoginSection';
import { AccountButtonBase } from './AccountButtonBase';
import { AccountsListBase, AccountsListProps } from './AccountsListBase';

export const AvailableAccountsList = ({ user, title, buttonsCopy }: AccountsListProps) => {
  const availableAccounts = Object.values(SocialProvider).filter(
    (p) => !user.identities?.some((id) => id.providerName.includes(p)),
  );

  if (availableAccounts.length === 0) {
    return null;
  }

  const buttons = availableAccounts.map((provider) => {
    const buttonCopy = buttonsCopy.find(({ type }) => type === provider);

    async function linkAccount() {
      const params = new URLSearchParams({
        client_id: COGNITO_CLIENT_ID,
        redirect_uri: `${APP_BASE_URL}/profile/link`,
        response_type: 'code',
        scope: 'openid aws.cognito.signin.user.admin',
        prompt: 'select_account consent',
      });
      await signInWithProviderExternal(provider, params);
    }

    return (
      <AccountButtonBase
        icon={socialProviderIconMap[provider]}
        isLinkedAccount={false}
        key={provider}
        primaryText={buttonCopy?.availableAccountTitle}
        secondaryText={buttonCopy?.availableAccountSubtitle}
        onClick={linkAccount}
      />
    );
  });

  return <AccountsListBase title={title}>{buttons}</AccountsListBase>;
};
