import { useState } from 'react';

import { StreamType } from '@/strapi/types/collection/stream';

import { GameStream } from '../../shared/types/GameStream';
import { generateGameStream } from '../../shared/utils/generateGameStream';

export function useSelectedGameStream(streams: StreamType[] | null) {
  const [selectedGameStream, setSelectedGameStream] = useState<GameStream | null>(() =>
    getInitialSelectedStream(streams),
  );

  return {
    selectedGameStream,
    handleGameStreamSelect: setSelectedGameStream,
  };
}

function getInitialSelectedStream(streams: StreamType[] | null) {
  if (!streams?.length) {
    return null;
  }

  const now = Date.now();
  const firstLiveStream = streams.find((s) => {
    const streamStartTime = s.startTime ? new Date(s.startTime).getTime() : 0;
    const streamEndTime = s.endTime ? new Date(s.endTime).getTime() : Infinity;
    return streamStartTime <= now && streamEndTime > now;
  });

  return generateGameStream(firstLiveStream ? firstLiveStream : streams[0]);
}
