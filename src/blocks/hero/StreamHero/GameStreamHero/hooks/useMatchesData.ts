import { useMemo } from 'react';

import { useCurrentLocale } from '@/hooks/i18n';
import { useGameMatchSeriesData } from '@/services/graphql/hooks';

import { convertMatchSeriesToHeroData } from '../../shared/utils/convertMatchSeriesToHeroData';

export function useMatchesData() {
  const locale = useCurrentLocale();
  const { data, loading, error } = useGameMatchSeriesData();

  const matches = useMemo(
    () => (data ? convertMatchSeriesToHeroData(data.matchSeries.items, locale) : []),
    [data, locale],
  );

  return { matches, loading, errorMessage: error?.message };
}
