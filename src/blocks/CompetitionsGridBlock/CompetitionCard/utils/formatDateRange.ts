import { format } from 'date-fns';
import { Locale } from 'date-fns';

export function formatDateRange(start: Date, end: Date, locale: Locale, isChineseLocale: boolean): string {
  const sameMonth = start.getMonth() === end.getMonth();
  const sameYear = start.getFullYear() === end.getFullYear();

  const startFormat = isChineseLocale ? 'MMM do' : 'MMM dd';
  const endFormatSameMonth = isChineseLocale ? 'do, yyyy' : 'dd, yyyy';
  const endFormatDiffMonth = isChineseLocale ? 'MMM do, yyyy' : 'MMM dd, yyyy';

  const formattedStart = format(start, startFormat, { locale });
  const formattedEnd = format(end, sameMonth && sameYear ? endFormatSameMonth : endFormatDiffMonth, { locale });

  return `${formattedStart} - ${formattedEnd}`;
}
