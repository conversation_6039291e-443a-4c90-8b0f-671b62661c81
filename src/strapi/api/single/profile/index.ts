import { Locale } from '@/hooks/i18n/const';

import { SeoType } from '../../../types/shared/seo';
import { getStrapiDefaultParams, strapiClient } from '../../client';
import { ProfilePageData } from './types';

export async function getProfilePageData(locale: Locale) {
  try {
    const profilePageData = await strapiClient.single('profile-page').find({
      populate: {
        basicInfo: { populate: '*' },
        deleteAccount: { populate: '*' },
        additionalInfo: { populate: '*' },
        linkedAccounts: { populate: '*' },
        notifications: { populate: '*' },
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = profilePageData.data as ProfilePageData;
    return data;
  } catch (error) {
    console.error('Profile Page data not fetched!', error);
    throw new Error('Profile Page data not fetched!');
  }
}

export async function getProfilePageSeo(locale: Locale) {
  try {
    const profilePageData = await strapiClient.single('profile-page').find({
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    return profilePageData.data.seo as SeoType;
  } catch (error) {
    console.error('Profile Page SEO not fetched!', error);
    return null;
  }
}
