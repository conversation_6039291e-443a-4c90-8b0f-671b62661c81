import { PrizeCurrency, Tournament, TournamentContestant } from '@/services/graphql/types/tournament';

import { TableStanding } from '../types';
import { fillMissingStandings, getStandings } from './getStandings';
import { getContestantRankMap, getPrizeRankMap } from './rankMap';

export function convertToStandingsData(data: Tournament[]) {
  const allContestants = data.flatMap((d) => d.contestants).filter((c) => c?.rank) as TournamentContestant[];
  const contestantsRankMap = getContestantRankMap(allContestants);

  const allPrizepools = data.flatMap((d) => d.prizePool);

  const moneyPrizepools = allPrizepools.filter((p) => p?.currency === PrizeCurrency.USD);
  const moneyRankMap = getPrizeRankMap(moneyPrizepools);

  const pointsPrizepools = allPrizepools.filter((p) => p?.currency === PrizeCurrency.XTS);
  const pointsRankMap = getPrizeRankMap(pointsPrizepools);

  const standings: TableStanding[] = getStandings(moneyRankMap, contestantsRankMap, pointsRankMap);
  fillMissingStandings(standings);

  return standings;
}
