import clsx from 'clsx';

import { ScrollWithFadeWrapper } from '@/components/ScrollWithFadeWrapper';
import { logButtonClickedEvent } from '@/services/braze';
import { JsonFieldType } from '@/strapi/types/helper';
import { WeekTicketsLinkType } from '@/strapi/types/helper/weekTickets';

import { getScheduleGridWeekId } from './GridContent/GridContent';
import { WeekDates } from './utils';

interface Props {
  weekTickets: WeekTicketsLinkType[];
  weekDates: WeekDates[];
  selectedWeek: number;
  translations: JsonFieldType | null;
  onWeekSelect: (w: number) => void;
}

export const WeekNavigation = ({ weekTickets, weekDates, selectedWeek, translations, onWeekSelect }: Props) => {
  return (
    <ScrollWithFadeWrapper>
      <div className="flex gap-2">
        {weekDates.map((wd) => (
          <WeekButton
            isSelected={wd.week === selectedWeek}
            key={wd.week}
            number={wd.week}
            ticketsUrl={weekTickets.find((wt) => wt.week === wd.week)?.ticketsUrl}
            translations={translations}
            onSelect={() => onWeekSelect(wd.week)}
          />
        ))}
      </div>
    </ScrollWithFadeWrapper>
  );
};

interface WeekButtonProps {
  number: number;
  isSelected: boolean;
  ticketsUrl?: string | null;
  translations: JsonFieldType | null;
  onSelect: () => void;
}

const WeekButton = ({ number, isSelected, ticketsUrl, translations, onSelect }: WeekButtonProps) => {
  return (
    <button
      className={clsx(
        'flex cursor-pointer flex-col items-center gap-2.5 rounded-t-[21px] px-2.5 pt-5 transition-colors',
        isSelected ? 'bg-dark-default text-white' : 'text-dark-default',
      )}
      key={number}
      onClick={() => {
        scrollToWeek(number);
        onSelect();
        logButtonClickedEvent({ location: 'Schedule Grid - Week Navigation', button_name: `Week ${number} Button` });
      }}
    >
      <p className="font-primary text-[13px] leading-[1] font-bold uppercase">
        {translations?.['week'] ?? 'week'} #{number}
      </p>
      <a
        className={clsx(
          'text-dark-default font-primary rounded-sm bg-white px-3 py-[3px] text-[11px] font-bold whitespace-nowrap uppercase',
          ticketsUrl && isSelected ? 'visible' : 'invisible',
        )}
        href={ticketsUrl ?? ''}
        target="__blank"
        onClick={() => {
          logButtonClickedEvent({
            location: 'Schedule Grid - Week Navigation',
            button_name: `Week ${number} - Buy Tickets Button`,
          });
        }}
      >
        {translations?.['buyTickets'] ?? 'buy tickets'}
      </a>
    </button>
  );
};

const scrollToWeek = (number: number) =>
  document
    ?.getElementById(getScheduleGridWeekId(number))
    ?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
