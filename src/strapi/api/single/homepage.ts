import { Locale } from '@/hooks/i18n/const';

import { StrapiBase } from '../../types/base';
import { DynamicZoneBlock } from '../../types/block';
import {
  HERO_TOURNAMENT_FINISHED_BLOCK_KEY,
  HERO_TOURNAMENT_UPCOMING_BLOCK_KEY,
  HeroTournamentFinishedBlockType,
  HeroTournamentUpcomingBlockType,
  HOMEPAGE_STREAM_HERO_BLOCK_KEY,
  HomepageStreamHeroType,
} from '../../types/hero';
import { SeoType } from '../../types/shared/seo';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { populateVods } from '../collection/vods';
import { assembleBlocksWithAdditionalData } from '../utils/block';
import { assembleHeroWithAdditionalData } from '../utils/hero';

interface HomepageData extends StrapiBase {
  hero: HeroTournamentUpcomingBlockType | HeroTournamentFinishedBlockType | HomepageStreamHeroType | null;
  blocks: DynamicZoneBlock[];
}

const streamsPopulate = {
  populate: {
    logoDark: { fields: ['url', 'alternativeText', 'width', 'height'] },
    logoLight: { fields: ['url', 'alternativeText', 'width', 'height'] },
    backgroundImage: { populate: '*' },
    customSchedule: true,
    vods: populateVods,
    game: {
      fields: ['title', 'slug'],
      populate: {
        logoDark: { fields: ['url', 'alternativeText', 'width', 'height'] },
        schedulePopupLogo: { fields: ['url', 'alternativeText', 'width', 'height'] },
        competitionSlugs: true,
      },
    },
  },
};

export async function getHomepageData(locale: Locale) {
  try {
    //todo: getting link too long error we should move to using post instead of get when fetching data from strapi
    const baseParams = getStrapiDefaultParams(locale);

    const heroResponse = await strapiClient.single('homepage').find({
      populate: {
        hero: {
          on: {
            [HERO_TOURNAMENT_UPCOMING_BLOCK_KEY]: {
              populate: {
                logo: { fields: ['url', 'alternativeText', 'width', 'height'] },
                backgroundMedia: { fields: ['url', 'alternativeText', 'width', 'height'] },
              },
            },
            [HERO_TOURNAMENT_FINISHED_BLOCK_KEY]: {
              populate: {
                logo: { fields: ['url', 'alternativeText', 'width', 'height'] },
                backgroundMedia: { fields: ['url', 'alternativeText', 'width', 'height'] },
                stats: { fields: ['mainText', 'subText'] },
              },
            },
            [HOMEPAGE_STREAM_HERO_BLOCK_KEY]: {
              populate: {
                weekStream: {
                  populate: {
                    streams: streamsPopulate,
                  },
                },
              },
            },
          },
        },
      },
      ...baseParams,
    });

    const blocksResponse = await strapiClient.single('homepage').find({
      populate: {
        blocks: getDynamicZoneBlocks(),
      },
      ...baseParams,
    });

    await assembleBlocksWithAdditionalData(blocksResponse.data.blocks, locale);
    const hero = heroResponse.data.hero?.[0];
    if (hero) {
      await assembleHeroWithAdditionalData(hero, locale);
    }

    const data = { ...blocksResponse.data, hero } as HomepageData;
    return data;
  } catch (error) {
    console.error('Homepage data not fetched!', error);
    throw new Error('Homepage data not fetched!');
  }
}

export async function getHomepageSeo(locale: Locale) {
  try {
    const homepageData = await strapiClient.single('homepage').find({
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    return homepageData.data.seo as SeoType;
  } catch (error) {
    console.error('Homepage SEO not fetched!', error);
    return null;
  }
}
