import { StrapiImage } from '@/components/StrapiImage';

import { FooterProps } from './Footer';
import {
  CallCenterInfo,
  ChooseLanguageSection,
  CopyrightSection,
  FollowUsSection,
  LinksSection,
  TakeEwcSection,
} from './sections';

export const DesktopFooter = ({
  callCenterInfo,
  legalLinks,
  copyright,
  logo,
  chooseLanguageLabel,
  apps,
  socials,
  disabledLocales,
}: FooterProps) => {
  return (
    <div className="flex justify-between gap-8">
      <div className="mt-auto flex flex-col gap-14 xl:gap-16">
        {callCenterInfo && <CallCenterInfo {...callCenterInfo} />}
        {legalLinks && <LinksSection links={legalLinks} />}
        {copyright && <CopyrightSection text={copyright} />}
      </div>
      <div className="flex w-full flex-col items-end justify-between gap-12">
        {logo && (
          <StrapiImage
            alt="Esports World Cup logo"
            className="h-[78px] w-[468px] xl:h-[91px] xl:w-[567px]"
            image={logo}
          />
        )}
        <div className="flex gap-11 xl:gap-[102px]">
          {apps && <TakeEwcSection {...apps} />}
          <div className="flex gap-11 max-xl:self-end xl:gap-[102px]">
            {socials && <FollowUsSection {...socials} />}
            <ChooseLanguageSection disabledLocales={disabledLocales} label={chooseLanguageLabel} />
          </div>
        </div>
      </div>
    </div>
  );
};
