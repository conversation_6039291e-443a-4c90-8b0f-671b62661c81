'use client';

import { useEffect, useRef } from 'react';
import Slider from 'react-slick';

import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { Card } from './Card';
import { ShowcaseGridRowProps } from './ShowcaseGridRow';

export const ScrollingGridRow = ({ items, isRTL, isPaused }: ShowcaseGridRowProps) => {
  const sliderRef = useRef<Slider | null>(null);
  const { isSm, isMd, isLg, isXl } = useScreenType();

  useEffect(() => {
    const interval = setInterval(() => {
      if (isRTL) sliderRef.current?.slickPrev();
      else sliderRef.current?.slickNext();
    }, 2000);

    if (isPaused) clearInterval(interval);

    return () => clearInterval(interval);
  }, [sliderRef, isPaused, isRTL]);

  const settings = {
    infinite: true,
    speed: 250,
    slidesToScroll: 1,
    slidesToShow: isSm ? 2 : isMd ? 4 : isLg ? 2 : isXl ? 3 : 4,
    autoplay: false,
    autoplaySpeed: 2000,
    draggable: false,
    arrows: false,
    rtl: isRTL,
  };

  return (
    <Slider ref={sliderRef} {...settings} className="showcase-autoscroll">
      {[...items, ...items].map((item, index) => {
        return <Card {...item} key={index} />;
      })}
    </Slider>
  );
};
