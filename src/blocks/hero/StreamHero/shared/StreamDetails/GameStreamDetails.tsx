import Image from 'next/image';

import { JsonFieldType } from '@/strapi/types/helper';
import { Badge } from '@/ui/components/Badge';
import { getLocalizedMatchDescriptor } from '@/utils/localization/getLocalizedMatchDescriptor';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { StreamHeroMatch } from '../types/StreamHeroMatch';
import { isFFATournament } from '../utils/tournamentType';

export const GameStreamDetails = ({
  selectedMatchSeries,
  gameName,
  translations,
  apiTranslations,
  button,
}: {
  selectedMatchSeries: StreamHeroMatch;
  gameName?: string | null;
  translations?: JsonFieldType;
  apiTranslations?: JsonFieldType;
  button: React.ReactNode;
}) => {
  const { status, stream, contestants } = selectedMatchSeries;
  const isLive = status === LocalMatchStatus.LIVE;
  const isFFA = isFFATournament(selectedMatchSeries.type);

  return (
    <div className="bg-dark-default flex flex-col justify-between gap-4 p-4 md:flex-row xl:px-8 xl:py-6 [&>a]:md:w-fit">
      <div className="flex flex-col gap-2.5 md:flex-col-reverse">
        <div className="flex items-center gap-2 text-white">
          {/* // TODO: add VOD badge */}
          {isLive && (
            <Badge className="bg-red-accent gap-0.5 rounded-sm">
              <div className="size-1.5 animate-pulse rounded-full bg-white" />
              <span className="animate-pulse leading-[100%]">{translations?.live ?? 'live'}</span>
            </Badge>
          )}
          {gameName && <div className="font-base mt-px text-[12px] leading-none font-extrabold">{gameName}</div>}
          {stream?.language && (
            <Badge className="text-dark-default h-full rounded-sm bg-white px-1.5 py-0.5 text-[10px] leading-[1.1]">
              {stream?.language}
            </Badge>
          )}
          {!isFFA && (
            <Badge className="gap-1 rounded-sm bg-[#4d4d4d] px-1 py-0.5 leading-[1.1]">
              <div className="flex items-center gap-0.5">
                {contestants[0]?.logoUrl && (
                  <Image
                    alt={contestants[0].name ?? ''}
                    className="size-3"
                    height={12}
                    src={contestants[0].logoUrl}
                    width={12}
                  />
                )}
                <span>{contestants[0]?.name}</span>
              </div>
              <span className="text-gray-dark text-[10px] lowercase">{translations?.vs ?? 'vs'}</span>
              <div className="flex items-center gap-0.5">
                {contestants[1]?.logoUrl && (
                  <Image
                    alt={contestants[1].name ?? ''}
                    className="size-3"
                    height={12}
                    src={contestants[1].logoUrl}
                    width={12}
                  />
                )}
                <span>{contestants[1]?.name}</span>
              </div>
            </Badge>
          )}
        </div>
        <h4 className="font-primary text-[24px] leading-[100%] font-bold text-white xl:text-[34px]">
          {getLocalizedMatchDescriptor(selectedMatchSeries.metadata, apiTranslations)}
        </h4>
      </div>
      {button}
    </div>
  );
};
