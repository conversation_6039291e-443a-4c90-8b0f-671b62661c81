import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { filterEventsByDateRange } from '@/app/[locale]/(app)/schedule/_components/utils';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getSchedulePageData, getSchedulePageSeo } from '@/strapi/api/single/schedule';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';

import { ScheduleGrid } from './_components';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const seo = await getSchedulePageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | Schedule',
    description: seo?.metaDescription ?? 'This is Esports World Cup | Schedule',
  };
}

export default async function SchedulePage({ params }: PageProps) {
  const locale = (await params).locale;
  const pageData = await getSchedulePageData(locale);
  const siteConfig = await getSiteConfig(locale);

  if (!pageData) {
    return notFound();
  }

  const {
    title,
    subtitle,
    timezoneInfo,
    tournamentStartDate,
    tournamentEndDate,
    gameEvents,
    weekTickets,
    festivals,
    gridColumnWidth,
  } = pageData;

  const filteredGameEvents = filterEventsByDateRange(gameEvents, tournamentStartDate, tournamentEndDate);
  const filteredFestivals = filterEventsByDateRange(festivals, tournamentStartDate, tournamentEndDate);

  return (
    <ScheduleGrid
      festivals={filteredFestivals}
      gameEvents={filteredGameEvents}
      gridColumnWidth={gridColumnWidth}
      subtitle={subtitle}
      timezoneInfo={timezoneInfo}
      title={title}
      tournamentEndDate={tournamentEndDate}
      tournamentStartDate={tournamentStartDate}
      translations={siteConfig?.translations ?? null}
      weekTickets={weekTickets ?? []}
    />
  );
}
