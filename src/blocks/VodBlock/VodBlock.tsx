'use client';

import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { isVodEnrichedItem } from '@/blocks/VodBlock/utils/utils';
import { VodBlockType, VodItemType } from '@/strapi/types/block';

import { VodEnrichedItem, VodGrid } from './VodGrid';

export const VodBlock = ({ section, vods, itemsPerPage = 8, languageBadge = true, translations }: VodBlockType) => {
  const filteredVods = (vods as (VodItemType | VodEnrichedItem)[]).filter(
    (vod): vod is VodEnrichedItem => isVodEnrichedItem(vod) && !!vod.fetchedTitle.trim(),
  );
  if (filteredVods.length === 0) {
    return null;
  }
  return (
    <BlockSectionWrapper {...section} className="relative mx-auto w-full max-w-6xl px-4 py-[50px] lg:px-8 lg:py-[90px]">
      <VodGrid
        itemsPerPage={itemsPerPage}
        languageBadge={languageBadge}
        translations={translations}
        vods={filteredVods}
      />
    </BlockSectionWrapper>
  );
};
