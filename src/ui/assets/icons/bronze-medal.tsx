import React, { useId } from 'react';

type Props = React.SVGProps<SVGSVGElement>;

/**
 * React component rendering a bronze medal SVG icon with unique gradient and clipPath IDs.
 *
 * @remarks
 * This component should be used instead of embedding a regular SVG file directly.
 * The SVG uses internal references (IDs) for gradients and clip paths. When the same SVG is rendered
 * multiple times on a page, static IDs can cause conflicts—subsequent instances may reference the
 * definitions of the first rendered SVG. If the first instance is hidden or removed, the others may break
 * due to missing references.
 *
 * By generating unique IDs for each instance using React's `useId`, this component ensures that
 * gradients and clip paths are correctly scoped and do not interfere with each other, maintaining
 * visual integrity regardless of how many times the icon is rendered or manipulated in the DOM.
 *
 * @param props - Standard SVG and React component props.
 * @returns A bronze medal SVG icon with isolated definitions for safe multiple usage.
 */
const BronzeMedal: React.FC<Props> = (props) => {
  const id = useId();
  const clipPathId = `bronze-medal-clip-${id}`;
  const gradientId = `bronze-medal-gradient-${id}`;

  return (
    <svg fill="none" height="100%" viewBox="0 0 33 32" width="100%" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath={`url(#${clipPathId})`}>
        <path
          clipRule="evenodd"
          d="M16.7592 2.37036L12.611 10.0741L16.7592 19.5555H26.2407L16.7592 2.37036ZM27.4258 21.926H16.1666L12.611 29.6297H32.1666L27.4258 21.926ZM0.166687 29.6296L10.8334 12.4444L14.3889 20.7407L9.64817 29.6296H0.166687Z"
          fill={`url(#${gradientId})`}
          fillRule="evenodd"
        />
      </g>
      <defs>
        <radialGradient
          cx="0"
          cy="0"
          gradientTransform="translate(13.1667 26) rotate(-71.5651) scale(18.9737 69.4144)"
          gradientUnits="userSpaceOnUse"
          id={gradientId}
          r="1"
        >
          <stop stopColor="#A65A14" />
          <stop offset="0.403838" stopColor="#C09369" />
          <stop offset="0.87844" stopColor="#EFC399" />
        </radialGradient>
        <clipPath id={clipPathId}>
          <rect fill="white" height="32" transform="translate(0.166687)" width="32" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default BronzeMedal;
