import clsx from 'clsx';

import { BRACKET_HEIGHT, getConnectorWrapperHeightForTier, ROUND_HEADER_HEIGHT_STYLE } from './const';

interface Props {
  tier: number;
  bracketCount: number;
  connectorType: 'single' | 'dual';
}

export const ConnectorColumn = ({ bracketCount, connectorType, tier }: Props) => {
  const connectorCount = connectorType === 'single' ? bracketCount : bracketCount / 2;
  return (
    <div className="flex flex-col gap-4">
      <div className={ROUND_HEADER_HEIGHT_STYLE} />
      {Array.from({ length: connectorCount }).map((_, i) => (
        <Connector key={i} {...{ connectorType, tier }} />
      ))}
    </div>
  );
};

const wrapperStyle = 'flex w-10 flex-1 flex-col justify-center md:w-15';
export const Connector = ({ tier, connectorType }: Pick<Props, 'tier' | 'connectorType'>) => {
  if (connectorType === 'single') {
    return (
      <div className={clsx(wrapperStyle, BRACKET_HEIGHT)}>
        <div className="h-0.5 bg-[#E0E0E0]" />
      </div>
    );
  }

  return (
    <div className={wrapperStyle}>
      <div className={clsx('flex w-full', getConnectorWrapperHeightForTier(tier))}>
        <div className="flex grow flex-col justify-between">
          <div className="h-0.5 bg-[#E0E0E0]" />
          <div className="h-0.5 bg-[#E0E0E0]" />
        </div>
        <div className="w-0.5 bg-[#E0E0E0]" />
        <div className="flex grow flex-col justify-center">
          <div className="h-0.5 bg-[#E0E0E0]" />
        </div>
      </div>
    </div>
  );
};
