'use client';

import { motion } from 'motion/react';
import { RefObject, useRef } from 'react';
import { useOnClickOutside } from 'usehooks-ts';

import { Locale } from '@/hooks/i18n/const';
import { logButtonClickedEvent } from '@/services/braze';
import { NavigationData } from '@/strapi/api/single/navigation';
import { JsonFieldType } from '@/strapi/types/helper';
import { ButtonType } from '@/strapi/types/shared';
import { Button } from '@/ui/components/Button';

import { NavigationItem, ProfileNavigationItem } from '../../components';
import { LanguageDropdown } from './LanguageDropdown';
import { PreviousTournamentsDropdown } from './PreviousTournamentsDropdown';

interface Props {
  data: NavigationData;
  disabledLocales?: Locale[];
  isEmbedded?: boolean;
  translations: JsonFieldType;
  onClick: () => void;
}

export const MenuComponent = ({ data, disabledLocales, translations, onClick, isEmbedded }: Props) => {
  const ref = useRef<HTMLDivElement>(null);
  useOnClickOutside(ref as RefObject<HTMLElement>, onClick);

  const menuItems = data.items?.filter((i) => !i.appearsOnMobileNavigation);
  const { buttons, previousTournamentNavItems } = data;

  if (!buttons?.length && !menuItems?.length) {
    return null;
  }

  return (
    <motion.div
      animate={{ y: 0, opacity: 1 }}
      className="shadow-mobile-navigation fixed end-0 bottom-24 z-80 me-2.5 mb-2.5 h-fit min-w-[280px] rounded-2xl bg-[#EEEDEE] px-2.5 py-1.5 backdrop-blur-[30px] lg:hidden"
      exit={{ y: 20, opacity: 0 }}
      initial={{ y: 20, opacity: 0 }}
      ref={ref}
      style={{ marginBottom: 'env(safe-area-inset-bottom)' }}
      transition={{ bounce: 0, duration: 0.2 }}
    >
      <div className="flex flex-col">
        {menuItems?.map((item, i) => (
          <NavigationItem
            icon={item.icon as any}
            isEmbedded={isEmbedded}
            key={i}
            layout="row"
            title={item.title}
            url={item.url}
            onClick={() => {
              onClick();
              logButtonClickedEvent({
                location: 'Mobile Bottom Navigation - More Menu',
                button_name: `Navigation Item (${item.title})`,
              });
            }}
          />
        ))}
        {previousTournamentNavItems.length !== 0 && (
          <PreviousTournamentsDropdown
            isEmbedded={isEmbedded}
            items={data.previousTournamentNavItems}
            label={translations.history ?? 'History'}
            onClick={onClick}
          />
        )}
        {!isEmbedded && <LanguageDropdown disabledLocales={disabledLocales} onClick={onClick} />}
        <ProfileNavigationItem isEmbedded={isEmbedded} />
        <ButtonsSection buttons={buttons} isEmbedded={isEmbedded} onClick={onClick} />
      </div>
    </motion.div>
  );
};

interface ButtonsSectionProps {
  buttons?: ButtonType[];
  isEmbedded?: boolean;
  onClick: () => void;
}

const ButtonsSection = ({ buttons, isEmbedded, onClick }: ButtonsSectionProps) => {
  if (!buttons?.length) {
    return null;
  }

  return (
    <div className="flex flex-col gap-1.5">
      {buttons.map((b) => (
        <div className="[&>a>button]:rounded-[14px] [&>a>button]:text-[13px]" key={b.id}>
          <Button
            {...b}
            brazeEventProperties={{
              button_name: `Button (${b.text})`,
              location: `Mobile Bottom Navigation - More Menu`,
            }}
            isEmbedded={isEmbedded}
            isFullWidth
            onClick={onClick}
          />
        </div>
      ))}
    </div>
  );
};
