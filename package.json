{"name": "ewc-2025", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:staging": "NODE_ENV=test next dev", "build": "next build", "build:analyze": "ANALYZE=true npm run build", "start": "next start", "start:standalone": "node .next/standalone/server.js", "lint": "next lint", "typecheck": "tsc --noEmit --incremental false --tsBuildInfoFile null", "pq:generate-manifest": "generate-persisted-query-manifest", "pq:normalize": "node src/ui/providers/ApolloClientProvider/persistedQueries/writeNormalizedQueries.mjs", "pq:setup": "npm run pq:generate-manifest && npm run pq:normalize", "format": "prettier --write .", "prepare": "husky"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/client-integration-nextjs": "^0.12.2", "@apollo/persisted-query-lists": "^1.0.0", "@aws-sdk/client-cognito-identity-provider": "^3.812.0", "@braze/web-sdk": "^5.9.0", "@hookform/resolvers": "^5.0.1", "@next/bundle-analyzer": "^15.2.2", "@next/third-parties": "^15.3.0", "@radix-ui/react-popover": "^1.1.14", "@strapi/blocks-react-renderer": "^1.0.2", "@strapi/client": "^1.0.1", "@tanstack/react-query": "^5.75.5", "aws-amplify": "^6.14.4", "aws-jwt-verify": "^5.1.0", "client-only": "^0.0.1", "clsx": "^2.1.1", "country-list-js": "^3.1.8", "crypto-hash": "^3.1.0", "date-fns": "^4.1.0", "graphql-ws": "^6.0.5", "lodash": "^4.17.21", "motion": "^12.5.0", "next": "^15.2.3", "next-i18n-router": "^5.5.1", "nextjs-basic-auth-middleware": "^3.1.0", "obscenity": "^0.4.3", "react": "^19.0.0", "react-content-loader": "^7.0.2", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-flagkit": "^2.0.4", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0", "react-responsive-masonry": "^2.7.1", "react-select": "^5.10.1", "react-slick": "^0.30.3", "react-zoom-pan-pinch": "^3.7.0", "slick-carousel": "^1.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.1", "usehooks-ts": "^3.1.1", "yup": "^1.6.1"}, "devDependencies": {"@apollo/generate-persisted-query-manifest": "^1.3.0", "@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.16", "@types/node": "^22.0.0", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-responsive-masonry": "^2.6.0", "@types/react-slick": "^0.23.13", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.0.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "lint-staged": "15.5.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}}