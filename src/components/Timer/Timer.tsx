'use client';

import clsx from 'clsx';
import { differenceInDays, intervalToDuration } from 'date-fns';
import { useEffect, useState } from 'react';

import { JsonFieldType } from '../../strapi/types/helper';
import { TimeLeft } from './types';

export const Timer = ({
  targetDatetime,
  translations,
}: {
  targetDatetime: string;
  translations: JsonFieldType | null;
}) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft | null>(null);

  useEffect(() => {
    const timer = setInterval(() => setTimeLeft(calculateTimeLeft(targetDatetime)), 1000);
    return () => clearInterval(timer);
  }, [targetDatetime]);

  return (
    <div className="font-primary rounded-2xl bg-white/10 p-3 pb-2 font-bold backdrop-blur-[32px] md:rounded-4xl md:p-[18px] md:pb-[9.5px]">
      <div
        className={clsx(
          'grid gap-y-[7px] md:gap-y-[9.5px]',
          '2xl:grid-cols-[repeat(3,minmax(190px,auto)_auto)_minmax(190px,auto)]',
          'md:grid-cols-[repeat(3,minmax(125px,auto)_auto)_minmax(125px,auto)]',
          'grid-cols-[repeat(3,minmax(70px,auto)_auto)_minmax(70px,auto)]',
        )}
      >
        <TimeCell time={timeLeft?.days} />
        <SeparatorCell />
        <TimeCell time={timeLeft?.hours} />
        <SeparatorCell />
        <TimeCell time={timeLeft?.minutes} />
        <SeparatorCell />
        <TimeCell time={timeLeft?.seconds} />
        <MetricCell text={translations?.days ?? 'days'} />
        <div className="col-start-3">
          <MetricCell text={translations?.hours ?? 'hours'} />
        </div>
        <div className="col-start-5">
          <MetricCell text={translations?.minutes ?? 'minutes'} />
        </div>
        <div className="-col-start-2">
          <MetricCell text={translations?.seconds ?? 'seconds'} />
        </div>
      </div>
    </div>
  );
};

const TimeCell = ({ time }: { time?: number }) => {
  return (
    <div className="flex items-center justify-center rounded-2xl bg-white/10 px-3 py-2">
      <p
        className={clsx(
          'text-[32px] leading-[1.2] text-white md:text-7xl 2xl:text-[124px]',
          time === undefined && 'invisible',
        )}
      >
        {time ? time.toString().padStart(2, '0') : '00'}
      </p>
    </div>
  );
};

const SeparatorCell = () => (
  <div className="flex items-center justify-center px-1">
    <p className="text-[32px] leading-tight text-white md:text-5xl">:</p>
  </div>
);

const MetricCell = ({ text }: { text: string }) => (
  <div className="flex justify-center">
    <p className="text-[8px] leading-[1.1] text-white uppercase md:text-[10px] 2xl:text-[12px]">{text}</p>
  </div>
);

const calculateTimeLeft = (targetDatetime: string) => {
  const now = Date.now();

  const { hours, minutes, seconds } = intervalToDuration({ start: now, end: targetDatetime });
  const days = differenceInDays(targetDatetime, now);

  return { days, hours: hours ?? 0, minutes: minutes ?? 0, seconds: seconds ?? 0 };
};
