import { Locale } from '@/hooks/i18n/const';
import { PreviousTournamentPageType } from '@/strapi/types/collection/previousTournament';

import { StrapiBase } from '../../types/base';
import { NavigationItem } from '../../types/helper/navigationItem';
import { ButtonType, ImageLinkType } from '../../types/shared';
import { getStrapiDefaultParams, strapiClient } from '../client';

export interface NavigationData extends StrapiBase {
  logo?: ImageLinkType;
  items?: NavigationItem[];
  buttons?: ButtonType[];
  previousTournamentNavItems: NavigationItem[];
}

export async function getNavigationData(locale: Locale): Promise<NavigationData | null> {
  try {
    const baseNavigationData = await strapiClient.single('navigation').find({
      populate: {
        logo: { populate: '*' },
        items: { populate: '*' },
        buttons: { populate: '*' },
        populate: '*',
      },
      ...getStrapiDefaultParams(locale),
    });

    const previousTournamentsData = await strapiClient.collection('previous-tournaments').find({
      fields: ['slug', 'navigationLabel'],
      sort: 'slug:asc',
      ...getStrapiDefaultParams(locale),
    });

    const previousTournamentNavItems = (previousTournamentsData.data as PreviousTournamentPageType[])
      .filter((d) => !!d.slug)
      .map((d) => ({
        title: d.navigationLabel ?? `Year ${d.slug}`,
        url: `/history/${d.slug}`,
        icon: 'history',
        appearsOnMobileNavigation: true,
      }));

    const data = { ...baseNavigationData.data, previousTournamentNavItems } as NavigationData;
    return data;
  } catch (error) {
    console.error('Navigation data not fetched!', error);
    return null;
  }
}
