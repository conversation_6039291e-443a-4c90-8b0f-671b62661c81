'use client';

import { SocialProvider } from '@/services/auth/types';

import { SocialLoginSection } from '../SocialLoginSection';
import { TextDivider } from '../TextDivider';
import { LoginForm, LoginFormProps } from './LoginForm';

type Props = LoginFormProps & {
  separatorText: string | null;
  onProviderClick: (provider: SocialProvider) => Promise<void>;
};

export const LoginSection = ({ onProviderClick, separatorText, ...rest }: Props) => {
  return (
    <div className="flex flex-col gap-2 md:gap-4">
      <SocialLoginSection onProviderClick={onProviderClick} />
      <TextDivider text={separatorText} />
      <LoginForm {...rest} />
    </div>
  );
};
