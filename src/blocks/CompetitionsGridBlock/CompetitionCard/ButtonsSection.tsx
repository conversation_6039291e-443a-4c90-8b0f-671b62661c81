import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

interface ButtonsSectionProps {
  gameTitle?: string | null;
  gamePageSlug?: string | null;
  ticketsUrl?: string | null;
  isLinkingToGamePageEnabled?: boolean | null;
  isCompleted: boolean;
  translations: JsonFieldType;
}

export const ButtonsSection = ({
  gameTitle,
  gamePageSlug,
  isLinkingToGamePageEnabled,
  ticketsUrl,
  isCompleted,
  translations,
}: ButtonsSectionProps) => {
  const { isSm } = useScreenType();
  return (
    <div className="flex w-full flex-col gap-2 px-2 pb-2 md:px-6 md:pb-6">
      {isLinkingToGamePageEnabled && gamePageSlug && (
        <Button
          brazeEventProperties={{
            button_name: `Primary Button (Visit Game Page)`,
            location: `Competitions Aggregator - Card (${gameTitle})`,
          }}
          isFullWidth
          link={`/competitions/${gamePageSlug}`}
          size={isSm ? 'small' : 'default'}
          text={translations['visitGamePage'] ?? 'Visit game page'}
          variant={isCompleted ? 'white' : 'primary'}
        />
      )}
      {!isCompleted && ticketsUrl && (
        <Button
          brazeEventProperties={{
            button_name: `Secondary Button (Buy Tickets)`,
            location: `Competitions Aggregator - Card (${gameTitle})`,
          }}
          isFullWidth
          link={ticketsUrl}
          size={isSm ? 'small' : 'default'}
          text={translations['buyTickets'] ?? 'Buy tickets'}
          variant="secondary"
        />
      )}
    </div>
  );
};
