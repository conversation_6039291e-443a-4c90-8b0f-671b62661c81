import { Locale } from '@/hooks/i18n/const';
import { JsonFieldType } from '@/strapi/types/helper';
import { ConfigType, FeatureFlagsType } from '@/strapi/types/siteConfig';

import { StrapiBase } from '../../types/base';
import { getStrapiDefaultParams, strapiClient } from '../client';

export interface SiteConfigData extends StrapiBase {
  translations: JsonFieldType | null;
  apiTranslations: JsonFieldType | null;
  featureFlags: FeatureFlagsType | null;
  config: ConfigType | null;
}

export async function getSiteConfig(locale: Locale) {
  try {
    const siteConfig = await strapiClient.single('site-config').find({
      fields: ['translations', 'featureFlags', 'apiTranslations', 'config'],
      ...getStrapiDefaultParams(locale),
    });

    const data = siteConfig.data as SiteConfigData;
    return data;
  } catch (error) {
    console.error('Site Config data not fetched!', error);
    return null;
  }
}
