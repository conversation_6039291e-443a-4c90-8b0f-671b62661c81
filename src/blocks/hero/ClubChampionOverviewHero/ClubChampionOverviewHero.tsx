import clsx from 'clsx';
import Image from 'next/image';

import { fetchClubChampionshipWinner } from '@/blocks/shared/api/fetchClubChampionshipWinner';
import { StrapiImage } from '@/components/StrapiImage';
import { constructImageUrl } from '@/services/graphql/utils';
import { JsonFieldType } from '@/strapi/types/helper';
import { HeroClubChampionOverviewType } from '@/strapi/types/hero';

import { MetricSection } from './MetricSection';
import { TeamDescriptionSection } from './TeamDescriptionSection';

const borderStyles = 'rounded-lg md:rounded-xl lg:rounded-4xl';

export type ClubChampionOverviewHeroProps = HeroClubChampionOverviewType & { translations: JsonFieldType };

export const ClubChampionOverviewHero = async ({
  clubId,
  games,
  background,
  clubDescription,
  moneyWon,
  pointsWon,
  topLabel,
  translations,
}: ClubChampionOverviewHeroProps) => {
  if (!clubId) {
    return null;
  }

  const { winner, competitions } = await fetchClubChampionshipWinner(games, clubId);
  if (!winner) {
    return null;
  }

  const winnerLogoUrl = constructImageUrl(winner.images);
  const gamePlacements = competitions.map((c) => ({
    game: c.title ?? '',
    placement: c.rank[0].rank ?? NaN,
  }));

  const fixedMoneyWon = moneyWon ?? winner.prizes.USD;
  const fixedPointsWon = pointsWon ?? winner.prizes.XTS;
  return (
    <section
      className={clsx(
        'mx-auto max-w-6xl px-4 pt-[50px] lg:px-8 lg:pt-[90px]',
        '-mb-[20px] md:-mb-[65px] lg:-mb-[120px] 2xl:-mb-[160px]',
      )}
    >
      <div
        className={clsx(
          'bg-dark-default relative',
          'h-[134px] md:h-[301px] lg:h-[420px] xl:h-[518px] 2xl:h-[572px]',
          'flex w-full items-start justify-center',
          borderStyles,
        )}
      >
        {background ? (
          <StrapiImage
            className={clsx('absolute inset-0 size-full object-cover object-center', borderStyles)}
            image={background}
            priority
            quality={25}
          />
        ) : (
          <div className={clsx('bg-dark-default absolute inset-0 size-full', borderStyles)} />
        )}
        <div className={clsx('absolute inset-0 size-full bg-black/20', borderStyles)} />
        {winnerLogoUrl && (
          <Image
            alt=""
            className={clsx(
              'relative object-contain',
              'size-[64px] md:size-[160px] lg:size-[210px] 2xl:size-[276px]',
              'mt-[25px] md:mt-10 lg:mt-11 xl:mt-25 2xl:mt-[90px]',
            )}
            height={160}
            quality={25}
            src={winnerLogoUrl}
            width={160}
          />
        )}
      </div>
      <div
        className={clsx(
          'relative',
          '-top-[20px] md:-top-[65px] lg:-top-[120px] 2xl:-top-[160px]',
          'flex w-full max-lg:flex-col',
          'divide-[#ddd] bg-white shadow-md max-lg:divide-y-[1px] lg:divide-x-[1px]',
          borderStyles,
        )}
      >
        <TeamDescriptionSection
          clubDescription={clubDescription}
          clubName={winner.club.name}
          gamePlacements={gamePlacements}
          topLabel={topLabel}
        />
        {(fixedMoneyWon || fixedPointsWon) && (
          <div className="flex grow divide-[#DDDDDD] max-lg:py-3 max-md:justify-around lg:flex-col lg:divide-y-[1px]">
            {fixedPointsWon && (
              <MetricSection
                description={translations.ccPointsAchieved ?? 'CC Points Achieved'}
                labelAlignment="top"
                metric={fixedPointsWon.toLocaleString()}
              />
            )}
            {fixedMoneyWon && (
              <MetricSection
                description={translations.prizeMoneyWon ?? 'Prize Money Won'}
                metric={`$ ${fixedMoneyWon.toLocaleString()}`}
              />
            )}
          </div>
        )}
      </div>
    </section>
  );
};
