import clsx from 'clsx';
import { useMemo } from 'react';

import { logButtonClickedEvent } from '@/services/braze';
import { WeekTicketsLinkType } from '@/strapi/types/helper/weekTickets';

import { JsonFieldType } from '../../../../../strapi/types/helper';
import { isDateInRange, WeekDates } from './utils';

interface Props {
  weekDates: WeekDates[];
  weekTickets: WeekTicketsLinkType[];
  translations: JsonFieldType | null;
  dayColumnWidth: number;
}

export const WeekLabels = ({ weekDates, weekTickets, translations, dayColumnWidth }: Props) => {
  const totalDays = useMemo(() => weekDates.flatMap((w) => w.dates).length, [weekDates]);
  const totalGridWidth = `${dayColumnWidth * totalDays}px`;
  return (
    <div className="flex" style={{ width: totalGridWidth }}>
      {weekDates.map((week) => {
        const weekWidth = dayColumnWidth * week.dates.length;
        const weekTicket = weekTickets.find((ticket) => ticket.week === week.week);

        // TODO: use date object to check if current week
        const today = new Date();
        const currentWeek =
          weekDates.findIndex((week) => isDateInRange(today, week.dates[0], week.dates[week.dates.length - 1])) + 1;

        const isCurrentWeek = week.week === currentWeek;
        const isPastWeek = week.week < currentWeek;
        const isFutureWeek = week.week > currentWeek;

        return (
          <div
            className="flex flex-col gap-2 p-2"
            key={week.week}
            style={{ width: `${weekWidth}px`, backgroundColor: isCurrentWeek ? 'var(--color-gray)' : 'transparent' }}
          >
            <div
              className={clsx('flex items-center justify-between rounded px-4 py-2', {
                'bg-[#B39B6D] text-white': isCurrentWeek,
                'bg-gray text-dark-default': isFutureWeek,
                'bg-gray-easy text-dark-default': isPastWeek,
              })}
            >
              <div className="flex items-center gap-2 py-1">
                <span className="font-primary flex h-full items-center text-[18px] leading-[110%] font-bold capitalize">
                  {translations?.week ?? 'Week'} #{week.week}
                </span>
                {isCurrentWeek && (
                  <span className="text-dark-default font-secondary flex h-full items-center rounded-xs bg-white p-[3px] pt-[4px] text-[10px] leading-[110%] font-extrabold uppercase">
                    {translations?.current ?? 'Current'}
                  </span>
                )}
              </div>
              {!isPastWeek && weekTicket && (
                <a
                  className={clsx(
                    'font-primary font-weight flex items-center justify-center rounded-[3px] px-3 py-1 text-[10px] leading-[130%] text-white',
                    {
                      'bg-dark-default': isCurrentWeek,
                      'bg-gold-primary': isFutureWeek,
                    },
                  )}
                  href={weekTicket.ticketsUrl || '#'}
                  onClick={() =>
                    logButtonClickedEvent({
                      location: `Schedule Grid - Week Label (${week.week})`,
                      button_name: `Buy Tickets Link`,
                    })
                  }
                >
                  {translations?.buyTickets ?? 'BUY TICKETS'}
                </a>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};
