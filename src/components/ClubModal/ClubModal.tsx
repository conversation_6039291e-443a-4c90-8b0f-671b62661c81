'use client';

import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

import { Tournament } from '@/services/graphql/types/tournament';
import { GameWithCompetitions } from '@/strapi/api/collection/game';
import { ClubType } from '@/strapi/types/collection/club';
import { JsonFieldType } from '@/strapi/types/helper';

import { GameFilter } from './GameFilter';
import { Header } from './Header';
import { useClubFilteredData } from './hooks';
import { RostersSection } from './RostersSection';

interface ClubModalProps {
  club: ClubType;
  allTournaments: Tournament[];
  allGames: GameWithCompetitions[];
  translations: JsonFieldType;
  onClose: () => void;
}

export const ClubModal = ({ club, allTournaments, allGames, translations, onClose }: ClubModalProps) => {
  const [isMounted, setIsMounted] = useState(false);
  const [selectedGameId, setSelectedGameId] = useState<string>('');

  const { rankings, games, rosters } = useClubFilteredData(club.graphId, allTournaments, allGames, translations);
  const filteredRosters = rosters.filter((r) => (selectedGameId ? r.game.id === selectedGameId : true));

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  const modalContent = (
    <div className="fixed inset-0 z-500 flex items-center justify-center" onClick={onClose}>
      <div aria-hidden="true" className="fixed inset-0 bg-black/80" />
      <div
        className={clsx(
          'relative mx-4 h-full max-h-[90vh] w-full max-w-[1124px]',
          'p-4 md:pt-12 lg:p-8 lg:pt-16',
          'flex flex-col gap-4 overflow-y-auto bg-white shadow-xl lg:gap-8',
          'rounded-lg md:rounded-2xl lg:rounded-4xl',
        )}
        onClick={(e) => e.stopPropagation()}
      >
        <Header {...club} rankings={rankings} translations={translations} onClose={onClose} />
        <hr className="border-gray border border-t-0" />
        <GameFilter
          games={games}
          selectedGameId={selectedGameId}
          translations={translations}
          onSelect={(id) => setSelectedGameId((prevId) => (prevId === id ? '' : id))}
        />
        <hr className="border-gray border border-t-0" />
        <RostersSection rosters={filteredRosters} translations={translations} />
      </div>
    </div>
  );

  if (!isMounted) {
    return null;
  }

  return createPortal(modalContent, document.body);
};
