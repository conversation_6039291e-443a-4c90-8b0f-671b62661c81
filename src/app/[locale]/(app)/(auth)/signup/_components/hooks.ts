import { yupResolver } from '@hookform/resolvers/yup';
import { ReadonlyURLSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { signInWithProviderInternal } from '@/services/auth/cognito';
import { signInWithProviderExternal } from '@/services/auth/cognito/utils';
import { SignupValues, SocialProvider } from '@/services/auth/types';
import { SignupPageData } from '@/strapi/api/single/authPages/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { showWarningToast } from '@/ui/components/Toast';

import { getSignupFormSchema } from './schema';

export interface SignupFormValues extends SignupValues {
  passwordConfirm: string;
  country: string;
  termsAccepted: boolean;
  newsAccepted: boolean;
  marketingAccepted: boolean;
}

export function useSignupForm(translations?: JsonFieldType | null) {
  return useForm<SignupFormValues>({
    defaultValues: {
      email: '',
      name: '',
      surname: '',
      username: '',
      password: '',
      passwordConfirm: '',
      country: '',
      termsAccepted: false,
      newsAccepted: true,
      marketingAccepted: false,
    },
    resolver: yupResolver(getSignupFormSchema(translations)),
    mode: 'onTouched',
  });
}

export function useSocialLoginHandler(
  searchParams: ReadonlyURLSearchParams,
  {
    termsOfUseAlertTitle,
    termsOfUseAlertBody,
    termsOfUseAlertButtonText,
  }: Pick<SignupPageData, 'termsOfUseAlertTitle' | 'termsOfUseAlertBody' | 'termsOfUseAlertButtonText'>,
) {
  const [selectedProvider, setSelectedProvider] = useState<SocialProvider>();

  useEffect(() => {
    if (selectedProvider) {
      setSelectedProvider(undefined);

      async function redirectToProviderLogin(provider: SocialProvider) {
        const isExternalSignup = !!searchParams.get('client_id');
        if (isExternalSignup) {
          signInWithProviderExternal(provider, searchParams);
        } else {
          signInWithProviderInternal(provider);
        }
      }

      showWarningToast({
        title: termsOfUseAlertTitle ?? 'Accept terms of use to proceed.',
        description:
          termsOfUseAlertBody ?? 'By continuing with sign up you are agreeing to the terms of use of the platform.',
        buttonText: termsOfUseAlertButtonText ?? 'I Accept',
        duration: 10000,
        onClick: () => redirectToProviderLogin(selectedProvider),
      });
    }
  }, [searchParams, selectedProvider, termsOfUseAlertBody, termsOfUseAlertButtonText, termsOfUseAlertTitle]);

  return async (p: SocialProvider) => setSelectedProvider(p);
}
