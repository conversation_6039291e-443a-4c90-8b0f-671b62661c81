import clsx from 'clsx';
import Image from 'next/image';

import { MatchSeriesContestant, MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import { constructImageUrl } from '@/services/graphql/utils';
import { JsonFieldType } from '@/strapi/types/helper';
import EWC from '@/ui/assets/icons/logos/ewc-acronym.svg';

import { defaultStatusMap } from '../shared/MatchStatusPill';

const TeamScoreCard = ({
  contestant,
  matchStatus,
  logoUrl,
  isLayoutReversed = false,
  translations,
}: {
  contestant: MatchSeriesContestant | null;
  matchStatus: MatchSeriesStatus;
  logoUrl: string | null;
  isLayoutReversed?: boolean;
  translations?: JsonFieldType | null;
}) => {
  return (
    <div
      className={clsx(
        'relative flex min-h-[55px] w-full justify-end gap-2 rounded-md bg-white p-1 shadow-md max-md:rounded-t-none max-md:rounded-b-sm max-md:p-1.5',
        {
          'flex-row-reverse': isLayoutReversed,
        },
      )}
      key={contestant?.team.id}
    >
      <div
        className={clsx(
          'mr-4 flex items-center gap-4 max-md:flex-1 max-md:flex-col-reverse! max-md:justify-center max-md:gap-1',
          {
            'flex-row-reverse': isLayoutReversed,
          },
        )}
      >
        <h5
          className={clsx('text-button-big text-[16px]! max-md:text-center max-md:text-[10px]!', {
            'text-dark-default/20': !contestant,
            'text-right': !isLayoutReversed,
          })}
        >
          {contestant?.team?.name ?? translations?.['tbd'] ?? 'TBD'}
        </h5>
        {logoUrl ? (
          <Image
            alt=""
            className="object-cover"
            height={40}
            src={constructImageUrl(contestant?.team?.images, 'logo_transparent_whitebg') ?? ''}
            width={40}
          />
        ) : (
          <EWC className="size-10" />
        )}
      </div>
      <div
        className={clsx(
          'bg-white-dirty relative flex h-auto min-w-[49px] items-center justify-center self-stretch rounded-md',
          {
            'bg-[#F40F30]!': matchStatus === MatchSeriesStatus.LIVE,
            'bg-dark-default!': contestant?.result === 'WIN',
          },
        )}
      >
        <span
          className={clsx('text-h4 text-dark-default', {
            'text-white!': matchStatus === MatchSeriesStatus.LIVE || contestant?.result === 'WIN',
          })}
        >
          {defaultStatusMap[matchStatus] === 'Upcoming' ? '-' : (contestant?.score ?? '-')}
        </span>
      </div>
    </div>
  );
};

export default TeamScoreCard;
