import clsx from 'clsx';
import { useRouter } from 'next/navigation';

import { MatchSeries, MatchSeriesMatch, MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import Trophy from '@/ui/assets/icons/trophy.svg';
import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';

const MatchesAndPlayersCard = ({
  matches,
  matchSeries,
  gameLink,
}: {
  matches: MatchSeriesMatch[];
  matchSeries: MatchSeries;
  gameLink?: string;
}) => {
  const { push } = useRouter();
  const team1Id = matchSeries.contestants[0]?.team.id;
  const team2Id = matchSeries.contestants[1]?.team.id;

  const team1Players = matchSeries?.contestants[0]?.members?.map((m) => m.player.name) ?? [];
  const team2Players = matchSeries?.contestants[1]?.members?.map((m) => m.player.name) ?? [];

  return (
    <div className="flex h-full w-full gap-2.5 max-md:gap-0.5">
      <div className="flex w-full flex-col items-end self-stretch rounded-md bg-white px-2 py-1">
        {team1Players?.map((p, i) => (
          <div className="grid grid-cols-[1fr_10px] justify-items-center gap-1 text-right" key={i}>
            <span className="text-dark-default text-xs font-extrabold">{p}</span>
            <span className="text-dark-default text-xs font-extrabold">{i + 1}</span>
          </div>
        ))}
      </div>

      <div className="flex w-max flex-col gap-1 self-stretch">
        {[...matches]
          .sort((a, b) => (a.sequence ?? 0) - (b.sequence ?? 0))
          .map((match, i) => {
            const team1Contestant = match.contestants.find((c) => c.team?.id === team1Id);
            const team2Contestant = match.contestants.find((c) => c.team?.id === team2Id);

            return (
              <div
                className={clsx(
                  'grid min-h-[28px]! w-full grid-cols-[auto_1fr_auto_1fr_auto] items-center justify-items-center gap-2.5 rounded-sm p-1.5 md:min-w-[130px]',
                  {
                    'bg-white': i % 2 === 0,
                    'bg-white-dirty': i % 2 !== 0,
                  },
                )}
                key={match.sequence}
              >
                {team1Contestant?.result === 'WIN' ? (
                  <Trophy height={16} width={16} />
                ) : (
                  <div className="h-[16px] w-[16px]" />
                )}
                <span
                  className={clsx('text-button-big text-gray-dark col-start-2 min-w-[16px] text-center', {
                    'text-dark-default!': team1Contestant?.result === 'WIN',
                  })}
                >
                  {team1Contestant?.score ?? '-'}
                </span>
                <h5
                  className={clsx('font-base text-center align-middle text-[9px] leading-none font-extrabold', {
                    'line-through': match.status === 'CLOSED',
                  })}
                >
                  {match.gameMap ? match.gameMap.name : '-'}
                </h5>
                <span
                  className={clsx('text-button-big text-gray-dark min-w-[16px] text-center', {
                    'text-dark-default!': team2Contestant?.result === 'WIN',
                  })}
                >
                  {team2Contestant?.score ?? '-'}
                </span>
                {team2Contestant?.result === 'WIN' ? (
                  <Trophy height={16} width={16} />
                ) : (
                  <div className="h-[16px] w-[16px]" />
                )}
              </div>
            );
          })}
        {matchSeries.status === MatchSeriesStatus.LIVE && (
          <Only for="mdAndAbove">
            <Button
              brazeEventProperties={{
                button_name: 'Watch Now',
                location: 'Game Schedule Block',
              }}
              isFullWidth
              size="small"
              text="Watch Now"
              variant="red"
              onClick={(e) => {
                e.stopPropagation();
                if (gameLink) {
                  push(gameLink);
                  return;
                }
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
            />
          </Only>
        )}
      </div>

      <div className="flex w-full flex-col self-stretch rounded-md bg-white px-2 py-1">
        {team2Players.map((p, i) => (
          <div className="grid grid-cols-[10px_1fr] gap-1" key={i}>
            <span className="text-dark-default justify-self-center text-left text-xs font-extrabold">{i + 1}</span>
            <span className="text-dark-default text-xs font-extrabold">{p}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MatchesAndPlayersCard;
