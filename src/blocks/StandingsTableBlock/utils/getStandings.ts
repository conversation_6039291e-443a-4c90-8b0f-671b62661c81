import { StandingContestant, TableStanding } from '../types';

export function getStandings(
  moneyRankMap: Record<number, number>,
  contestantsRankMap: Record<number, StandingContestant[]>,
  pointsRankMap: Record<number, number>,
) {
  const standings: TableStanding[] = [];
  for (const rank in moneyRankMap) {
    const prizeMoney = moneyRankMap[rank];
    const contestants = contestantsRankMap[rank] ?? [];

    const existingStanding = standings.find((r) => r.prize === prizeMoney);
    const isSequentialRank = existingStanding?.ranks.includes(Number(rank) - 1);

    if (existingStanding && isSequentialRank) {
      existingStanding.ranks.push(Number(rank));
      existingStanding.clubs.push(...contestants);
    } else {
      const tbdContestant: StandingContestant = {
        rank: Number(rank),
        name: 'TBD',
        id: `tbd-${rank}`,
        logo: null,
      };

      standings.push({
        ranks: [Number(rank)],
        clubs: contestants.length ? contestants : [tbdContestant],
        prize: prizeMoney,
        points: pointsRankMap[rank] ?? null,
      });
    }
  }
  return standings;
}

export function fillMissingStandings(standings: TableStanding[]) {
  // After collecting all ranks with same prize money, ensure we have the right number of entries
  standings.forEach((standing) => {
    const expectedEntries = standing.ranks.length; // How many positions we should have
    const actualEntries = standing.clubs.length; // How many we actually have

    // If we have no entries at all, just add one TBD
    if (actualEntries === 0) {
      standing.clubs.push({
        rank: standing.ranks[0],
        name: 'TBD',
        id: `tbd-${standing.ranks.join('-')}`,
        logo: null,
      });
    }
    // If we have some entries but not enough, add TBDs for missing positions
    else if (actualEntries < expectedEntries) {
      for (let i = actualEntries; i < expectedEntries; i++) {
        standing.clubs.push({
          rank: standing.ranks[i],
          name: 'TBD',
          id: `tbd-${standing.ranks[i]}`,
          logo: null,
        });
      }
    }
  });

  return standings;
}
