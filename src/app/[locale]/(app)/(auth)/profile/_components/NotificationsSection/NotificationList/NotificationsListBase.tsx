'use client';

import clsx from 'clsx';
import { FaCheck } from 'react-icons/fa';

interface Props {
  title: string;
  subtitle: string;
  notifications: Notification[];
}

export const NotificationsListBase = ({ title, subtitle, notifications }: Props) => {
  return (
    <div className="bg-white-dirty flex flex-col gap-3 rounded-lg p-4">
      <div className="font-base text-dark-default flex flex-col gap-0.5">
        <h3 className="text-base leading-tight font-bold md:text-lg">{title}</h3>
        <p className="text-sm leading-tight font-normal">{subtitle}</p>
      </div>
      <div className="bg-gray h-px w-full" />
      <div className="flex flex-wrap gap-3">
        {notifications.map((n) => (
          <NotificationCheckbox key={n.label} {...n} />
        ))}
      </div>
    </div>
  );
};

interface Notification {
  label: string;
  value: boolean;
  onChange: (v: boolean) => void;
}

const NotificationCheckbox = ({ label, value, onChange }: Notification) => {
  return (
    <label className="relative flex cursor-pointer items-start gap-1 rounded-[3px] bg-white p-1 pe-3 shadow-[0px_4px_8px_0px_#00000014]">
      <input checked={value} className="peer absolute size-0" type="checkbox" onChange={() => onChange(!value)} />
      <div
        className={clsx(
          'flex size-4 shrink-0 items-center justify-center rounded-xs transition-colors duration-75',
          value ? 'bg-dark-default' : 'bg-white-dirty border-gray border',
          'peer-focus-visible:ring-2 peer-focus-visible:ring-[#3b82f6]',
        )}
      >
        <FaCheck
          className={clsx(
            'size-[10px] shrink-0 text-white transition-opacity duration-75',
            value ? 'opacity-100' : 'opacity-0',
          )}
        />
      </div>
      <div className="text-dark-default font-base mt-px text-xs font-bold capitalize">
        <p>{label}</p>
      </div>
    </label>
  );
};
