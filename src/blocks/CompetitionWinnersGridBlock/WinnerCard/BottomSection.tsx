import clsx from 'clsx';

import { StrapiImage } from '@/components/StrapiImage';

import { WinnerCardProps } from './WinnerCard';

type Props = Pick<WinnerCardProps, 'gameLogo' | 'translations'> & {
  isVisitGamePageVisible: boolean;
};

export const BottomSection = ({ gameLogo, isVisitGamePageVisible, translations }: Props) => {
  return (
    <div className="px-px pb-px">
      <div className="bg-radial-gold rounded-b-[3px] px-4 py-2 md:rounded-b-[7px]">
        {gameLogo ? (
          <StrapiImage
            className={clsx('block h-[54px] w-full object-contain', isVisitGamePageVisible && 'group-hover:hidden')}
            height={54}
            image={gameLogo}
            width={180}
          />
        ) : (
          <div className={clsx('block h-[54px] w-full', isVisitGamePageVisible && 'group-hover:hidden')} />
        )}
        {isVisitGamePageVisible && (
          <div className="bg-dark-default hidden h-[54px] w-full items-center justify-center rounded-md group-hover:flex hover:bg-[#212121]">
            <p className="text-button-default text-white">{translations.visitGame ?? 'visit game'}</p>
          </div>
        )}
      </div>
    </div>
  );
};
