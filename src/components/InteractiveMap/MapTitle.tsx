export default function MapTitle({ title, subtitle }: { title?: string; subtitle?: string }) {
  return (
    <div className="w-full bg-[#000] px-4 py-8 sm:px-6 md:px-10">
      <h2
        className="font-primary text-xl font-bold text-white uppercase drop-shadow-md sm:text-2xl md:text-4xl lg:text-5xl"
        dangerouslySetInnerHTML={{ __html: title || '' }}
      />
      <span className="font-base text-sm text-[#E5CA49] sm:text-base">{subtitle}</span>
    </div>
  );
}
