import Masonry, { ResponsiveMasonry } from 'react-responsive-masonry';

import { JsonFieldType } from '@/strapi/types/helper';

import { RosterCard } from '../RosterCard';
import { Roster } from '../RosterCard/types';

interface Props {
  rosters: Roster[];
  translations: JsonFieldType;
}

export const RostersSection = ({ rosters, translations }: Props) => {
  return (
    <div className="flex flex-col gap-4">
      <h5 className="text-h5 capitalize">{translations.rosters ?? 'Rosters'}</h5>
      <ResponsiveMasonry columnsCountBreakPoints={{ 350: 1, 768: 2, 1200: 3 }}>
        <Masonry gutter="20px" sequential>
          {rosters.map((roster) => (
            <RosterCard {...roster} key={roster.game.id} translations={translations} />
          ))}
        </Masonry>
      </ResponsiveMasonry>
    </div>
  );
};
