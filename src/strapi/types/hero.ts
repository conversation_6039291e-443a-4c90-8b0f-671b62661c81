import { GameWithCompetitions } from '../api/collection/game';
import { DynamicComponentBase } from './base';
import { StreamType } from './collection/stream';
import { WeekStream } from './collection/weekStream';
import { JsonFieldType, RichTextContentType, StatsBlock } from './helper';
import { MediaType } from './media';

interface HeroWithGames extends DynamicComponentBase {
  year: string | null;
  //! added in `assembleBlocksWithAdditionalData`
  games: GameWithCompetitions[];
}

export const HERO_TOURNAMENT_UPCOMING_BLOCK_KEY = 'hero.hero-tournament-upcoming';

export interface HeroTournamentUpcomingBlockType extends DynamicComponentBase {
  dateHeading: string;
  subtitle: string;
  countdownTimerDate: string | null;
  scrollForMoreText: string | null;
  logo: MediaType;
  backgroundMedia: MediaType;
  translations: JsonFieldType | null;
}

export const HERO_TOURNAMENT_FINISHED_BLOCK_KEY = 'hero.tournament-completed-hero';

export interface HeroTournamentFinishedBlockType extends DynamicComponentBase {
  dateHeading: string;
  subtitle: string;
  scrollForMoreText: string | null;
  logo: MediaType;
  backgroundMedia: MediaType;
  stats: StatsBlock[] | null;
}

export const HOMEPAGE_STREAM_HERO_BLOCK_KEY = 'hero.hero-live-state';

export interface HomepageStreamHeroType extends HeroWithGames {
  noStreamLabel: string | null;
  weekStream: {
    internalName: string | null;
    streams: StreamType[] | null;
  } | null;
}

export const GAME_STREAM_HERO_BLOCK_KEY = 'hero.game-stream-hero';

export interface GameStreamHeroType extends HeroWithGames {
  noStreamLabel: string | null;
  gameStreams: StreamType[];
}

export const CUSTOM_STREAM_HERO_BLOCK_KEY = 'hero.custom-stream-hero';

export interface CustomStreamHeroBlockType extends HeroWithGames {
  noStreamLabel: string | null;
  internalName: string | null;
  weekStream: WeekStream | null;
}

export const HERO_WINNER_HIGHLIGHT_BANNER = 'hero.winner-highlight-banner';

export interface HeroWinnerHighlightBannerType extends DynamicComponentBase {
  heroWinnerImage: MediaType | null;
  heroWinnerDescription: RichTextContentType | null;
  heroWinnerTitle: string | null;
  heroWinnerSubtitle: string | null;
  ewcLogo: MediaType | null;
  winnerLogo: MediaType | null;
}

export const HERO_CLUB_CHAMPION_OVERVIEW_BLOCK_KEY = 'hero.club-champion-overview';

export interface HeroClubChampionOverviewType extends HeroWithGames {
  clubId: string | null;
  background: MediaType | null;
  topLabel: string | null;
  clubDescription: RichTextContentType | null;
  pointsWon: number | null;
  moneyWon: number | null;
}
