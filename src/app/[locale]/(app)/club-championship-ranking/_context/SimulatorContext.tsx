import { createContext, ReactNode, useContext, useEffect, useState } from 'react';

import { SIMULATED_POINTS_OPTIONS } from '@/components/CompetitionCard/SimulatedPointsDropdown';
import { Tournament } from '@/services/graphql/types/tournament';
import { GameType } from '@/strapi/types/collection/game';

import { calculateRankingsForDay } from '../_utils/calculate-historical-rankings';
import { simulatePoints, sortAndUpdateRanks } from '../_utils/simulator';
import { SimulatedClubCompetition, SimulatedClubData } from '../_utils/types';

interface SimulatorContextType {
  isSimulatorMode: boolean;
  toggleSimulatorMode: () => void;
  simulatedRankings: SimulatedClubData[];
  setSimulatedRankings: React.Dispatch<React.SetStateAction<SimulatedClubData[]>>;
  simulatedCompetitionsMap: Record<string, Record<string, string | null>>;
  setSimulatedCompetitionsMap: React.Dispatch<React.SetStateAction<Record<string, Record<string, string | null>>>>;
  resetSimulation: () => void;
  handleSimulatePoints: (clubId: string, competitionSlug: string, points: number | null) => void;
}

const SimulatorContext = createContext<SimulatorContextType | undefined>(undefined);

export function SimulatorProvider({
  children,
  gameCompetitions,
  tournaments,
}: {
  children: ReactNode;
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo' | 'title'>[];
  tournaments: Tournament[];
}) {
  const [isSimulatorMode, setIsSimulatorMode] = useState(false);
  const [simulatedRankings, setSimulatedRankings] = useState<SimulatedClubData[]>([]);
  const [simulatedCompetitionsMap, setSimulatedCompetitionsMap] = useState<
    Record<string, Record<string, string | null>>
  >({});

  // Initialize simulatedCompetitionsMap when entering simulator mode
  useEffect(() => {
    if (isSimulatorMode) {
      const initialMap = Object.fromEntries(
        gameCompetitions
          .filter((game) => game.slug)
          .map((game) => [
            game.slug,
            Object.fromEntries(
              SIMULATED_POINTS_OPTIONS.filter((option) => option.canBeDisabled).map((option) => [option.key, null]),
            ),
          ]),
      );
      setSimulatedCompetitionsMap(initialMap);
    }
  }, [isSimulatorMode, gameCompetitions]);

  const resetSimulation = () => {
    setSimulatedRankings([]);
    setSimulatedCompetitionsMap({});
  };

  const toggleSimulatorMode = () => {
    setIsSimulatorMode((prev) => !prev);
    if (!isSimulatorMode) {
      resetSimulation();
    }
  };

  const allocateCompetitionPosition = (
    prevMap: Record<string, Record<string, string | null>>,
    competitionSlug: string,
    clubId: string,
    points: number | null,
  ) => {
    if (!prevMap?.[competitionSlug]) return prevMap;

    // Find which option was selected based on points
    const selectedOption = SIMULATED_POINTS_OPTIONS.find((opt) => opt.value === points && opt.canBeDisabled);
    if (!selectedOption?.key) return prevMap;

    // Get the previous option used by this club for this competition
    const previousOptionKey = Object.entries(prevMap[competitionSlug]).find(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ([_key, usedByClubId]) => usedByClubId === clubId,
    )?.[0];

    // Create new competition map
    const newCompetitionMap = { ...prevMap[competitionSlug] };

    // If club had a previous option, clear it
    if (previousOptionKey) {
      newCompetitionMap[previousOptionKey] = null;
    }

    // Set the new option
    newCompetitionMap[selectedOption.key] = clubId;

    return {
      ...prevMap,
      [competitionSlug]: newCompetitionMap,
    };
  };

  const calculateSimulatedClubMetrics = (competitions: Record<string, SimulatedClubCompetition>) => {
    const medals = {
      gold: 0,
      silver: 0,
      bronze: 0,
    };
    let top8Placements = 0;

    // Update medals and top8 based on simulated points
    Object.values(competitions).forEach((competition) => {
      const simulatedPoints = competition.simulatedPoints;
      if (simulatedPoints !== null) {
        // Using actual point values from SIMULATED_POINTS_OPTIONS
        if (simulatedPoints === 1000) medals.gold++;
        else if (simulatedPoints === 750) medals.silver++;
        else if (simulatedPoints === 500) medals.bronze++;

        // Top 8 includes 1st through 4th place (1000, 750, 500, 300 points)
        if (simulatedPoints >= 300) top8Placements++;
      }
    });

    // Calculate total simulated points
    const totalSimulatedPoints = Object.values(competitions).reduce(
      (sum, competition) => sum + (competition.simulatedPoints ?? 0),
      0,
    );

    // Calculate eligibility flags
    const isChampionshipEligible = top8Placements >= 2 && medals.gold > 0;
    const shouldBeGrayedOut = top8Placements < 2;

    return {
      medals,
      top8Placements,
      totalSimulatedPoints,
      isChampionshipEligible,
      shouldBeGrayedOut,
    };
  };

  const processSimulationResults = (
    prevRankings: SimulatedClubData[],
    clubId: string,
    competitionSlug: string,
    points: number | null,
  ): SimulatedClubData[] => {
    const today = new Date().toISOString().split('T')[0];

    // If no previous rankings, initialize with historical data but don't sort
    const updatedRankings =
      prevRankings.length > 0
        ? prevRankings
        : simulatePoints(calculateRankingsForDay(tournaments, today, gameCompetitions));

    // Update the points and recalculate stats
    const newRankings = updatedRankings.map((club: SimulatedClubData) => {
      if (club.club.id === clubId) {
        const updatedCompetitions = {
          ...club.competitions,
          [competitionSlug]: {
            ...club.competitions[competitionSlug],
            simulatedPoints: points,
          },
        };

        const stats = calculateSimulatedClubMetrics(updatedCompetitions);

        return {
          ...club,
          competitions: updatedCompetitions,
          simulatedPoints: stats.totalSimulatedPoints,
          medals: stats.medals,
          top8Placements: stats.top8Placements,
          isChampionshipEligible: stats.isChampionshipEligible,
          shouldBeGrayedOut: stats.shouldBeGrayedOut,
        };
      }
      return club;
    });

    // Only sort and update ranks if we're actually changing points
    return points !== null ? sortAndUpdateRanks(newRankings) : newRankings;
  };

  const handleSimulatePoints = (clubId: string, competitionSlug: string, points: number | null) => {
    // Update the simulatedCompetitionsMap first
    setSimulatedCompetitionsMap((prevMap) => allocateCompetitionPosition(prevMap, competitionSlug, clubId, points));

    // Then update the rankings
    setSimulatedRankings((prevRankings) => processSimulationResults(prevRankings, clubId, competitionSlug, points));
  };

  const value = {
    isSimulatorMode,
    toggleSimulatorMode,
    simulatedRankings,
    setSimulatedRankings,
    simulatedCompetitionsMap,
    setSimulatedCompetitionsMap,
    resetSimulation,
    handleSimulatePoints,
  };

  return <SimulatorContext.Provider value={value}>{children}</SimulatorContext.Provider>;
}

export function useSimulator() {
  const context = useContext(SimulatorContext);
  if (context === undefined) {
    throw new Error('useSimulator must be used within a SimulatorProvider');
  }
  return context;
}
