import { TeamImage } from '@/services/graphql/types/shared';
import { Prize, TournamentContestant } from '@/services/graphql/types/tournament';
import { MediaType } from '@/strapi/types/media';

export type Prizes = {
  USD?: number;
  XTS?: number;
};

// Base competition interface with shared properties
interface BaseClubCompetition {
  rank: Array<{
    rank: number | null;
    prizes: Prizes;
  }>;
  logoDark: MediaType | null;
  logoLight: MediaType | null;
  slug: string;
  title: string | null;
  timestamp: number | null;
  prizes: Prizes;
  isFinished: boolean;
}

// Regular competition data
export interface RegularClubCompetition extends BaseClubCompetition {
  isSimulated: false;
}

// Simulated competition data
export interface SimulatedClubCompetition extends BaseClubCompetition {
  isSimulated: true;
  simulatedPoints: number | null;
}

// Union type for all competition data
export type ClubCompetition = RegularClubCompetition | SimulatedClubCompetition;

// Base interface with shared properties
interface BaseClubData {
  club: {
    id: string;
    name: string | null;
  };
  prizes: Prizes;
  medals: {
    gold: number;
    silver: number;
    bronze: number;
  };
  top8Placements: number;
  shouldBeGrayedOut: boolean;
  isChampionshipEligible: boolean;
  images: TeamImage[];
  tournamentParticipated: Set<string>;
  tournamentCount?: number;
  rank?: number;
  rankDiff?: number;
}

// Regular club data
export interface RegularClubData extends BaseClubData {
  isSimulated: false;
  competitions: Record<string, RegularClubCompetition>;
}

// Simulated club data
export interface SimulatedClubData extends BaseClubData {
  isSimulated: true;
  simulatedPoints: number | null | undefined;
  competitions: Record<string, SimulatedClubCompetition>;
  originalRank?: number;
}

// Union type for all club data
export type ClubData = RegularClubData | SimulatedClubData;

// Internal types used by calculate-rankings-new.ts
export interface Competition {
  prizePool: Prize[];
  contestants: Record<
    string,
    {
      team: NonNullable<TournamentContestant['team']>;
      rank: {
        current: number | null;
        min: number | null;
      };
      tournamentId: string;
    }
  >;
  logoDark: MediaType | null;
  logoLight: MediaType | null;
  slug: string;
  title: string | null;
  timestamp: number | null;
}

export interface ExtendedTournamentContestant extends TournamentContestant {
  updatedAt?: string;
}

export interface HistoricalRankings {
  [date: string]: ClubData[];
}
