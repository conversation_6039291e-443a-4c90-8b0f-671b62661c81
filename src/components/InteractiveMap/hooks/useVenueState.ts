'use client';

import { useEffect, useMemo, useRef, useState } from 'react';

import { useFestivalFilter } from '@/context/FestivalFilterContext';
import { VenueData } from '@/strapi/types/collection/festival';

export function useVenueState(venueData: VenueData[]) {
  const { selectedVenueId, setSelectedVenueId } = useFestivalFilter();

  const [hoveredVenueId, setHoveredVenueId] = useState<string | null>(null);
  const [isHoverable, setIsHoverable] = useState(false);

  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);
  const activeHoverRefs = useRef(new Set<string>());

  const idToVenueId = useMemo(() => {
    const map = new Map<number, string>();
    venueData.forEach((v) => map.set(v.id, v.venueId));
    return map;
  }, [venueData]);

  const venueIdToId = useMemo(() => {
    const map = new Map<string, number>();
    venueData.forEach((v) => map.set(v.venueId, v.id));
    return map;
  }, [venueData]);

  const clickedVenueId = idToVenueId.get(selectedVenueId ?? -1) ?? null;

  function setClickedVenueId(venueId: string | null) {
    const newId = venueId ? (venueIdToId.get(venueId) ?? null) : null;
    setSelectedVenueId(newId);
  }

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsHoverable(window.matchMedia('(hover: hover)').matches);
    }
  }, []);

  const handleHoverEnter = (venueId: string) => {
    if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
    activeHoverRefs.current.add(venueId);
    setHoveredVenueId(venueId);
  };

  const handleHoverLeave = (venueId: string) => {
    activeHoverRefs.current.delete(venueId);
    if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
    hoverTimeout.current = setTimeout(() => {
      if (activeHoverRefs.current.size === 0) {
        setHoveredVenueId(null);
      }
    }, 100);
  };

  return {
    clickedVenueId,
    setClickedVenueId,
    hoveredVenueId,
    setHoveredVenueId,
    isHoverable,
    handleHoverEnter,
    handleHoverLeave,
  };
}
