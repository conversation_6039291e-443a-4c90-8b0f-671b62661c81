import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { PreviousTournamentWinners } from '@/strapi/api/single/previousTournament/types';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';

import { Card } from './Card';
import { getWinnersDataWithContentImages } from './const';

type Props = Partial<PreviousTournamentWinners & Pick<SiteConfigData, 'translations'>>;

export const ProudWinnersGrid = ({ title, subtitle, cards, translations }: Props) => {
  const winnersData = getWinnersDataWithContentImages(cards);

  return (
    <BlockSectionWrapper
      className="mx-auto max-w-6xl px-4 py-[50px] lg:py-[90px] xl:px-8"
      isSectionDividerVisible
      subtitle={subtitle ?? 'Get Ready for the Next Level of Competition!'}
      title={title ?? 'Proud winners of 2024'}
    >
      <div className="grid grid-cols-1 gap-x-4 gap-y-8 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {winnersData.map((d) => (
          <Card {...d} key={d.game} translations={translations} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
