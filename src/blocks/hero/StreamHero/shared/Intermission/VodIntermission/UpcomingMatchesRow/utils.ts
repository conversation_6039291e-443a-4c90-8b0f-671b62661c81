import { MatchSeriesMetadata, MatchSeriesMetadataType } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';

export function getStage(meta: MatchSeriesMetadata[], apiTranslations: JsonFieldType = {}) {
  const stageMeta =
    meta.find((m) => m.type === MatchSeriesMetadataType.ROUND) ??
    meta.find((m) => m.type === MatchSeriesMetadataType.BRACKET);

  return stageMeta ? (apiTranslations[stageMeta.id] ?? stageMeta.name) : null;
}
