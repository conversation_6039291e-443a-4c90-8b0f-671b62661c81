import { useMemo } from 'react';

import { useTodaysCcRankings } from '@/hooks/tournaments/useTodaysCcRankings';
import { Tournament } from '@/services/graphql/types/tournament';
import { GameWithCompetitions } from '@/strapi/api/collection/game';

import { CcRankings } from '../types';

/**
 * Custom React hook to compute and retrieve the club's current rankings, medals, and points.
 *
 * @param clubId - The unique identifier of the club to retrieve rankings for. Can be `null`.
 * @param allTournaments - Array of all tournaments. ALL tournaments are needed to correctly calculate rankings.
 * @param allGames - Array of all games. ALL games are needed to correctly calculate rankings.
 * @returns An object containing the club's rank, total medals, and points. If the club is not found, all values are `null`.
 *
 * @remarks
 * - Uses `useMemo` to optimize performance and avoid unnecessary recalculations.
 * - Relies on `useTodaysCcRankings` to get the latest rankings data.
 * - The returned object has the following structure:
 *   - `rank`: The club's rank (number) or `null`.
 *   - `medals`: The total number of medals (number) or `null`.
 *   - `points`: The club's points (number) or `null`.
 */
export function useClubRankings(clubId: string | null, allTournaments: Tournament[], allGames: GameWithCompetitions[]) {
  const { todaysCCRankings } = useTodaysCcRankings(allGames);

  return useMemo(() => {
    const data: CcRankings = { rank: null, medals: null, points: null };
    if (!clubId || !todaysCCRankings) {
      return data;
    }

    const clubRankings = todaysCCRankings.find((r) => r.club.id === clubId) ?? null;

    if (clubRankings) {
      data.rank = clubRankings.rank ?? null;
      data.medals = clubRankings.medals.bronze + clubRankings.medals.silver + clubRankings.medals.gold;
      data.points = clubRankings.prizes.XTS ?? null;
    }

    return data;
  }, [clubId, todaysCCRankings]);
}
