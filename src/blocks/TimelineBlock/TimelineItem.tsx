import clsx from 'clsx';

import { StrapiImage } from '@/components/StrapiImage';
import { TimelineItemType } from '@/strapi/types/helper/timelineItem';
import { Button } from '@/ui/components/Button';

interface TimelineItemProps {
  isUpItem: boolean;
}

type Props = Partial<TimelineItemProps & TimelineItemType>;

export const TimelineItem = ({ title, dateRange, image, button, isUpItem = false }: Props) => {
  return (
    <div className={clsx(isUpItem ? 'mb-8' : 'mt-8')}>
      <div className="font-tajawal relative flex w-[249px] flex-none flex-col justify-between rounded-2xl bg-white p-4 lg:rounded-3xl">
        <div>
          {image && (
            <StrapiImage className="mb-3 h-[122px] w-[217px] rounded-lg object-cover lg:rounded-2xl" image={image} />
          )}
          {dateRange && <p className="mb-1 h-[11px] w-full text-center text-[11px] font-bold">{dateRange}</p>}
          {title && (
            <p className="text-h5 mb-3 line-clamp-2 h-[50px] w-[216px] py-[2px] text-center text-ellipsis">{title}</p>
          )}
        </div>
        {button && (
          <div className="mt-3 flex items-center justify-center pt-3">
            <Button
              {...button}
              brazeEventProperties={{
                button_name: `Primary Button (${button.text})`,
                location: `Timeline Block - Item (${title})`,
              }}
              isFullWidth
            />
          </div>
        )}
        <Triangle isUpItem={isUpItem} />
      </div>
    </div>
  );
};

const Triangle = ({ isUpItem }: { isUpItem: boolean }) => {
  return (
    <div
      className={clsx(
        'border-b-dark-default absolute left-[118px] h-0 w-0 border-transparent',
        isUpItem
          ? 'border-t-dark-default top-[100%] border-t-[10px] border-r-[7px] border-l-[7px]'
          : 'border-b-dark-default bottom-[100%] border-r-[7px] border-b-[10px] border-l-[7px]',
      )}
    />
  );
};
