import { Locale } from '@/hooks/i18n/const';
import { StreamProviderType } from '@/strapi/types/collection/streamProvider';

import { getStrapiDefaultParams, strapiClient } from '../client';

export async function getStreamProviders(locale: Locale) {
  try {
    const streamProviderData = await strapiClient.collection('stream-providers').find({
      fields: ['title', 'regex', 'bgColor', 'priority'],
      populate: { icon: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    const data = streamProviderData.data as StreamProviderType[];
    if (!data) {
      return [];
    }

    return data;
  } catch (error) {
    console.error('Stream providers not fetched!', error);
    throw new Error('Stream providers not fetched!');
  }
}
