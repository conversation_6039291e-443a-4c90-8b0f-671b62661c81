'use client';

import { User } from '@/services/auth/types';
import { AccountButton } from '@/strapi/api/single/profile/types';
import { JsonFieldType } from '@/strapi/types/helper';

export interface AccountsListProps {
  title: string;
  user: User;
  buttonsCopy: AccountButton[];
  translations?: JsonFieldType | null;
}

export const AccountsListBase = ({ title, children }: React.PropsWithChildren<Pick<AccountsListProps, 'title'>>) => {
  return (
    <div className="flex w-full min-w-0 flex-col gap-2.5 lg:max-w-1/2">
      <h3 className="font-base text-dark-default text-[13px] leading-tight font-bold">{title}</h3>
      {children}
    </div>
  );
};
