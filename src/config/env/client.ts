export const STRAPI_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_URL as string;

if (!STRAPI_BASE_URL) {
  throw new Error('Missing NEXT_PUBLIC_STRAPI_URL env variable!');
}

const STRAPI_MEDIA_URL = STRAPI_BASE_URL.replace(':1337', '').replace('.strapiapp.com', '.media.strapiapp.com');
export const STRAPI_MEDIA_HOSTNAME = STRAPI_MEDIA_URL.replace('https://', '').replace('http://', '');

export const CDN_BASE_URL = process.env.NEXT_PUBLIC_CDN_BASE_URL as string;

if (!CDN_BASE_URL) {
  throw new Error('Missing NEXT_PUBLIC_CDN_BASE_URL env variable!');
}

export const APP_BASE_URL = process.env.NEXT_PUBLIC_APP_BASE_URL as string;

if (!APP_BASE_URL) {
  throw new Error('Missing NEXT_PUBLIC_APP_BASE_URL env variable!');
}

export const COGNITO_DOMAIN = process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN as string;
export const COGNITO_USER_POOL_ID = process.env.NEXT_PUBLIC_AWS_COGNITO_USER_POOL_ID as string;
export const COGNITO_CLIENT_ID = process.env.NEXT_PUBLIC_AWS_COGNITO_USER_POOL_CLIENT_ID as string;

if (!COGNITO_DOMAIN || !COGNITO_USER_POOL_ID || !COGNITO_CLIENT_ID) {
  throw new Error('Missing AWS_COGNITO env variables!');
}

export const GRAPH_API_URL = process.env.NEXT_PUBLIC_GRAPH_API_URL as string;

if (!GRAPH_API_URL) {
  throw new Error('Missing NEXT_PUBLIC_GRAPH_API_URL env variable!');
}
export const GRAPHQL_API_HOSTNAME = GRAPH_API_URL.replace('https://', '').replace('http://', '').split('/')[0];

export const FANTASY_BASE_URL = process.env.NEXT_PUBLIC_FANTASY_BASE_URL as string;

if (!FANTASY_BASE_URL) {
  throw new Error('Missing NEXT_PUBLIC_FANTASY_BASE_URL env variable!');
}
