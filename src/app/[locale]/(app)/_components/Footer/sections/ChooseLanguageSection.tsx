'use client';

import { DarkLanguageDropdown } from '@/components/LanguageDropdown/DarkLanguageDropdown';
import { Locale } from '@/hooks/i18n/const';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

interface Props {
  label: string | null;
  disabledLocales?: Locale[];
}

export const ChooseLanguageSection = ({ label, disabledLocales }: Props) => {
  const { isSm, isMd } = useScreenType();

  return (
    <div className="flex w-full min-w-[196px] flex-col items-center justify-between gap-3 lg:w-fit lg:items-end">
      {label && <p className="font-primary text-gold-primary text-xs leading-normal font-bold uppercase">{label}</p>}
      <div className="relative h-[44.5px] w-full">
        <div className="absolute end-0 top-0 w-full lg:w-fit">
          <DarkLanguageDropdown disabledLocales={disabledLocales} isOpenUpwards={!isSm && !isMd} />
        </div>
      </div>
    </div>
  );
};
