'use client';

import { createContext, useContext, useState } from 'react';

interface SelectedMatchData {
  selectedMatchId: string;
  setSelectedMatchId: (id: string) => void;
}

export const SelectedMatchContext = createContext<SelectedMatchData | undefined>(undefined);

export const SelectedMatchProvider = ({ children }: React.PropsWithChildren) => {
  const [selectedMatchId, setSelectedMatchId] = useState('');

  return (
    <SelectedMatchContext.Provider value={{ selectedMatchId, setSelectedMatchId }}>
      {children}
    </SelectedMatchContext.Provider>
  );
};

export function useSelectedMatch() {
  const state = useContext(SelectedMatchContext);

  if (!state) {
    throw new Error('useSelectedMatch must be used within a SelectedMatchProvider!');
  }

  return state;
}
