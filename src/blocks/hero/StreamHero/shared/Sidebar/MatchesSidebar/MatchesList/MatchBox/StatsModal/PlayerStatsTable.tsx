import clsx from 'clsx';
import { camelCase } from 'lodash';
import Image from 'next/image';

import { GameStream } from '@/blocks/hero/StreamHero/shared/types/GameStream';
import { GameDataQueryResponse, Player } from '@/services/graphql/types/gameData';
import { Member } from '@/services/graphql/types/tournament';
import { JsonFieldType } from '@/strapi/types/helper';
import EWC from '@/ui/assets/icons/logos/ewc-acronym.svg';

interface Props {
  logoUrl: string | null;
  teamName: string;
  members: Member[];
  selectedGameStream: GameStream | null;
  data?: GameDataQueryResponse | null;
  translations?: JsonFieldType;
  slug: string | null;
}

const gameMapper = {
  valorant: {
    style: 'grid-cols-[60px_60px_60px_80px] max-md:grid-cols-[30px_30px_30px_60px]',
    columns: ['K-D', 'Assists', 'ADR', 'Agent'],
    statMapper: (player: Player, totalRounds?: number) => {
      return {
        kd: `${player.stats?.kills ?? 0} -
          ${player.stats?.deaths ?? 0}`,
        assists: player.stats?.assists ?? 0,
        adr: totalRounds && totalRounds > 0 ? ((player.stats?.damage ?? 0) / totalRounds).toFixed(1) : 0,
        hero: player.meta?.character?.name ?? '-',
      };
    },
  },
  dota2: {
    style: 'grid-cols-[60px_60px_60px_60px_80px] max-md:grid-cols-[30px_30px_30px_30px_60px]',
    columns: ['K-D', 'Assists', 'XP', 'GOLD', 'Hero'],
    statMapper: (player: Player) => {
      return {
        kd: `${player.stats?.kills ?? 0} -
          ${player.stats?.deaths ?? 0}`,
        assists: player.stats?.assists ?? 0,
        xp: player.attributes?.xp?.current ?? 0,
        gold: player.attributes?.gold?.current ?? 0,
        hero: player.meta?.character?.name?.split('_')?.[3] ?? '-',
      };
    },
  },
  mlbb: {
    style: 'grid-cols-[60px_60px_60px_60px_80px] max-md:grid-cols-[30px_30px_30px_30px_60px]',
    columns: ['K-D', 'Assists', 'XP', 'GOLD', 'Hero'],
    statMapper: (player: Player) => {
      return {
        kd: `${player.stats?.kills ?? 0} -
          ${player.stats?.deaths ?? 0}`,
        assists: player.stats?.assists ?? 0,
        xp: player.attributes?.xp?.current ?? 0,
        gold: player.attributes?.gold?.current ?? 0,
        hero: player.meta?.character?.name ?? '-',
      };
    },
  },
  'mlbb-women': {
    style: 'grid-cols-[60px_60px_60px_60px_80px] max-md:grid-cols-[30px_30px_30px_30px_60px]',
    columns: ['K-D', 'Assists', 'XP', 'GOLD', 'Hero'],
    statMapper: (player: Player) => {
      return {
        kd: `${player.stats?.kills ?? 0} -
          ${player.stats?.deaths ?? 0}`,
        assists: player.stats?.assists ?? 0,
        xp: player.attributes?.xp?.current ?? 0,
        gold: player.attributes?.gold?.current ?? 0,
        hero: player.meta?.character?.name ?? '-',
      };
    },
  },
  cs2: {
    style: 'grid-cols-[60px_60px_60px_60px] max-md:grid-cols-[30px_30px_30px_30px]',
    columns: ['K-D', 'Assists', 'HS%', 'ADR'],
    statMapper: (player: Player, totalRounds?: number) => {
      return {
        kd: `${player.stats?.kills ?? 0} -
          ${player.stats?.deaths ?? 0}`,
        assists: player.stats?.assists ?? 0,
        hs:
          player.stats?.headshots != null && player.stats?.kills != null && player.stats?.headshots !== 0
            ? `${((player.stats?.headshots / player.stats?.kills) * 100).toFixed(1)}%`
            : 0,
        adr: totalRounds && totalRounds > 0 ? ((player.stats?.damage ?? 0) / totalRounds).toFixed(1) : 0,
      };
    },
  },
  'league-of-legends': {
    style: 'grid-cols-[60px_60px_60px_80px] max-md:grid-cols-[30px_30px_30px_60px]',
    columns: ['K-D', 'Assists', 'GOLD', 'Champion'],
    statMapper: (player: Player) => {
      return {
        kd: `${player.stats?.kills ?? 0} -
          ${player.stats?.deaths ?? 0}`,
        assists: player.stats?.assists ?? 0,
        gold: player.attributes?.gold?.current ?? 0,
        champion: player.meta?.character?.name ?? '-',
      };
    },
  },
};

export const PlayerStatsTable = ({
  logoUrl,
  teamName,
  members,
  selectedGameStream,
  data,
  translations,
  slug,
}: Props) => {
  if (!selectedGameStream && !slug) return null;

  const gameDataTeams = data?.gameData?.data?.[0]?.state?.contestants ?? [];

  return (
    <div className="flex w-full flex-col">
      <div className="bg-gray-easy flex min-h-[24px] w-full justify-between rounded-md px-2 py-1.5">
        <div className="flex items-center gap-2">
          {logoUrl ? (
            <Image alt="" className="object-cover" height={20} src={logoUrl ?? ''} width={20} />
          ) : (
            <EWC className="size-6" />
          )}
          <span className="text-dark-default font-riforma line-clamp-1 text-xs font-bold max-md:text-[10px]">
            {teamName}
          </span>
        </div>
        <div
          className={clsx(
            'grid items-center gap-6 text-center max-md:gap-1.5',
            gameMapper[(selectedGameStream?.slug as keyof typeof gameMapper) ?? (slug as keyof typeof gameMapper)]
              ?.style,
          )}
        >
          {gameMapper[
            (selectedGameStream?.slug as keyof typeof gameMapper) ?? (slug as keyof typeof gameMapper)
          ]?.columns.map((c: string) => (
            <span className="font-riforma text-dark-default text-[10px] font-bold uppercase max-md:text-[8px]" key={c}>
              {translations?.[camelCase(c)] ?? c}
            </span>
          ))}
        </div>
      </div>
      {(() => {
        // Calculate total rounds as the sum of all team scores
        const totalRounds = gameDataTeams.reduce((sum, team) => sum + (team.score ?? 0), 0);
        // Map members to { member, matchedPlayer } objects
        const memberPlayerPairs = members.map((p) => {
          let matchedPlayer = undefined;
          for (const team of gameDataTeams) {
            matchedPlayer = team.players.find(
              (player: { id: string }) => p.player?.id && player?.id && p.player?.id === player?.id,
            );
            if (matchedPlayer) break;
          }
          return { member: p, matchedPlayer };
        });
        // Filter out those without a matchedPlayer (type guard)
        const filtered = memberPlayerPairs.filter(
          (pair): pair is { member: (typeof members)[number]; matchedPlayer: Player } => !!pair.matchedPlayer,
        );
        // Sort by kills
        filtered.sort((a, b) => (b.matchedPlayer.stats?.kills ?? 0) - (a.matchedPlayer.stats?.kills ?? 0));
        // Render
        return filtered.map(({ member: p, matchedPlayer }, i) => (
          <div
            className={clsx('flex justify-between gap-1 bg-white p-2', {
              'bg-white-dirty': i % 2 !== 0,
            })}
            key={i}
          >
            <span className="text-dark-default truncate text-sm font-extrabold max-md:text-[10px]">
              {p.player.name}
            </span>
            <div
              className={clsx(
                'grid items-center gap-6 text-center max-md:gap-1.5',
                gameMapper[(selectedGameStream?.slug as keyof typeof gameMapper) ?? (slug as keyof typeof gameMapper)]
                  ?.style,
              )}
            >
              {(() => {
                const game =
                  gameMapper[
                    (selectedGameStream?.slug as keyof typeof gameMapper) ?? (slug as keyof typeof gameMapper)
                  ];
                if (game && 'statMapper' in game) {
                  const stats = game.statMapper(matchedPlayer, totalRounds);
                  return Object.entries(stats).map(([key, stat]) => (
                    <span
                      className="text-dark-default truncate text-sm font-extrabold capitalize max-md:text-[10px]"
                      key={key}
                    >
                      {stat}
                    </span>
                  ));
                }
              })()}
            </div>
          </div>
        ));
      })()}
    </div>
  );
};
