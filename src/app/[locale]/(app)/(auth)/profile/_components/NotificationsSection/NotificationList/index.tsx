'use client';

import { useUpdateUserNotificationPreferences } from '@/services/auth/hooks';

import { NotificationsListBase } from './NotificationsListBase';

interface ChannelPermissionsListProps {
  emailNotificationsPermitted: boolean;
  pushNotificationsPermitted: boolean;
  emailNotificationsLabel?: string | null;
  pushNotificationsLabel?: string | null;
  title?: string | null;
  subtitle?: string | null;
}

export const ChannelPermissionsList = ({
  emailNotificationsPermitted,
  pushNotificationsPermitted,
  emailNotificationsLabel,
  pushNotificationsLabel,
  title,
  subtitle,
}: ChannelPermissionsListProps) => {
  const { mutateAsync } = useUpdateUserNotificationPreferences();
  return (
    <NotificationsListBase
      notifications={[
        {
          label: emailNotificationsLabel ?? 'Email',
          value: emailNotificationsPermitted,
          onChange: () => mutateAsync({ emailNotificationsPermitted: !emailNotificationsPermitted }),
        },
        {
          label: pushNotificationsLabel ?? 'Push Notifications',
          value: pushNotificationsPermitted,
          onChange: () => mutateAsync({ pushNotificationsPermitted: !pushNotificationsPermitted }),
        },
      ]}
      subtitle={subtitle ?? "Where we'll contact you with updates, exclusive promotions, and more."}
      title={title ?? 'Channel Permissions'}
    />
  );
};
