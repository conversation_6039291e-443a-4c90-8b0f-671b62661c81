import clsx from 'clsx';
import { useMemo } from 'react';

import { CustomStreamSchedule } from '../../../../strapi/types/collection/stream';
import { JsonFieldType } from '../../../../strapi/types/helper';
import { Badge } from '../../../../ui/components/Badge';
import { Button } from '../../../../ui/components/Button';
import { useCountdown } from '../shared/hooks/useCountdown';

export const StreamDetails = ({
  translations,
  customSchedule,
  title,
  button,
}: {
  translations?: JsonFieldType;
  title: string | null | undefined;
  customSchedule: CustomStreamSchedule[] | null;
  button: { text: string | null; url: string | null };
}) => {
  // Find the first live schedule by checking each schedule's time
  const firstLiveSchedule = useMemo(() => {
    if (!customSchedule) return null;
    const now = new Date();
    return customSchedule.find((schedule) => {
      const startTime = schedule.startTime ? new Date(schedule.startTime) : null;
      const endTime = schedule.endTime ? new Date(schedule.endTime) : null;
      return startTime && endTime && startTime <= now && endTime >= now;
    });
  }, [customSchedule]);

  const { isLive } = useCountdown(firstLiveSchedule?.startTime ?? null, firstLiveSchedule?.endTime ?? null);

  return (
    <div className="bg-dark-default flex flex-col justify-between gap-4 p-4 md:flex-row xl:px-8 xl:py-6 [&>a]:md:w-fit">
      {firstLiveSchedule && (
        <div className="flex flex-col gap-2.5 md:flex-col-reverse">
          <div className="flex items-center gap-2 text-white">
            {isLive && (
              <Badge className="bg-red-accent gap-0.5 rounded-sm">
                <div className="size-1.5 animate-pulse rounded-full bg-white" />
                <span className="animate-pulse leading-[100%]">{translations?.live ?? 'live'}</span>
              </Badge>
            )}
            <div className="font-base text-[12px] leading-[120%] font-extrabold">{title}</div>
            {firstLiveSchedule.language && (
              <Badge className="text-dark-default bg-white">{firstLiveSchedule.language}</Badge>
            )}
          </div>
          <h4 className="font-primary text-[24px] leading-[100%] font-bold text-white xl:text-[34px]">
            {firstLiveSchedule.title}
          </h4>
        </div>
      )}
      {!firstLiveSchedule && (
        <h4 className="font-primary self-center text-[24px] leading-[100%] font-bold text-white xl:text-[34px]">
          {title}
        </h4>
      )}
      {button.url && (
        <div className={clsx('md:ms-auto md:w-fit')}>
          <Button
            brazeEventProperties={{ button_name: 'test', location: 'Custom Stream Hero - Stream Details' }}
            isFullWidth
            link={button.url}
            text={button.text}
            variant="white"
          />
        </div>
      )}
    </div>
  );
};
