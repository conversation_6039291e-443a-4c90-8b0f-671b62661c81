import { Locale } from '@/hooks/i18n/const';
import { MatchSeries, MatchSeriesContestant, MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import { constructImageUrl } from '@/services/graphql/utils';
import { convertToLocalMatchStatus, LocalMatchStatus } from '@/utils/matchSeries';

import { Contestant, StreamHeroMatch } from '../types/StreamHeroMatch';

export function convertMatchSeriesToHeroData(series: MatchSeries[], locale: Locale) {
  const matches = series.map((s) => {
    const contestants = getContestants(s.contestants);

    const localStreams = s.streams.filter((s) => s.language === locale);
    const stream = localStreams.find((s) => s.primary) ?? localStreams[0] ?? null;

    let status = LocalMatchStatus.COMPLETED;
    if (s.status === MatchSeriesStatus.LIVE) {
      status = LocalMatchStatus.LIVE;
    } else if ([MatchSeriesStatus.OPEN, MatchSeriesStatus.DELAYED, MatchSeriesStatus.POSTPONED].includes(s.status)) {
      status = LocalMatchStatus.UPCOMING;
    }

    return {
      tournamentName: s.tournament.name,
      tournamentId: s.tournament.id,
      id: s.id,
      startTime: s.startTime,
      status,
      stream,
      contestants,
      type: s.tournament.variant,
      metadata: s.metadata,
      mapResults: s.matches
        .toSorted((a, b) => (a.sequence ?? 0) - (b.sequence ?? 0))
        .map((m) => {
          return {
            matchId: m.id,
            id: m.gameMap?.id ?? null,
            name: m.gameMap?.name ?? null,
            status: convertToLocalMatchStatus(m.status),
            sequence: m.sequence,
            winnerId: m.contestants.find((c) => c.result === 'WIN')?.team.id ?? null,
            results:
              m.contestants.map((c) => ({
                contestantId: c.team.id,
                score: c.score,
                isWinner: c.team.id === m.contestants.find((c) => c.result === 'WIN')?.team.id,
                rank: c.rank,
                points: c.points,
              })) ?? [],
          };
        }),
    } as StreamHeroMatch;
  });

  matches.sort(sortByTypeAndStartTime);
  return matches;
}

function sortByTypeAndStartTime(a: StreamHeroMatch, b: StreamHeroMatch) {
  if (a.status === b.status) {
    return a.startTime < b.startTime ? -1 : a.startTime > b.startTime ? 1 : 0;
  }

  if (a.status === LocalMatchStatus.LIVE) {
    return -1;
  }
  if (a.status === LocalMatchStatus.UPCOMING && b.status === LocalMatchStatus.COMPLETED) {
    return -1;
  }
  return 1;
}

function getContestants(contestants: MatchSeriesContestant[]): Contestant[] {
  const matchContestants = contestants.map((c) => ({
    id: c.team.id,
    name: c.team.name ?? 'TBD',
    logoUrl: constructImageUrl(c.team.images, 'logo_transparent_whitebg'),
    points: c.points,
    score: c.score,
    rank: c.rank,
    isWinner: c.result === 'WIN',
    members: c.members,
  }));

  const placeholdersCount = 2 - matchContestants.length;
  const placeholders = [...Array(placeholdersCount > 0 ? placeholdersCount : 0)].map((_, i) => ({
    id: i.toString(),
    name: 'TBD',
    points: null,
    logoUrl: null,
    members: [],
    isWinner: null,
    score: null,
    rank: null,
  }));

  return [...matchContestants, ...placeholders];
}
