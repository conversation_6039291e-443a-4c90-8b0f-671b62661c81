'use client';

import Image from 'next/image';
import { memo, useRef } from 'react';
import { useIsClient } from 'usehooks-ts';

import { useDragToScroll } from '@/components/ScrollWithFadeWrapper/useDragToScroll';
import { StreamType } from '@/strapi/types/collection/stream';
import { JsonFieldType } from '@/strapi/types/helper';
import { Badge } from '@/ui/components/Badge';
import { cn } from '@/utils/cn';

import { useCountdown } from '../../shared/hooks/useCountdown';
import { GameStream } from '../../shared/types/GameStream';
import { generateGameStream } from '../../shared/utils/generateGameStream';
import { EmbedSliderArrows } from './EmbedSliderArrows';

interface GamePickerProps {
  selectedGameStream: GameStream | null;
  onGameSelect: (game: GameStream) => void;
  streams: StreamType[] | null;
  translations?: JsonFieldType;
}

export const GamePicker = memo(({ selectedGameStream, onGameSelect, streams, translations }: GamePickerProps) => {
  const ref = useRef<HTMLDivElement>(null);
  useDragToScroll(ref);

  const areStreamsEmpty = !streams || streams.length === 0;
  if (areStreamsEmpty) {
    return null;
  }

  const gameStreams: GameStream[] = streams.map(generateGameStream);

  return (
    <EmbedSliderArrows scrollRef={ref}>
      <div
        className="bg-dark-default hide-scrollbar flex shrink-0 gap-2 overflow-x-auto px-3 lg:gap-4 lg:px-4 xl:px-6"
        ref={ref}
      >
        {gameStreams.map((gameStream) => (
          <GameStreamCard
            gameStream={gameStream}
            isSelected={gameStream.id === selectedGameStream?.id}
            key={gameStream.id}
            translations={translations}
            onClick={() => onGameSelect(gameStream)}
          />
        ))}
      </div>
    </EmbedSliderArrows>
  );
});

GamePicker.displayName = 'GamePicker';

const GameStreamCard = ({
  gameStream,
  onClick,
  isSelected,
  translations,
}: {
  gameStream: GameStream;
  onClick: () => void;
  isSelected: boolean;
  translations?: JsonFieldType;
}) => {
  const { timeLeft, isLive, isUpcoming, isCompleted } = useCountdown(gameStream.startTime, gameStream.endTime);
  const isClient = useIsClient();

  return (
    <button className="relative shrink-0 cursor-pointer py-3 lg:py-4 xl:py-6" onClick={onClick}>
      <div
        className={cn(
          'relative flex h-[42px] w-[120px] items-center justify-center rounded-sm bg-white/10 px-[7px] py-[5px] md:h-[56px] md:w-[160px] md:px-[14px] md:py-2 lg:rounded-lg xl:h-[64px] xl:w-[180px] xl:px-6 xl:py-3',
          isSelected && 'bg-white',
        )}
      >
        {isSelected && gameStream.darkLogo?.url && (
          <Image
            alt={gameStream.title ?? ''}
            className="size-full object-contain"
            height={40}
            src={gameStream.darkLogo.url}
            width={132}
          />
        )}
        {!isSelected && gameStream.lightLogo?.url && (
          <Image
            alt={gameStream.title ?? ''}
            className="size-full object-contain"
            height={40}
            src={gameStream.lightLogo.url}
            width={132}
          />
        )}
        {isClient && isLive && (
          <Badge className="bg-red-accent absolute -top-2 left-2.5 gap-0.5 text-white uppercase">
            <div className="size-1.5 animate-pulse rounded-full bg-white" />
            <span className="animate-pulse leading-[100%]">{translations?.['live'] ?? 'live'}</span>
          </Badge>
        )}
        {gameStream.tag && (
          <div className="font-primary bg-gold-primary absolute -top-2 right-2.5 flex h-[17px] w-fit max-w-[110px] items-center rounded-xs px-[5px] py-[3px] text-[10px] font-bold text-white uppercase max-xl:max-w-[90px] max-md:max-w-[50px]">
            <span className="truncate">{gameStream.tag}</span>
          </div>
        )}
        {isClient && isCompleted && (
          <Badge className="text-dark-default absolute inset-x-0 -bottom-2 mx-auto h-[17px] w-fit gap-0.5 bg-white uppercase">
            {translations?.['completed'] ?? 'COMPLETED'}
          </Badge>
        )}
        {isClient && isUpcoming && timeLeft && (
          <Badge className="text-dark-default absolute inset-x-0 -bottom-2 mx-auto h-[17px] w-fit gap-0.5 bg-white uppercase">
            <span className="hidden whitespace-nowrap md:inline">{translations?.['startsIn'] ?? 'starts in'}</span>{' '}
            <span>
              {timeLeft.days.toString().padStart(2, '0')}:{timeLeft.hours.toString().padStart(2, '0')}:
              {timeLeft.minutes.toString().padStart(2, '0')}:{timeLeft.seconds.toString().padStart(2, '0')}
            </span>
          </Badge>
        )}
      </div>

      {isSelected && (
        <div className="bg-red-accent absolute inset-x-0 bottom-0 mx-auto h-1 w-[45px] rounded-t-lg md:w-[90px] lg:h-1.5 xl:h-2" />
      )}
    </button>
  );
};
