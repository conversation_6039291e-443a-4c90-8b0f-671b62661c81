'use client';

import { FaLocationCrosshairs } from 'react-icons/fa6';
import { RxCross1 } from 'react-icons/rx';
import { useWindowSize } from 'usehooks-ts';

import { RichTextContent } from '@/blocks/RichTextBlock/RichTextContent';
import { useContentDirection } from '@/hooks/i18n';
import { JsonFieldType, RichTextContentType } from '@/strapi/types/helper';
import { ButtonType } from '@/strapi/types/shared';
import { Button } from '@/ui/components/Button';

type Props = {
  title: string;
  content: RichTextContentType;
  ctaButton: ButtonType | null;
  onClose?: () => void;
  ticketsUrl: string | null;
  translations: JsonFieldType | null | undefined;
  latitude: string | null;
  longitude: string | null;
};

export default function VenueModal({
  title,
  ticketsUrl,
  content,
  translations,
  onClose,
  ctaButton,
  longitude,
  latitude,
}: Props) {
  const { isRTL } = useContentDirection();
  const { width } = useWindowSize();
  const isFullWidth = width < 500;
  return (
    <div
      className="relative w-full max-w-[640px] rounded-xl bg-black/20 p-4 px-4 ltr:text-left rtl:text-right"
      style={{
        boxShadow: '0px 12px 16px 0px #0000000F',
        backdropFilter: 'blur(32px)',
        WebkitBackdropFilter: 'blur(32px)',
        border: '1px solid #FFFFFF1A',
        zIndex: 1000,
      }}
    >
      {onClose && (
        <button
          aria-label="Close modal"
          className={`absolute top-4 ${isRTL ? 'left-4' : 'right-4'} text-white opacity-60 transition hover:opacity-100`}
          onClick={onClose}
        >
          <RxCross1 size={18} />
        </button>
      )}

      <div className="flex max-h-[320px] flex-col justify-between gap-2 overflow-y-auto pr-2 sm:max-h-[280px]">
        <div className="flex flex-col items-start gap-1">
          {longitude && latitude && (
            <div className="flex items-center gap-1.5 text-[#E5CA49]">
              <FaLocationCrosshairs size={14} />
              <span className="font-primary text-xs">
                {latitude}, {longitude}
              </span>
            </div>
          )}

          <h3 className="text-base text-xl font-bold text-white">{title}</h3>
        </div>

        {/* Scrollable content */}
        {content && (
          <div className="my-2 flex-1 overflow-y-auto pr-2 [&_li]:text-white [&_ol]:text-white [&_ul]:text-white">
            <RichTextContent
              content={content}
              paragraphClassName="text-sm text-white text-start font-base font-normal"
            />
          </div>
        )}

        <div className="mt-2 flex flex-col gap-3 min-[450px]:flex-row min-[450px]:items-center">
          {ctaButton && (
            <Button
              {...ctaButton}
              brazeEventProperties={{
                button_name: `Primary Button - ${ctaButton?.text}`,
                location: `Interactive map - venue ${title}`,
              }}
              isFullWidth={isFullWidth}
            />
          )}

          {ticketsUrl && (
            <Button
              brazeEventProperties={{
                button_name: `Secondary Button (Buy Tickets)`,
                location: `Interactive map - venue ${title}`,
              }}
              isFullWidth={isFullWidth}
              link={ticketsUrl}
              text={translations?.['buyTickets'] ?? 'Buy tickets'}
              variant="gold"
            />
          )}
        </div>
      </div>
    </div>
  );
}
