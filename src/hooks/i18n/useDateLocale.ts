'use client';

import { format } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { ar } from 'date-fns/locale/ar';
import { zhCN } from 'date-fns/locale/zh-CN';

import { useCurrentLocale } from './index';

const localeMap = {
  ar,
  en: enUS,
  zh: zhCN,
};

export function useDateLocale() {
  const currentLocale = useCurrentLocale();
  return localeMap[currentLocale];
}

export function useDateLocaleFormatter() {
  const locale = useDateLocale();
  return (date: string, formatStr: string) => format(date, formatStr, { locale });
}
