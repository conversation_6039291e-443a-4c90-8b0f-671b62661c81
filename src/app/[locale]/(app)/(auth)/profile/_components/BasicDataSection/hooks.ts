import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { getEmailSchema } from '@/config/schema/auth';
import { User } from '@/services/auth/types';
import { JsonFieldType } from '@/strapi/types/helper';

function getBasicProfileSchema(translations?: JsonFieldType | null) {
  return yup.object({
    email: getEmailSchema(translations),
    username: yup.string().ensure(),
    name: yup.string().ensure(),
    surname: yup.string().ensure(),
    birthdate: yup.string().ensure(),
    gender: yup.string().ensure(),
  });
}

export interface BasicProfileFormValues {
  email: string;
  username: string;
  name: string;
  surname: string;
  birthdate: string;
  gender: string;
}

export function useBasicProfileForm(user: User, translations?: JsonFieldType | null) {
  return useForm<BasicProfileFormValues>({
    defaultValues: {
      email: user.email ?? '',
      username: user.preferred_username ?? '',
      name: user.given_name ?? '',
      surname: user.family_name ?? '',
      birthdate: user.birthdate ?? '',
      gender: user.gender ?? '',
    },
    resolver: yupResolver(getBasicProfileSchema(translations)),
    mode: 'onTouched',
  });
}
