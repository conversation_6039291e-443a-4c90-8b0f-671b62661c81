import { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useIntersectionObserver } from 'usehooks-ts';

import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { GameStream } from '../../../types/GameStream';

export function useScrollHandlers(
  selectedGameStream: GameStream | null,
  liveMatchesLength: number,
  upcomingMatchesLength: number,
  isLoading: boolean,
) {
  const { isSm, isMd } = useScreenType();

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const liveClusterRef = useRef<HTMLDivElement>(null);
  const upcomingClusterRef = useRef<HTMLDivElement>(null);

  const [livePosition, setLivePosition] = useState<'above' | 'below' | null>(null);
  const [scrolledFromTop, setScrolledFromTop] = useState(false);
  const [atBottom, setAtBottom] = useState(false);
  const [hasAutoScrolled, setHasAutoScrolled] = useState(false);

  const { ref: intersectionRef } = useIntersectionObserver({
    root: scrollContainerRef.current,
    threshold: 0.1,
  });

  const combinedLiveClusterRef = useCallback(
    (node: HTMLDivElement | null) => {
      liveClusterRef.current = node;
      intersectionRef(node);
    },
    [intersectionRef],
  );

  useEffect(() => {
    setLivePosition(null);
    setScrolledFromTop(false);
    setAtBottom(false);
    setHasAutoScrolled(false);
  }, [selectedGameStream]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    const live = liveClusterRef.current;
    if (!container) return;

    const handleScroll = () => {
      setScrolledFromTop(container.scrollTop > 10);
      setAtBottom(container.scrollHeight - container.scrollTop - container.clientHeight < 10);
      if (!live) return;
      const containerRect = container.getBoundingClientRect();
      const liveRect = live.getBoundingClientRect();
      if (liveRect.bottom < containerRect.top) {
        setLivePosition('above');
      } else if (liveRect.top > containerRect.bottom) {
        setLivePosition('below');
      } else {
        setLivePosition(null);
      }
    };
    handleScroll();
    container.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleScroll);
    return () => {
      container.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [liveMatchesLength]);

  const scrollToLive = () => {
    const container = scrollContainerRef.current;
    const live = liveClusterRef.current;
    if (container && live) {
      const containerRect = container.getBoundingClientRect();
      const liveRect = live.getBoundingClientRect();
      const offset =
        liveRect.top - containerRect.top + container.scrollTop - container.clientHeight / 2 + liveRect.height / 2;
      container.scrollTo({ top: offset, behavior: 'smooth' });
    }
  };

  const scrollToUpcoming = () => {
    const container = scrollContainerRef.current;
    const upcoming = upcomingClusterRef.current;
    if (container && upcoming) {
      const containerRect = container.getBoundingClientRect();
      const upcomingRect = upcoming.getBoundingClientRect();
      const offset =
        upcomingRect.top -
        containerRect.top +
        container.scrollTop -
        container.clientHeight / 2 +
        upcomingRect.height / 2;
      container.scrollTo({ top: offset, behavior: 'smooth' });
    }
  };

  useLayoutEffect(() => {
    const container = scrollContainerRef.current;
    const live = liveClusterRef.current;
    const upcoming = upcomingClusterRef.current;
    const isLargeScreen = !(isSm || isMd);

    if (!container) return;
    if (isLoading) return;
    if (hasAutoScrolled) return;

    if (isLargeScreen && liveMatchesLength > 0) {
      scrollToLive();
    } else if (isLargeScreen && upcomingMatchesLength > 0) {
      scrollToUpcoming();
    } else {
      if (liveMatchesLength > 0 && live) {
        const containerRect = container.getBoundingClientRect();
        const liveRect = live?.getBoundingClientRect();
        const scrollOffset = liveRect.left - containerRect.left + container.scrollLeft;
        container.scrollTo({ left: scrollOffset, behavior: 'smooth' });
      } else if (upcomingMatchesLength > 0 && upcoming) {
        const containerRect = container.getBoundingClientRect();
        const upcomingRect = upcoming.getBoundingClientRect();
        const scrollOffset = upcomingRect.left - containerRect.left + container.scrollLeft;
        container.scrollTo({ left: scrollOffset, behavior: 'smooth' });
      }
    }

    setHasAutoScrolled(true);
  }, [isLoading, hasAutoScrolled, isSm, isMd, liveMatchesLength, upcomingMatchesLength]);

  return {
    scrollContainerRef,
    combinedLiveClusterRef,
    upcomingClusterRef,
    livePosition,
    scrolledFromTop,
    atBottom,
    scrollToLive,
  };
}
