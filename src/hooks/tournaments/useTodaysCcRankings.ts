import { useMemo } from 'react';

import { getToday } from '@/app/[locale]/(app)/club-championship-ranking/_components/utils';
import { calculateRankingsForDay } from '@/app/[locale]/(app)/club-championship-ranking/_utils/calculate-historical-rankings';
import { useTournamentsData } from '@/services/graphql/hooks';
import { Tournament } from '@/services/graphql/types/tournament';
import { GameWithCompetitions } from '@/strapi/api/collection/game';

export function useTodaysCcRankings(gameCompetitions: GameWithCompetitions[]) {
  const tournamentIds = gameCompetitions.flatMap((gc) => gc.competitionSlugs.map((cs) => cs.competitionId));
  const { data: tournamentsData, loading } = useTournamentsData(tournamentIds);

  const todaysCCRankings = useMemo(
    () => calculateRankingsFromTournamentsData(tournamentsData?.tournaments.result ?? [], gameCompetitions),
    [gameCompetitions, tournamentsData?.tournaments.result],
  );

  return { todaysCCRankings, loading };
}

export function calculateRankingsFromTournamentsData(
  tournaments: Tournament[],
  gameCompetitions: GameWithCompetitions[],
) {
  const today = getToday();
  return calculateRankingsForDay(tournaments, today, gameCompetitions);
}
