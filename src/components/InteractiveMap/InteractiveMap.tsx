'use client';

import clsx from 'clsx';
import { useLayoutEffect, useRef, useState } from 'react';
import { ReactZoomPanPinchRef, TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';

import { EwcRibbonBlock } from '@/app/[locale]/(app)/2024/_components/EwcRibbonBlock';
import { useVenueState } from '@/components/InteractiveMap/hooks/useVenueState';
import MapBorders from '@/components/InteractiveMap/MapBorders';
import MapCompass from '@/components/InteractiveMap/MapCompass';
import MapTitle from '@/components/InteractiveMap/MapTitle';
import { createLocalMedia } from '@/components/InteractiveMap/utils/createLocalMedia';
import { findVenueById } from '@/components/InteractiveMap/utils/findVenueById';
import { getInitialTransform } from '@/components/InteractiveMap/utils/getInitialTransform';
import { getTooltipData } from '@/components/InteractiveMap/utils/getTooltipData';
import VenueModal from '@/components/InteractiveMap/VenueModal';
import VenueOverlay from '@/components/InteractiveMap/VenueOverlay';
import { mapBase, VenueMapConfig, venueMapConfigs } from '@/components/InteractiveMap/venues';
import VenueTooltip from '@/components/InteractiveMap/VenueTooltip';
import { useContentDirection } from '@/hooks/i18n';
import { VenueData } from '@/strapi/types/collection/festival';
import { RibbonType } from '@/strapi/types/collection/ribbon';
import { JsonFieldType } from '@/strapi/types/helper';

export default function InteractiveMap({
  venueData,
  translations,
  showRibbon,
}: {
  venueData: VenueData[];
  translations?: JsonFieldType | null | undefined;
  showRibbon: boolean;
}) {
  const { clickedVenueId, setClickedVenueId, hoveredVenueId, handleHoverEnter, handleHoverLeave, isHoverable } =
    useVenueState(venueData);

  const tooltip = getTooltipData(hoveredVenueId, venueData, venueMapConfigs);
  const tooltipVenue = findVenueById(venueData, tooltip?.id ?? null);
  const modalVenue = findVenueById(venueData, clickedVenueId);

  const transformRef = useRef<ReactZoomPanPinchRef | null>(null);

  const [initialScale, setInitialScale] = useState(() => getInitialTransform().scale);
  const [positionX, setPositionX] = useState(() => getInitialTransform().positionX);
  const [positionY, setPositionY] = useState(() => getInitialTransform().positionY);
  const lastVerticalCoordinateRef = useRef<number>(undefined);

  useLayoutEffect(() => {
    const handleResize = () => {
      const { scale, positionX, positionY } = getInitialTransform();
      setInitialScale(scale);
      setPositionX(positionX);
      setPositionY(positionY);

      requestAnimationFrame(() => {
        transformRef.current?.setTransform(positionX, positionY, scale);
      });
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toPctX = (val: number) => `${(val / mapBase.width) * 100}%`;
  const toPctY = (val: number) => `${(val / mapBase.height) * 100}%`;
  const { isRTL } = useContentDirection();

  return (
    <>
      <div className="relative mx-auto w-full max-w-[2620px]">
        <MapBorders />
        <MapCompass />
        <MapTitle
          subtitle={translations?.['interactiveMapSubtitle'] ?? 'Click on Venues to get more info.'}
          title={translations?.['interactiveMapTitle'] ?? "EWC '25 <br /> Interactive map"}
        />

        <div
          className="relative flex w-full items-center justify-center overflow-hidden bg-black"
          onTouchEndCapture={() => {
            lastVerticalCoordinateRef.current = undefined;
          }}
          onTouchMoveCapture={(event) => {
            const touch = event.touches[0];
            if (!touch) return;

            const newY = touch.clientY;
            const oldY = lastVerticalCoordinateRef.current;

            if (oldY) {
              const dy = newY - oldY;
              window.scrollBy({
                top: -dy,
                behavior: 'instant',
              });
            }

            lastVerticalCoordinateRef.current = newY;
          }}
        >
          <TransformWrapper
            centerZoomedOut={false}
            disablePadding
            doubleClick={{ disabled: true }}
            initialPositionX={positionX}
            initialPositionY={positionY}
            initialScale={initialScale}
            limitToBounds={false}
            maxScale={initialScale}
            minScale={initialScale}
            panning={{ velocityDisabled: true, lockAxisY: true }}
            pinch={{ disabled: true }}
            ref={transformRef}
            wheel={{ disabled: true }}
            zoomAnimation={{ disabled: true }}
            onPanning={({ state }) => {
              transformRef.current?.setTransform(state.positionX, positionY, initialScale);
            }}
            onPanningStop={({ state }) => {
              transformRef.current?.setTransform(state.positionX, positionY, initialScale);
            }}
            onZoomStop={({ state }) => {
              if (state.scale !== initialScale) {
                requestAnimationFrame(() => {
                  transformRef.current?.setTransform(state.positionX, positionY, initialScale);
                });
              }
            }}
          >
            <TransformComponent>
              <div className="max-h-[80vh] w-full touch-none overflow-hidden overscroll-none">
                <div className="relative mx-auto aspect-[2620/1474] min-h-[68vh] max-w-none lg:w-[85vw]">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    alt="Map"
                    className="absolute inset-0 h-full w-full object-contain"
                    src="/assets/map/interactive-map.png"
                  />
                  <div className="absolute inset-0">
                    {tooltip && tooltipVenue && (
                      <VenueTooltip
                        subtext={translations?.['clickForMore'] ?? 'Click for more'}
                        text={tooltipVenue.name}
                        venueId={tooltipVenue.venueId}
                        x={tooltip.x}
                        y={tooltip.y}
                        onHoverEndAction={handleHoverLeave}
                        onHoverStartAction={handleHoverEnter}
                        onTooltipClickAction={setClickedVenueId}
                      />
                    )}

                    {venueMapConfigs
                      .filter((venue) => venueData.some((v) => v.venueId === venue.id))
                      .map((venue: VenueMapConfig) => (
                        <VenueOverlay
                          handleHoverEnter={handleHoverEnter}
                          handleHoverLeave={handleHoverLeave}
                          isClicked={clickedVenueId === venue.id}
                          isHoverable={isHoverable}
                          isHovered={hoveredVenueId === venue.id}
                          key={venue.id}
                          setClickedVenue={setClickedVenueId}
                          toPctX={toPctX}
                          toPctY={toPctY}
                          venue={venue}
                        />
                      ))}
                  </div>
                </div>
              </div>
            </TransformComponent>
          </TransformWrapper>

          {modalVenue && (
            <div
              className={clsx(
                'absolute bottom-8 z-50 w-[95vw]',
                isRTL
                  ? 'right-1/2 translate-x-1/2 md:right-4 md:translate-x-0 lg:right-12'
                  : 'left-1/2 -translate-x-1/2 md:left-4 md:translate-x-0 lg:left-12',
              )}
            >
              <VenueModal
                content={modalVenue.content}
                ctaButton={modalVenue.mapCtaButton}
                latitude={modalVenue.latitude}
                longitude={modalVenue.longitude}
                ticketsUrl={modalVenue.ticketsUrl}
                title={modalVenue.name}
                translations={translations}
                onClose={() => setClickedVenueId(null)}
              />
            </div>
          )}

          <div className="pointer-events-none absolute right-0 bottom-0 left-0 h-24 bg-gradient-to-t from-black to-transparent" />
        </div>
      </div>

      {showRibbon && (
        <EwcRibbonBlock
          ribbon={
            {
              logoPrimary: createLocalMedia('/assets/logos/ewc.svg'),
              logoSecondary: createLocalMedia('/assets/logos/ewc-acronym.svg'),
              background: '#ffffff',
            } as RibbonType
          }
        />
      )}
    </>
  );
}
