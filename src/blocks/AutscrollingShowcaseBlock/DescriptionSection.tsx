import { AutoscrollingShowcaseType } from '@/strapi/types/block';
import { Button } from '@/ui/components/Button';

export const DescriptionSection = ({
  title,
  subtitle,
  description,
  button,
}: Pick<AutoscrollingShowcaseType, 'title' | 'subtitle' | 'description' | 'button'>) => {
  return (
    <div className="flex flex-col justify-start gap-4">
      <h2 className="text-h3 text-dark-default">{title}</h2>
      {subtitle && <p className="text-gray-dark text-subtitle">{subtitle}</p>}
      {description && <p className="text-dark-default text-paragraph">{description}</p>}
      {button && (
        <Button
          {...button}
          brazeEventProperties={{
            button_name: `Primary Button (${button.text})`,
            location: `Autoscrolling Showcase Block (${title})`,
          }}
        />
      )}
    </div>
  );
};
