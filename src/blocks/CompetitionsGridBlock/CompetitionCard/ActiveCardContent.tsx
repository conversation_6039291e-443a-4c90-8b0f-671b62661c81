'use client';

import { StrapiImage } from '@/components/StrapiImage';
import { useCurrentLocale } from '@/hooks/i18n';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { GameType } from '@/strapi/types/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';

import { CardLogo } from './CardLogo';
import { TextSection } from './TextSection';
import { formatDateRange } from './utils/formatDateRange';

export interface CardContentProps {
  game: GameType;
  translations: JsonFieldType;
}

export function ActiveCardContent({ game, translations }: CardContentProps) {
  const locale = useDateLocale();
  const currentLocale = useCurrentLocale();
  const isChineseLocale = currentLocale === 'zh';

  return (
    <div className="flex flex-col items-center">
      {game.keyArt && <StrapiImage className="h-10 object-cover md:h-20" image={game.keyArt} />}
      {game.gradientHex && <div className="h-1 w-full shrink-0" style={{ background: game.gradientHex }} />}
      <div className="mt-[9px] flex w-full flex-col items-center gap-1 px-2 md:mt-1.5 md:gap-[9px] md:px-6">
        <CardLogo logo={game.logoDark} />
        <div className="text-dark-default border-gray flex w-full flex-col">
          {game.tournamentStart && game.tournamentEnd && (
            <TextSection
              title={translations['startEnd'] ?? 'Start - End'}
              value={formatDateRange(
                new Date(game.tournamentStart),
                new Date(game.tournamentEnd),
                locale,
                isChineseLocale,
              )}
            />
          )}
          {game.prizePool && <TextSection title={translations['prizePool'] ?? 'Prize Pool'} value={game.prizePool} />}
          {game.competingTeams && (
            <TextSection
              title={translations['participatingClubs'] ?? 'Participating Clubs'}
              value={game.competingTeams}
            />
          )}
          {game.competingPlayers && (
            <TextSection
              title={translations['participatingPlayers'] ?? 'Participating Players'}
              value={game.competingPlayers}
            />
          )}
          {game.numQualifiers && (
            <TextSection title={translations['qualifiers'] ?? 'Qualifiers'} value={game.numQualifiers} />
          )}
        </div>
      </div>
    </div>
  );
}
