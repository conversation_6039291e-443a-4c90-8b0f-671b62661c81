'use client';

import clsx from 'clsx';
import { FormEvent, useState } from 'react';
import { MdOutlineWarningAmber } from 'react-icons/md';

import { useDeleteCurrentUser } from '@/services/auth/hooks';
import { logButtonClickedEvent } from '@/services/braze';
import { ProfilePageData } from '@/strapi/api/single/profile/types';
import { TextInput } from '@/ui/components/TextInput';
import { showErrorToast } from '@/ui/components/Toast';

import { ProfilePageSectionContainer } from './ProfilePageSectionContainer';

export const DeleteAccountSection = ({ deleteAccount }: ProfilePageData) => {
  const [isDeleteExpanded, setIsDeleteExpanded] = useState(false);
  const [confirmationValue, setConfirmationValue] = useState('');
  const deleteCurrentUser = useDeleteCurrentUser();

  async function handleDeleteUser(e: FormEvent) {
    e.preventDefault();
    try {
      await deleteCurrentUser();
    } catch (error: any) {
      showErrorToast({ title: 'Delete user error!', description: error.message });
    }
  }

  const requiredConfirmationValue = deleteAccount?.deleteConfirmationValue ?? 'I AGREE';
  const isConfirmed = confirmationValue === requiredConfirmationValue;
  return (
    <ProfilePageSectionContainer>
      <div className="flex flex-col">
        <h3 className="text-h5">{deleteAccount?.title ?? 'Delete Account'}</h3>
        <p className="text-paragraph">
          {deleteAccount?.subtitle ?? 'Leaving? We’ll miss you. But if you’re sure, you can delete your account'}{' '}
          <span className="text-gold-primary cursor-pointer underline" onClick={() => setIsDeleteExpanded(true)}>
            {deleteAccount?.subtitleDeleteCtaText ?? 'here'}
          </span>
        </p>
      </div>
      {isDeleteExpanded && (
        <>
          <div className="bg-gray h-px" />
          <div className="flex flex-col gap-4">
            <p className="text-paragraph text-dark-default">
              {deleteAccount?.body ??
                'You can delete your Account at any time. If you change your mind, you might not be able to recover it after a certain amount of time.'}
            </p>
            <div className="flex flex-col gap-4 rounded-sm border border-[#AD38381A] bg-[#AD38380D] p-4">
              <div className="flex items-center gap-2">
                <MdOutlineWarningAmber className="size-[22px] text-[#AD3838]" />
                <p className="font-primary text-lg leading-none font-bold text-[#AD3838] xl:text-[21px]">
                  {deleteAccount?.alertTitle ?? 'Warning!'}
                </p>
              </div>
              <p className="font-base text-base font-normal text-[#AD3838]">
                {deleteAccount?.alertBody ??
                  'By proceeding, your account and all associated data will be removed. This includes your personal data, your favorites, your settings and data from all services connected with this Account.'}
              </p>
            </div>
          </div>
          <div className="bg-gray h-px" />
          <form className="flex gap-4 max-md:flex-col" onSubmit={handleDeleteUser}>
            <TextInput
              error={deleteAccount?.deleteInputInfoText ?? undefined}
              value={confirmationValue}
              onChange={setConfirmationValue}
            />
            <button
              className={clsx(
                'flex h-full items-center justify-center rounded-[14px] px-9 py-[22px] xl:px-10 xl:py-[22.5px]',
                'cursor-pointer bg-[#AD3838] transition-opacity disabled:cursor-auto disabled:opacity-70',
              )}
              disabled={!isConfirmed}
              type="submit"
              onClick={() =>
                logButtonClickedEvent({ location: 'Delete Account Section', button_name: `Delete Account` })
              }
            >
              <span className="text-button-default whitespace-nowrap text-white">
                {deleteAccount?.deleteButtonText ?? 'confirm & delete'}
              </span>
            </button>
          </form>
        </>
      )}
    </ProfilePageSectionContainer>
  );
};
