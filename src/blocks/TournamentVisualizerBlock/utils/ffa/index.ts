import { MatchSeries } from '@/services/graphql/types/matchSeries';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { Config } from '../types';
import { createGroup, getGroupDescriptor, insertOverallMatchInGroup } from './helpers';
import { FfaGroup } from './types';

export function getFfaViewGroups(matchSeries: MatchSeries[], config: Config) {
  const groups: FfaGroup[] = [];
  for (const serie of matchSeries) {
    const descriptor = getGroupDescriptor(serie.metadata, config.apiTranslations, serie.id);

    const group = createGroup(serie, descriptor, config);
    insertOverallMatchInGroup(group, serie.contestants, config.apiTranslations);
    groups.push(group);
  }

  groups.sort((a, b) => a.position - b.position);
  patchGroupsStatus(groups);

  return groups;
}

function patchGroupsStatus(groups: FfaGroup[]) {
  groups.forEach((g) => {
    const hasLiveMatches = g.matches.some((m) => m.status === LocalMatchStatus.LIVE);
    if (hasLiveMatches) {
      g.status = LocalMatchStatus.LIVE;
      return;
    }

    const hasOpenMatches = g.matches.some((m) => m.status === LocalMatchStatus.UPCOMING);
    if (hasOpenMatches) {
      g.status = LocalMatchStatus.UPCOMING;
      return;
    }

    g.status = LocalMatchStatus.COMPLETED;
  });
}
