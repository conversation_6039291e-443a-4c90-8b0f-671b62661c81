'use client';

import { useState } from 'react';

import { ClubModal } from '@/components/ClubModal/ClubModal';
import { useTournamentsData } from '@/services/graphql/hooks/tournaments';
import { ParticipatingTeamsBlockType } from '@/strapi/types/block';
import { ClubType } from '@/strapi/types/collection/club';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { GridItem } from './GridItem';

export const ParticipatingTeamsBlock = ({
  section,
  gridItems,
  games,
  translations,
  featureFlags,
}: ParticipatingTeamsBlockType) => {
  const competitionIds = games.flatMap((g) => g.competitionSlugs.map((c) => c.competitionId));
  const { data } = useTournamentsData(competitionIds, { isSubscriptionDisabled: true });

  const [selectedClub, setSelectedClub] = useState<ClubType | null>(null);

  return (
    <>
      <BlockSectionWrapper
        {...section}
        className="relative mx-auto w-full max-w-6xl px-4 py-[50px] max-md:px-0 lg:px-8 lg:py-[90px] [&_.section-title]:max-md:px-4"
      >
        <div className="hide-scrollbar grid w-full auto-rows-fr gap-2 overflow-x-auto py-4 max-md:grid-flow-col max-md:grid-rows-2 max-md:px-4 max-md:pt-10 md:grid-cols-4 md:gap-5 md:overflow-visible lg:grid-cols-5 xl:grid-cols-7">
          {gridItems.map((item) => {
            return (
              <GridItem
                item={item}
                key={item.id}
                onClick={featureFlags?.isClubModalDisabled ? undefined : () => setSelectedClub(item.club)}
              />
            );
          })}
        </div>
      </BlockSectionWrapper>
      {selectedClub && (
        <ClubModal
          allGames={games}
          allTournaments={data?.tournaments.result ?? []}
          club={selectedClub}
          translations={translations ?? {}}
          onClose={() => setSelectedClub(null)}
        />
      )}
    </>
  );
};
