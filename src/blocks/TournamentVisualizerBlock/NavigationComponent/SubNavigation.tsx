import clsx from 'clsx';

import { DEFAULT_META_NAME } from '../utils/const';
import { NavigationProps } from './Navigation';

export const SubNavigation = ({ items, selectedItemId, onSelect }: Omit<NavigationProps, 'type'>) => {
  const isSingleDefaultItem = items.length === 1 && items[0].name === DEFAULT_META_NAME;
  if (isSingleDefaultItem) {
    return null;
  }

  return (
    <div className="bg-white p-2">
      <div className="flex gap-1 overflow-x-auto md:gap-2">
        {items.map((i) => (
          <button
            className={clsx(
              'font-primary rounded-sm px-2.5 py-2 text-xs leading-tight font-bold whitespace-nowrap capitalize md:rounded-lg md:px-[14px] md:py-2.5 md:text-sm',
              i.id === selectedItemId ? 'bg-dark-default text-white' : 'text-dark-default cursor-pointer',
            )}
            key={i.id}
            onClick={i.id !== selectedItemId ? () => onSelect(i.id) : undefined}
          >
            {i.name}
          </button>
        ))}
      </div>
    </div>
  );
};
