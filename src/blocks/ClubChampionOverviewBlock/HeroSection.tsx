import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';
import { MediaType } from '@/strapi/types/media';
import { default as Ewc<PERSON>ogo } from '@/ui/assets/icons/logos/ewc.svg';

interface Props {
  text: string | null;
  heroImage: MediaType | null;
  logoUrl: string | null;
}

export const HeroSection = ({ heroImage, logoUrl, text }: Props) => {
  return (
    <div className="relative aspect-[0.7] w-full overflow-clip rounded-lg md:aspect-[2] md:rounded-2xl lg:rounded-4xl">
      {heroImage && (
        <StrapiImage
          alt=""
          className="absolute inset-0 size-full object-cover object-bottom"
          height={500}
          image={heroImage}
          width={1000}
        />
      )}
      <div className="from-dark-default/0 to-dark-default/80 relative size-full bg-gradient-to-b" />
      <div className="absolute inset-x-0 bottom-[15px] flex flex-col items-center gap-4 md:bottom-[26px] lg:bottom-[37px] lg:gap-6">
        {logoUrl && (
          <Image
            alt="Winning club logo"
            className="size-[106px] object-contain md:size-[124px] lg:size-[180px]"
            height={180}
            src={logoUrl}
            width={180}
          />
        )}
        <EwcLogo className="w-1/2 max-w-[554px] min-w-[240px] text-white" />
        {text && (
          <p className="font-primary text-sm leading-none font-bold text-white uppercase md:text-lg lg:text-[28px]">
            {text}
          </p>
        )}
      </div>
    </div>
  );
};
