'use client';

import clsx from 'clsx';
import { useIsClient, useLocalStorage } from 'usehooks-ts';

import { RichTextContent } from '@/blocks/RichTextBlock/RichTextContent';
import { CookieConsentModalType } from '@/strapi/api/single/cookieConsentModal';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { Button } from '@/ui/components/Button';
import { WEBVIEW_HIDE_CLASS } from '@/utils/webviewHideScript';

interface CookieConsentModalAdditionalProps {
  siteConfig: SiteConfigData | null;
}

export const CookieConsentModal = (props: CookieConsentModalType & CookieConsentModalAdditionalProps) => {
  const [, setAcceptCookies] = useLocalStorage('accept-cookies', false);
  const [isDismissed, setIsDismissed] = useLocalStorage('cookie-consent-dismissed', false);

  const isClient = useIsClient();

  const translations = props.siteConfig?.translations;

  const handleClick = (cookiesAccepted: boolean) => {
    setAcceptCookies(cookiesAccepted);
    setIsDismissed(true);
  };

  if (isDismissed || !isClient) return;

  return (
    <div
      className={clsx(
        'fixed bottom-22 z-90 mx-4 h-fit w-[calc(100%_-_32px)] rounded-2xl bg-white px-8 py-6 shadow-md lg:bottom-8 lg:mx-0 lg:w-fit lg:max-w-[784px] lg:ltr:right-8 lg:rtl:left-8',
        WEBVIEW_HIDE_CLASS,
      )}
    >
      <div className="flex flex-col gap-8 lg:flex-row lg:items-center">
        <div className="flex flex-col gap-1">
          {props.title && <span className="font-primary text-lg font-bold">{props.title}</span>}
          {props.description && <RichTextContent content={props.description} />}
        </div>
        <div className="flex items-center gap-4">
          <Button
            brazeEventProperties={{ button_name: 'Accept Cookies', location: 'Cookie Consent Modal' }}
            isFullWidth
            text={translations?.['accept'] ?? 'Accept'}
            variant="primary"
            onClick={() => handleClick(true)}
          />
          <Button
            brazeEventProperties={{ button_name: 'Decline Cookies', location: 'Cookie Consent Modal' }}
            isFullWidth
            text={translations?.['decline'] ?? 'Decline'}
            variant="secondary"
            onClick={() => handleClick(false)}
          />
        </div>
      </div>
    </div>
  );
};
