import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';

import { RoundRobinContestant } from '@/blocks/TournamentVisualizerBlock/utils/roundRobin/types';
import PlaceholderLogo from '@/ui/assets/images/ewc-placeholder.png';

type ContestantRowProps = RoundRobinContestant & { rank: number };

export const ContestantRow = ({
  name,
  rank,
  logoUrl,
  wins,
  draws,
  losses,
  pointsScored,
  pointsLost,
  diff,
}: ContestantRowProps) => {
  return (
    <div
      className={clsx('flex items-center rounded-sm border-s-[3px] bg-white px-2.5 py-1', {
        'border-[#0FE5F4]': rank === 1,
        'border-[#0FF431]': [2, 3].includes(rank),
        'border-[#F40F30]': rank === 4,
      })}
    >
      <div className="font-primary text-dark-default flex grow items-center gap-2 text-xs leading-none font-bold uppercase">
        <p>{rank}.</p>
        <Image alt={`${name} logo`} className="size-5" height={24} src={logoUrl ?? PlaceholderLogo} width={24} />
        <p>{name}</p>
      </div>
      <div className="font-primary flex gap-2 text-xs leading-none font-bold uppercase">
        <p className="w-9 text-end">
          {wins}-{draws}-{losses}
        </p>
        <p className="w-[30px] text-end">
          {pointsScored}-{pointsLost}
        </p>
        <p className="w-3 text-end">{diff > 0 ? `+${diff}` : diff < 0 ? `${diff}` : 0}</p>
      </div>
    </div>
  );
};
