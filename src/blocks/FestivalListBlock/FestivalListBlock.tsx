'use client';

import clsx from 'clsx';
import { format } from 'date-fns';

import { isDayInRange } from '@/app/[locale]/(app)/schedule/_components/utils';
import { StrapiImage } from '@/components/StrapiImage';
import { useFestivalFilter } from '@/context/FestivalFilterContext';
import { FestivalListBlockType } from '@/strapi/types/festivalBlock';
import EsportIcon from '@/ui/assets/icons/esport-icon.svg';
import { Button } from '@/ui/components/Button';

import { RichTextContent } from '../RichTextBlock/RichTextContent';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const FestivalListBlock = ({ section, festival, translations }: FestivalListBlockType) => {
  const { selectedVenueId, selectedDate } = useFestivalFilter();
  const filteredFestivals = festival.filter((f) => {
    const matchesVenue = !selectedVenueId || (f.venues && f.venues.some((v) => v.id === selectedVenueId));
    const start = f.startDateTime ?? f.startDate;
    const end = f.endDateTime ?? f.endDate;
    let matchesDate =
      !selectedDate || (start && end && isDayInRange(new Date(selectedDate), start, end)) || start === selectedDate;

    if (matchesDate && selectedDate && f.excludedDays && f.excludedDays.length > 0) {
      const selectedWeekday = format(new Date(selectedDate), 'EEEE').toLowerCase();
      if (f.excludedDays.map((d) => d.toLowerCase()).includes(selectedWeekday)) {
        matchesDate = false;
      }
    }
    return matchesVenue && matchesDate;
  });
  return (
    <BlockSectionWrapper {...section} styleConfig={{ paddingTop: 80 }}>
      {filteredFestivals.length === 0 ? (
        <div className="font-riforma flex min-h-[120px] items-center justify-center py-12 text-center text-lg text-gray-400">
          {translations?.['noFestivalEvents'] || 'No events found for the selected filters.'}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-4">
          {filteredFestivals.map((f, index) => {
            if (!f.title || !f.slug) return null;

            return (
              <div
                className={clsx(
                  'relative w-full overflow-hidden rounded-lg shadow-md md:rounded-2xl lg:rounded-4xl',
                  'flex flex-col justify-between',
                  'h-full',
                  f.type === 'esport' ? 'bg-dark-default' : 'bg-white',
                )}
                key={`${index}-${f.id}`}
              >
                {f.type && (
                  <div className="absolute inset-4 z-10 flex h-fit gap-1">
                    {f.type === 'esport' && (
                      <div className="text-dark-default flex h-[18px] w-fit items-center justify-center rounded-sm bg-white p-1 leading-[110%]">
                        <EsportIcon className="h-[11px] w-[11px]" />
                      </div>
                    )}
                    <div className="text-dark-default h-min max-h-[18px] w-min rounded-sm bg-white px-1 leading-[110%]">
                      <span className="font-riforma text-sm uppercase md:text-xs lg:text-sm">
                        {translations?.[f.type] ?? f.type}
                      </span>
                    </div>
                  </div>
                )}
                <div className="relative aspect-[16/9] w-full bg-gray-100">
                  {f.posterImage && (
                    <StrapiImage alt={f.title ?? ''} className="h-full w-full object-cover" image={f.posterImage} />
                  )}
                </div>
                <div className="flex flex-1 flex-col gap-4 p-4 md:p-3 lg:px-6 lg:py-[22px]">
                  <div className="flex flex-col gap-1">
                    <h2
                      className={clsx(
                        'font-riforma text-lg font-bold md:text-[16px] lg:text-2xl',
                        f.type === 'esport' ? 'text-white' : 'text-dark-default',
                      )}
                    >
                      {f?.title}
                    </h2>
                    <span className="text-gold-primary font-riforma line-clamp-2 text-xs font-bold lg:text-sm">
                      {f?.subtitle}
                    </span>
                  </div>
                  {/* <p className="text-gray-dark font-base line-clamp-3 text-xs lg:text-sm"> */}
                  <RichTextContent
                    content={f?.description ?? []}
                    paragraphClassName="text-gray-dark font-base line-clamp-3 text-xs lg:text-sm"
                  />
                  {/* </p> */}
                  <div className="flex-1" />
                  <div className="flex w-full flex-col gap-2">
                    {f.buttonText && (
                      <Button
                        brazeEventProperties={{
                          button_name: f?.buttonText ?? '',
                          location: `Festival Page - ${f?.title ?? ''}`,
                        }}
                        isFullWidth
                        link={`/festival/${f?.slug}`}
                        text={f?.buttonText}
                        variant={f.type === 'esport' ? 'secondary' : 'primary'}
                      />
                    )}
                    {f.ticketsUrl && (
                      <Button
                        brazeEventProperties={{
                          button_name: 'Buy tickets',
                          location: `Festival Page - Buy tickets`,
                        }}
                        isFullWidth
                        link={f?.ticketsUrl ?? ''}
                        text={translations?.['buyTickets'] ?? 'Buy tickets'}
                        variant={f.type === 'esport' ? 'gold' : 'secondary'}
                      />
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </BlockSectionWrapper>
  );
};
