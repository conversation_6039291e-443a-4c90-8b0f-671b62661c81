interface Props {
  numberOfCols: number;
  columnWidth: number;
}

export const GridColumnSeparatorUnderlay = ({ numberOfCols, columnWidth }: Props) => {
  return (
    <div className="absolute inset-0 flex h-full divide-x divide-[#DBDBDB]">
      {[...Array(numberOfCols)].map((_, i) => (
        <div className="border-[#DBDBDB] first:border-s" key={i} style={{ width: `${columnWidth}px` }} />
      ))}
    </div>
  );
};
