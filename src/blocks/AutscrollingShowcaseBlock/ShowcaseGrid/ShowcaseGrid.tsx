'use client';

import { useState } from 'react';
import { useIsClient } from 'usehooks-ts';

import { AutoscrollingShowcaseType } from '@/strapi/types/block';
import { ClubType } from '@/strapi/types/collection/club';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { ShowcaseGridRow } from './ShowcaseGridRow';

export const ShowcaseGrid = ({ clubs }: Pick<AutoscrollingShowcaseType, 'clubs'>) => {
  const [isPaused, setIsPaused] = useState(false);
  const isClient = useIsClient();
  const { isSm, isMd } = useScreenType();

  if (!clubs?.length) {
    return null;
  }

  const splitItems = splitArrayIntoThree(clubs);
  return (
    <div
      className="pointer-events-none flex w-full shrink-0 flex-col lg:pointer-events-auto lg:w-2/5 xl:w-1/2 2xl:w-3/5"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
    >
      {isSm || isMd ? (
        <ShowcaseGridRow isLoading={!isClient} isPaused={isPaused} isRTL={false} items={clubs} />
      ) : (
        <>
          {new Array(3).fill(undefined).map((_, index) => {
            return (
              <ShowcaseGridRow
                isLoading={!isClient}
                isPaused={isPaused}
                isRTL={index % 2 === 0}
                items={splitItems[index]}
                key={index}
              />
            );
          })}
        </>
      )}
    </div>
  );
};

function splitArrayIntoThree(arr: ClubType[]) {
  const total = arr.length;
  const base = Math.floor(total / 3);
  const remainder = total % 3;

  const sizes = [base + (remainder > 0 ? 1 : 0), base + (remainder > 1 ? 1 : 0), base];

  const first = arr.slice(0, sizes[0]);
  const second = arr.slice(sizes[0], sizes[0] + sizes[1]);
  const third = arr.slice(sizes[0] + sizes[1]);

  return [first, second, third];
}
