import * as yup from 'yup';

import { getEmailSchema, getPasswordConfirmSchema, getPasswordSchema } from '@/config/schema/auth';
import { JsonFieldType } from '@/strapi/types/helper';

export function getSignupFormSchema(translations?: JsonFieldType | null) {
  return yup
    .object({
      email: getEmailSchema(translations),
      name: yup.string().required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.'),
      surname: yup.string().required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.'),
      username: yup.string().required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.'),
      password: getPasswordSchema(translations),
      passwordConfirm: getPasswordConfirmSchema(translations),
      country: yup.string().required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.'),
      termsAccepted: yup
        .boolean()
        .required()
        .test({ name: 'are-terms-accepted', test: (value) => value, message: 'Terms and condition must be accepted.' }),
      newsAccepted: yup.boolean().required(),
      marketingAccepted: yup.boolean().required(),
    })
    .required();
}
