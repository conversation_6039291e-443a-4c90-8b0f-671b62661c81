interface Props {
  text?: string | null;
  button: React.ReactNode;
}

export const SimpleStreamDetails = ({ text, button }: Props) => {
  return (
    <div className="bg-dark-default flex flex-col justify-between gap-4 p-4 md:flex-row xl:px-8 xl:py-6 [&>a]:md:w-fit">
      {text && <h4 className="font-primary text-[24px] leading-[100%] font-bold text-white xl:text-[34px]">{text}</h4>}
      {button}
    </div>
  );
};
