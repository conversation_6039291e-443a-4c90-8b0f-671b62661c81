import { JsonFieldType } from '@/strapi/types/helper';

import { Bracket } from '../../utils/brackets/types';
import { BracketGrid } from './components/BracketGrid';

interface Props {
  brackets: Bracket[];
  translations: JsonFieldType;
}

export const BracketView = ({ brackets, translations }: Props) => {
  return (
    <div className="flex flex-col gap-9 md:gap-[50px]">
      {brackets.map((b) => (
        <BracketGrid {...b} key={b.id} translations={translations} />
      ))}
    </div>
  );
};
