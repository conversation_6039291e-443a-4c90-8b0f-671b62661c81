'use client';

import { usePathname } from 'next/navigation';

import { Locale } from './const';
import { useCurrentLocale } from './useCurrentLocale';

export function useContentDirection() {
  const currentLocale = useCurrentLocale();

  const isRTL = currentLocale === 'ar';
  const isLTR = currentLocale !== 'ar';
  const contentDirection = isRTL ? 'rtl' : 'ltr';

  return { isRTL, isLTR, contentDirection };
}

export function useReplaceLocale() {
  const currentLocale = useCurrentLocale();
  const pathname = usePathname();

  function replaceLocale(newLocale: Locale) {
    const updatedPathname = pathname.replace(currentLocale, newLocale);
    // Perform a full page reload to ensure the server sets the locale cookie,
    // which is necessary for persisting the user's locale preference across sessions
    return window.location.assign(updatedPathname);
  }

  return replaceLocale;
}

export { useCurrentLocale };
