import { MediaType } from '@/strapi/types/media';

export const createLocalMedia = (path: string): MediaType => ({
  id: 0,
  documentId: '',
  createdAt: '',
  updatedAt: '',
  publishedAt: '',
  locale: 'en',

  alternativeText: null,
  caption: null,
  formats: null,
  previewUrl: path,
  provider: 'local',

  name: '',
  hash: '',
  ext: '',
  mime: 'image/svg+xml',
  path: null,
  width: 0,
  height: 0,
  size: 0,
  sizeInBytes: 0,
  url: path,
});
