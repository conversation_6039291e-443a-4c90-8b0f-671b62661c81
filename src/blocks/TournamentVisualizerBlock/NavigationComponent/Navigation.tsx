import { MainNavigation } from './MainNavigation';
import { SubNavigation } from './SubNavigation';

interface NavigationItem {
  id: string;
  name: string;
}

export interface NavigationProps {
  items: NavigationItem[];
  selectedItemId: string;
  type: 'main' | 'secondary';
  onSelect: (id: string) => void;
}

export const Navigation = ({ type, ...rest }: NavigationProps) => {
  if (type === 'main') {
    return <MainNavigation {...rest} />;
  }
  return <SubNavigation {...rest} />;
};
