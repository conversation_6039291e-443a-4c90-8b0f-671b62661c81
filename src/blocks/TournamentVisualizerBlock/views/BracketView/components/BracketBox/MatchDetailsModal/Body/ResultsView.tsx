import clsx from 'clsx';

import { MatchSeriesGameResult } from '@/services/graphql/types/matchSeries';
import TrophyIcon from '@/ui/assets/icons/trophy.svg';

import { MatchDetailsResult } from '../types';

export const ResultsView = ({ results }: { results: MatchDetailsResult[] }) => {
  return (
    <div className="flex flex-col gap-0.5 self-stretch">
      {results.map((r, i) => (
        <MapScores isWhite={i % 2 === 0} {...r} key={`${r.map}-${i}`} />
      ))}
    </div>
  );
};

type MapScoresProps = MatchDetailsResult & { isWhite?: boolean };

export const MapScores = ({ isWhite, map, teams }: MapScoresProps) => {
  const [teamA, teamB] = teams;

  //todo when do we show strikethrough
  const areScoresAvailable = true;
  const MapWrapperElement = areScoresAvailable ? 'span' : 's';
  return (
    <div
      className={clsx(
        'grid grid-cols-[16px_18px_minmax(0,60px)_18px_16px] md:grid-cols-[16px_18px_minmax(0,54px)_18px_16px]',
        'gap-x-9 rounded-sm p-1.5 md:gap-x-2.5',
        isWhite && 'bg-white',
      )}
    >
      <TrophyCell isVisible={teamA.result === MatchSeriesGameResult.WIN} placement="start" />
      <ScoreCell score={teamA.score} />
      <p className="font-base flex max-w-full items-center justify-self-center text-[9px] leading-none font-extrabold text-black">
        <MapWrapperElement className="max-w-full truncate">{map}</MapWrapperElement>
      </p>
      <ScoreCell score={teamB.score} />
      <TrophyCell isVisible={teamB.result === MatchSeriesGameResult.WIN} placement="end" />
    </div>
  );
};

const ScoreCell = ({ score }: { score: number | null }) => {
  return <p className="font-primary text-dark-default justify-self-center text-base leading-none font-bold">{score}</p>;
};

const TrophyCell = ({ isVisible, placement }: { isVisible: boolean; placement: 'start' | 'end' }) => {
  const style = clsx('size-4', placement === 'start' ? 'justify-self-start' : 'justify-self-end');

  return isVisible ? <TrophyIcon className={style} /> : <div className={style} />;
};
