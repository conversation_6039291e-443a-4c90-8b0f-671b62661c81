import { MatchSeries } from '@/services/graphql/types/matchSeries';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { extractMetadata } from '../helpers';
import { Config } from '../types';
import { createGroup, createMatchAndContestants, sortMatchGroups } from './helpers';
import { RoundRobinGroup } from './types';

export function getRoundRobinViewGroups(matches: MatchSeries[], config: Config) {
  const groups: RoundRobinGroup[] = [];
  for (const matchSeries of matches) {
    const { groupMeta } = extractMetadata(matchSeries);

    let group = groups.find((g) => g.id === groupMeta.id);
    if (!group) {
      group = createGroup(groupMeta, matchSeries, config);
      groups.push(group);
      continue;
    }

    const { match, contestants } = createMatchAndContestants(matchSeries, config.translations);
    group.matches.push(match);
    for (const contestant of contestants) {
      const existingContestant = group.contestants.find((c) => c.id === contestant.id);
      if (existingContestant) {
        existingContestant.pointsScored += contestant.pointsScored;
        existingContestant.pointsLost += contestant.pointsLost;
        existingContestant.wins += contestant.wins;
        existingContestant.draws += contestant.draws;
        existingContestant.losses += contestant.losses;
        existingContestant.diff += contestant.diff;
      } else {
        group.contestants.push(contestant);
      }
    }
  }

  sortMatchGroups(groups);
  patchGroupsStatus(groups);

  return groups;
}

function patchGroupsStatus(groups: RoundRobinGroup[]) {
  groups.forEach((g) => {
    const hasLiveMatches = g.matches.some((m) => m.status === LocalMatchStatus.LIVE);
    if (hasLiveMatches) {
      g.status = LocalMatchStatus.LIVE;
      return;
    }

    const hasOpenMatches = g.matches.some((m) => m.status === LocalMatchStatus.UPCOMING);
    if (hasOpenMatches) {
      g.status = LocalMatchStatus.UPCOMING;
      return;
    }

    g.status = LocalMatchStatus.COMPLETED;
  });
}
