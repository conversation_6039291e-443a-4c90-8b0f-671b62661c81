import clsx from 'clsx';
import Image from 'next/image';
import { FaChevronDown } from 'react-icons/fa';
import { useToggle } from 'usehooks-ts';

import EwcAcronymLogoIcon from '@/ui/assets/icons/logos/ewc-acronym.svg';

import { constructImageUrl } from '../../../../../../services/graphql/utils';
import { JsonFieldType } from '../../../../../../strapi/types/helper';
import { ClubData, SimulatedClubData } from '../../_utils/types';
import { CompetitionRankings } from '../CompetitionRankings';
import { ClubChampionshipEligibleIcon } from '../HelperComponents/ClubChampionshipEligibleIcon';
import { MedalPill } from '../HelperComponents/MedalPill';
import { RankDiffPill } from '../HelperComponents/RankDiffPill';

export const MobileTableRow = ({
  club,
  translations,
}: {
  club: ClubData | SimulatedClubData;
  translations?: JsonFieldType | null;
}) => {
  const [isOpen, toggleIsOpen] = useToggle(false);
  const clubLogo = constructImageUrl(club.images, 'logo_transparent_whitebg');

  return (
    <>
      <div
        className={clsx('font-primary relative flex cursor-pointer flex-col gap-3 bg-white p-4 font-bold', {
          'before:bg-dark-default before:absolute before:inset-y-0 before:start-0 before:h-full before:w-[3px]': isOpen,
          'opacity-30': club.shouldBeGrayedOut,
        })}
        onClick={() => toggleIsOpen()}
      >
        <div className="flex gap-2">
          <div className="flex grow gap-2">
            <div className="flex items-center gap-0.5">
              <span className="text-[12px] leading-[110%]">#</span>
              <span className="text-[16px] leading-[110%]">{club.rank}</span>
            </div>
            {clubLogo ? (
              <Image alt={club.club.name ?? ''} className="size-5" height={20} src={clubLogo} width={20} />
            ) : (
              <EwcAcronymLogoIcon className="size-5 text-black" />
            )}
            <span className="grow text-[16px]">{club.club.name}</span>
          </div>
          {club.isChampionshipEligible && <ClubChampionshipEligibleIcon />}
          <div className="flex min-w-16 items-end justify-end gap-0.5">
            <span className="text-gray-dark text-[8px] uppercase">{translations?.ccp ?? 'CCP'}</span>
            <span
              className={clsx('text-[18px] leading-[100%]', {
                'min-w-[60px] text-right': club.isSimulated,
              })}
            >
              {club.prizes.XTS}
            </span>
          </div>
        </div>
        <div className="flex justify-between">
          {club.isSimulated ? (
            <div
              className={clsx('flex items-center rounded-xs pe-1', {
                'bg-dark-default text-white': isOpen,
                'bg-gray text-dark-default': !isOpen,
              })}
            >
              <div className="flex items-center px-[3px]">
                <FaChevronDown className={clsx('size-2 transition-transform duration-300', { 'rotate-180': isOpen })} />
              </div>
              <span className="font-primary text-[8px] font-bold uppercase">
                {isOpen ? (translations?.close ?? 'CLOSE') : (translations?.simulate ?? 'SIMULATE')}
              </span>
            </div>
          ) : (
            <RankDiffPill diff={club.rankDiff} />
          )}
          {club.isSimulated ? (
            <div className="flex min-w-16 items-end justify-end gap-0.5">
              <span className="text-gray-dark text-[8px] uppercase">
                {translations?.simulatedPoints ?? 'simulated points'}
              </span>
              <span className="text-gold-primary min-w-[60px] text-right text-[18px] leading-[100%]">
                {club.simulatedPoints ?? '-'}
              </span>
            </div>
          ) : (
            <div className="flex gap-1">
              <MedalPill count={club.medals.gold} type="gold" />
              <MedalPill count={club.medals.silver} type="silver" />
              <MedalPill count={club.medals.bronze} type="bronze" />
              <DropdownButton isOpen={isOpen} translations={translations} />
            </div>
          )}
        </div>
      </div>
      {isOpen && (
        <CompetitionRankings clubId={club.club.id} competitions={club.competitions} translations={translations} />
      )}
    </>
  );
};

const DropdownButton = ({ isOpen, translations }: { isOpen: boolean; translations?: JsonFieldType | null }) => {
  return (
    <div
      className={clsx('flex items-center rounded-xs ps-1', {
        'bg-dark-default text-white': isOpen,
        'bg-gray': !isOpen,
      })}
    >
      <span className="text-[8px] leading-[110%] uppercase">
        {isOpen ? (translations?.hideInfo ?? 'HIDE INFO') : (translations?.moreInfo ?? 'MORE INFO')}
      </span>

      <FaChevronDown className={clsx('size-[15px] p-[3px]', { 'rotate-180': isOpen })} />
    </div>
  );
};
