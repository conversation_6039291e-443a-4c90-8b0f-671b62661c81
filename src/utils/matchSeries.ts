import { MatchSeriesGameResult, MatchSeriesStatus, MatchStatus } from '@/services/graphql/types/matchSeries';

export enum LocalMatchStatus {
  COMPLETED = 'completed',
  LIVE = 'live',
  UPCOMING = 'upcoming',
}

export function convertToLocalMatchStatus(status: MatchSeriesStatus | MatchStatus): LocalMatchStatus {
  switch (status) {
    case 'LIVE':
      return LocalMatchStatus.LIVE;
    case 'FINISHED':
      return LocalMatchStatus.COMPLETED;
    case 'OPEN':
      return LocalMatchStatus.UPCOMING;
    default:
      return '' as LocalMatchStatus;
  }
}

export function isWinningResult(result: MatchSeriesGameResult | string) {
  return [MatchSeriesGameResult.DEFAULT_WIN, MatchSeriesGameResult.WIN].includes(result as MatchSeriesGameResult);
}
