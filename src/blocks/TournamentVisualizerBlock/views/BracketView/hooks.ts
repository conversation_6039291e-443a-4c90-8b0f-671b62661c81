'use client';

import { useEffect } from 'react';

import { LocalMatchStatus } from '@/utils/matchSeries';

import { Bracket } from '../../utils/brackets/types';

/**
 * Custom React hook that automatically scrolls the bracket view to the most relevant match.
 *
 * The hook determines the "focused" match in the following priority:
 * 1. The first match with status `LIVE`.
 * 2. If none are live, the first match with status `OPEN`.
 * 3. If none are open, the most recent match with status `FINISHED`.
 *
 * Once the focused match is found, the hook scrolls the corresponding DOM element (by match ID)
 * into view smoothly, centering it horizontally and aligning it vertically as close as possible.
 *
 * @param brackets - An array of `Bracket` objects, each containing rounds and matches to search through.
 *
 * @remarks
 * - The hook should be used within a React component.
 * - It relies on the presence of DOM elements with IDs matching the match IDs.
 * - The scroll action is delayed by 500ms to allow for DOM rendering.
 */
export function useScrollToFocusedBracket(brackets: Bracket[]) {
  useEffect(() => {
    const matches = brackets
      .flatMap((b) => b.rounds)
      .flatMap((r) => r.matches)
      .sort((a, b) => (a.startTime < b.startTime ? -1 : a.startTime > b.startTime ? 1 : 0));

    let focusedMatch = matches.find((m) => m.status === LocalMatchStatus.LIVE);

    if (!focusedMatch) {
      focusedMatch = matches.find((m) => m.status === LocalMatchStatus.UPCOMING);
    }

    if (!focusedMatch) {
      focusedMatch = matches.toReversed().find((m) => m.status === LocalMatchStatus.COMPLETED);
    }

    if (focusedMatch) {
      const focusedBracket = document.getElementById(focusedMatch.id);
      setTimeout(() => focusedBracket?.scrollIntoView({ block: 'nearest', inline: 'center', behavior: 'smooth' }), 500);
    }
  }, [brackets]);
}
