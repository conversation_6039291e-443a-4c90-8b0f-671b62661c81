const TFT_LATE_SUBMISSION_CLUB_IDS = [
  '746fd743-2c7c-4351-8987-15b383d91f3b', // EVOS Esports
];
const TFT_COMPETITION_SLUG = 'teamfight-tactics';

const FATAL_FURY_LATE_SUBMISSION_CLUB_IDS = [
  '1659a642-42b9-441b-86ce-dfd6623200d1', //  Yesports – RB
];
const FATAL_FURY_COMPETITION_SLUG = 'fatal-fury';

const FREE_FIRE_LATE_SUBMISSION_CLUB_IDS = [
  'f5dddc54-c694-4f51-af04-ca2476eb3b6b', //  DRAGONS ESPORTS
];
const FREE_FIRE_COMPETITION_SLUG = 'free-fire';

const MLBB_LATE_SUBMISSION_CLUB_IDS = [
  '6f45a8e3-5a59-4914-99b6-93ab932c5754', //  SELANGOR RED GIANTS OG ESPORTS
];
const MLBB_COMPETITION_SLUG = 'mlbb';

const COD_WARZONE_LATE_SUBMISSION_CLUB_IDS = [
  '5b41fccb-a1a2-47e7-ba70-85b32b35bb4c', // FaZe Clan
];
const COD_WARZONE_COMPETITION_SLUG = 'cod-warzone';

const PUBG_LATE_SUBMISSION_CLUB_IDS = [
  '7e85001d-a000-472e-be19-3cfa2077e350', // FULL SENSE
];
const PUBG_COMPETITION_SLUG = 'pubg-battlegrounds';

/**
 * Determines if a club is considered a "late submission club" for a given competition.
 *
 * For certain clubs and competitions, points are incorrectly added in ProDB
 * because they entered after the deadline. These clubs are not part of the Club Championship,
 * and if this case is triggered, their Club Championship points are set to 0.
 *
 * @param competitionSlug - The slug identifier for the competition (e.g., 'teamfight-tactics', 'fatal-fury').
 * @param clubId - The unique identifier of the club to check.
 * @returns `true` if the club is a late submission club for the specified competition; otherwise, `false`.
 */
export function isLateSubmissionClub(competitionSlug: string, clubId: string) {
  switch (competitionSlug) {
    case TFT_COMPETITION_SLUG:
      return TFT_LATE_SUBMISSION_CLUB_IDS.includes(clubId);
    case FATAL_FURY_COMPETITION_SLUG:
      return FATAL_FURY_LATE_SUBMISSION_CLUB_IDS.includes(clubId);
    case FREE_FIRE_COMPETITION_SLUG:
      return FREE_FIRE_LATE_SUBMISSION_CLUB_IDS.includes(clubId);
    case MLBB_COMPETITION_SLUG:
      return MLBB_LATE_SUBMISSION_CLUB_IDS.includes(clubId);
    case COD_WARZONE_COMPETITION_SLUG:
      return COD_WARZONE_LATE_SUBMISSION_CLUB_IDS.includes(clubId);
    case PUBG_COMPETITION_SLUG:
      return PUBG_LATE_SUBMISSION_CLUB_IDS.includes(clubId);
    default:
      return false;
  }
}
