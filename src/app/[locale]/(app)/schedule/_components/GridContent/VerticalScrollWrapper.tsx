'use client';

import clsx from 'clsx';
import React, { ReactNode, useEffect, useRef, useState } from 'react';

type Props = {
  children: ReactNode;
  className?: string;
  fadeHeight?: string;
};

export function VerticalScrollFadeWrapper({ children, className = '', fadeHeight = 'h-6' }: Props) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showTopFade, setShowTopFade] = useState(false);
  const [showBottomFade, setShowBottomFade] = useState(false);

  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;

    const updateFade = () => {
      const { scrollTop, scrollHeight, clientHeight } = el;
      setShowTopFade(scrollTop > 0);
      setShowBottomFade(scrollTop + clientHeight < scrollHeight - 1);
    };

    updateFade();
    el.addEventListener('scroll', updateFade);
    window.addEventListener('resize', updateFade);

    return () => {
      el.removeEventListener('scroll', updateFade);
      window.removeEventListener('resize', updateFade);
    };
  }, []);

  return (
    <div className={clsx('relative flex h-full flex-col', className)}>
      <div
        className={clsx(
          'pointer-events-none absolute top-0 right-0 left-0 z-10',
          fadeHeight,
          showTopFade ? 'visible' : 'invisible',
        )}
        style={{
          background: 'linear-gradient(0deg, rgba(245, 245, 245, 0) 0%, #F5F5F5 100%)',
        }}
      />

      <div className="hide-scrollbar flex-grow overflow-y-auto" ref={scrollRef}>
        {children}
      </div>

      <div
        className={clsx(
          'pointer-events-none absolute right-0 bottom-0 left-0 z-10',
          fadeHeight,
          showBottomFade ? 'visible' : 'invisible',
        )}
        style={{
          background: 'linear-gradient(180deg, rgba(245, 245, 245, 0) 0%, #F5F5F5 100%)',
        }}
      />
    </div>
  );
}
