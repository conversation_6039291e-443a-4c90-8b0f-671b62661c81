import { useQuery } from '@apollo/client';
import { useMemo } from 'react';

import { useCompetitionSlugs } from '@/app/[locale]/(app)/competitions/[slug]/_components/CompetitionSlugsProvider';

import { MATCH_SERIES_QUERY } from '../queries';
import { MatchSeriesQueryResponse } from '../types/matchSeries';
import { useMatchSeriesSubscription } from './subscribe';

export interface QueryOptions {
  isDisabled?: boolean;
  isSubscriptionDisabled?: boolean;
}

export function useMatchSeriesData(tournamentIds: string[] | undefined, options?: QueryOptions) {
  const { isDisabled = false, isSubscriptionDisabled = false } = options ?? {};
  const areTournamentIdsEmpty = !tournamentIds || tournamentIds.length === 0;

  const query = useQuery<MatchSeriesQueryResponse>(MATCH_SERIES_QUERY, {
    variables: { tournamentIds },
    skip: isDisabled || areTournamentIdsEmpty,
  });

  useMatchSeriesSubscription({
    enabled: !!query.data && !areTournamentIdsEmpty && !isDisabled && !isSubscriptionDisabled,
    tournamentIds: tournamentIds ?? [],
  });

  return query;
}

export function useGameMatchSeriesData(options?: QueryOptions) {
  const competitionSlugs = useCompetitionSlugs();
  const tournamentIds = useMemo(() => competitionSlugs.map((s) => s.competitionId), [competitionSlugs]);

  return useMatchSeriesData(tournamentIds, options);
}
