import { BlocksContent } from '@strapi/blocks-react-renderer';
import { ReactNode } from 'react';

import { RichTextContent } from '../../../../../../blocks/RichTextBlock/RichTextContent';
import { cn } from '../../../../../../utils/cn';

export const ExplainerText = ({
  content,
  icon,
  className,
  paragraphClassName,
}: {
  content: BlocksContent;
  icon?: ReactNode;
  className?: string;
  paragraphClassName?: string;
}) => {
  return (
    <div
      className={cn(
        'border-gold-primary/10 text-gold-primary bg-gold-primary/5 flex items-center gap-2 rounded-xl border px-4 py-3',
        className,
      )}
    >
      {icon}
      <RichTextContent
        content={content}
        paragraphClassName={cn(
          'font-base font-bold text-[14px] md:text-[16px] leading-none mt-0.5',
          paragraphClassName,
        )}
      />
    </div>
  );
};
