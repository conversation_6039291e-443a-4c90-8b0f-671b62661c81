import { Locale } from '@/hooks/i18n/const';
import { COMPETITIONS_GRID_BLOCK_KEY, CompetitionsGridBlockType, DynamicZoneBlock } from '@/strapi/types/block';

import { fetchGames } from '../../collection/game';

export async function assembleCompetitionsGridBlocks(blocks: DynamicZoneBlock[], locale: Locale) {
  const filteredBlocks = blocks.filter(
    (b) => b.__component === COMPETITIONS_GRID_BLOCK_KEY,
  ) as CompetitionsGridBlockType[];

  for (const block of filteredBlocks) {
    const { games } = await fetchGames(locale, block.year);
    block.games = games;
  }
}
