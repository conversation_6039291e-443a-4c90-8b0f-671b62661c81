import clsx from 'clsx';
import Image from 'next/image';
import { MdPeopleAlt } from 'react-icons/md';

import EwcAcronymLogoIcon from '@/ui/assets/icons/logos/ewc-acronym.svg';
import BackgroundImage from '@/ui/assets/images/abstract-background-4.png';

import { constructImageUrl } from '../../../../../services/graphql/utils';
import { JsonFieldType } from '../../../../../strapi/types/helper';
import { ClubData } from '../_utils/types';

export const ClubCard = ({ translations, club }: { club: ClubData; translations?: JsonFieldType | null }) => {
  const getHoverTextColorClass = (position: number | undefined) => {
    switch (position) {
      case 1:
        return 'max-lg:text-[#987C4B] group-hover:text-[#987C4B]';
      case 2:
        return 'max-lg:text-[#727272] group-hover:text-[#727272]';
      case 3:
        return 'max-lg:text-[#683C13] group-hover:text-[#683C13]';
      default:
        return 'max-lg:text-[#987C4B] group-hover:text-[#987C4B]';
    }
  };

  const clubLogo = constructImageUrl(club.images, 'logo_transparent');

  return (
    <div className="group relative flex min-h-[199px] flex-1 rounded-2xl p-5 text-white md:p-4 lg:p-5 xl:p-8">
      <ClubCardBackground clubPosition={club.rank} />
      <ClubPositionBadge clubPosition={club.rank} translations={translations} />

      <div className="relative flex grow flex-col">
        {clubLogo ? (
          <Image
            alt={club.club.name ?? ''}
            className="absolute end-0 bottom-0 size-[95px] md:relative lg:absolute xl:size-[120px]"
            height={140}
            src={clubLogo}
            width={140}
          />
        ) : (
          <EwcAcronymLogoIcon className="absolute end-0 bottom-0 size-[95px] md:relative lg:absolute xl:size-[120px]" />
        )}

        <div className="flex h-full flex-col justify-between gap-4">
          <div className="flex flex-col gap-2">
            <div className="flex flex-col">
              <span
                className={clsx(
                  'font-primary text-gray-dark text-[10px] font-bold uppercase transition-colors',
                  getHoverTextColorClass(club.rank),
                )}
              >
                {translations?.['club'] ?? 'club'}
              </span>
              <h3 className="font-primary text-[32px] lg:max-w-[250px] xl:text-[30px]">{club.club.name}</h3>
            </div>
            <div className="flex flex-col gap-0.5">
              {/*
              REMOVED BECAUSE API LACKS REGION 
              <div className="flex items-center gap-1">
                <FaGlobeAmericas size={14} />
                <span className="font-base text-[12px] leading-[160%] font-bold">{club.region}</span>
              </div> */}
              <div className="flex items-center gap-1">
                <MdPeopleAlt size={14} />
                <span className="font-base text-[12px] leading-[160%] font-bold capitalize">
                  {translations?.tournamentsAttendings ?? 'tournaments attending'}: {club.tournamentCount}
                </span>
              </div>
            </div>
          </div>

          <div className="flex gap-8">
            <div className="flex flex-col">
              <span
                className={clsx(
                  'font-primary text-gray-dark text-[10px] font-bold uppercase transition-colors',
                  getHoverTextColorClass(club.rank),
                )}
              >
                {translations?.['medalsWon'] ?? 'medals won'}
              </span>
              <h4 className="font-primary text-h4">{club.medals.gold + club.medals.silver + club.medals.bronze}</h4>
            </div>
            <div className="flex flex-col">
              <span
                className={clsx(
                  'font-primary text-gray-dark text-[10px] font-bold uppercase transition-colors',
                  getHoverTextColorClass(club.rank),
                )}
              >
                {translations?.['ccPoints'] ?? 'cc points'}
              </span>
              <h4 className="font-primary text-h4">{club.prizes.XTS}</h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ClubCardBackground = ({ clubPosition }: { clubPosition: number | undefined }) => {
  return (
    <div className="bg-gray-dark pointer-events-none absolute inset-0 overflow-clip rounded-2xl brightness-70 transition-all group-hover:brightness-100 max-lg:brightness-100">
      <Image alt="" className="object-cover" fill src={BackgroundImage} />
      <div
        className={clsx('absolute inset-0 mix-blend-multiply', {
          'bg-[#987C4B]': clubPosition === 1,
          'bg-[#727272]': clubPosition === 2,
          'bg-[#683C13]': clubPosition === 3,
        })}
      />
    </div>
  );
};

const ClubPositionBadge = ({
  clubPosition,
  translations,
}: {
  clubPosition: number | undefined;
  translations?: JsonFieldType | null;
}) => {
  return (
    <div
      className={clsx(
        'absolute end-0 top-0 flex h-[74px] w-[80px] flex-col items-center justify-center rounded-tr-2xl rounded-bl-2xl',
        {
          'bg-gradient-to-br from-[#907C4B] from-[18.44%] to-[#BEAD83]': clubPosition === 1,
          'bg-gradient-to-br from-[#727272] from-[18.44%] to-[#D8D8D8]': clubPosition === 2,
          'bg-gradient-to-br from-[#683C13] from-[18.06%] to-[#CE7726]': clubPosition === 3,
        },
      )}
    >
      <span className="font-primary text-[10px] font-bold uppercase">{translations?.['position'] ?? 'position'}</span>
      <h3 className="text-h3 font-primary">#{clubPosition}</h3>
    </div>
  );
};
