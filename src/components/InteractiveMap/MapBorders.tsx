import { TbBorderCornerSquare } from 'react-icons/tb';

export default function MapBorders() {
  return (
    <>
      <TbBorderCornerSquare
        className="absolute top-1 left-1 z-10 text-xl text-white sm:text-2xl"
        style={{ strokeWidth: 1 }}
      />
      <TbBorderCornerSquare
        className="absolute top-1 right-1 rotate-90 text-xl text-white sm:text-2xl"
        style={{ strokeWidth: 1 }}
      />
      <TbBorderCornerSquare
        className="absolute bottom-1 left-1 z-10 -rotate-90 text-xl text-white sm:text-2xl"
        style={{ strokeWidth: 1 }}
      />
      <TbBorderCornerSquare
        className="absolute right-1 bottom-1 z-10 rotate-180 text-xl text-white sm:text-2xl"
        style={{ strokeWidth: 1 }}
      />
    </>
  );
}
