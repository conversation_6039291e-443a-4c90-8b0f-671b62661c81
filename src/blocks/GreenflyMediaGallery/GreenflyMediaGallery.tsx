'use client';

import { useCurrentLocale } from '@/hooks/i18n';
import { GreenflyMediaBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const GreenflyMediaGallery = ({ section, url, ...rest }: GreenflyMediaBlockType) => {
  const paramsPart = useQueryParamsString(rest);
  return (
    <BlockSectionWrapper {...section}>
      <iframe
        allow="camera; web-share; microphone; storage-access; fullscreen;"
        src={`${url}?${paramsPart}`}
        style={{ width: '100%', height: '100vh', border: 'none' }}
      />
    </BlockSectionWrapper>
  );
};

function useQueryParamsString({
  siteHeaderDisabled,
  cookieBannerDisabled,
}: Pick<GreenflyMediaBlockType, 'siteHeaderDisabled' | 'cookieBannerDisabled'>) {
  const locale = useCurrentLocale();
  let params = `locale=${locale}`;

  if (siteHeaderDisabled) {
    params += '&site_header=0';
  }
  if (cookieBannerDisabled) {
    params += '&cookie_banner=0';
  }

  return params;
}
