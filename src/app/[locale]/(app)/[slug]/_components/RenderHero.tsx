import { CustomStreamHero } from '@/blocks/hero/StreamHero/CustomStreamHero/CustomStreamHero';
import { PostPageHeader } from '@/blocks/shared/Post/PostPageHeader';
import { PageLayoutType, PageType } from '@/strapi/types/collection/page';
import { JsonFieldType } from '@/strapi/types/helper';

import { Locale } from '../../../../../hooks/i18n/const';
import { ImpactPageHeader } from './ImpactPageHeader';

type RenderHeroProps = Pick<PageType, 'hero' | 'header' | 'type'> & { locale: Locale; translations: JsonFieldType };

export const RenderHero = async ({ hero, header, translations, type }: RenderHeroProps) => {
  if (hero) {
    return <CustomStreamHero {...hero} translations={translations} />;
  }

  if (header) {
    if (type === PageLayoutType.IMPACT) {
      return <ImpactPageHeader {...header} />;
    } else {
      return (
        <div className="flex px-4 lg:px-8 xl:px-35 2xl:justify-center">
          <div className="w-full xl:max-w-[1158px]">
            <PostPageHeader {...header} />
          </div>
        </div>
      );
    }
  }

  return null;
};
