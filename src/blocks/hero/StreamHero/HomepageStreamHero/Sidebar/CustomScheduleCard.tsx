import clsx from 'clsx';
import { format } from 'date-fns';

import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { CustomStreamSchedule } from '@/strapi/types/collection/stream';
import { cn } from '@/utils/cn';

import { useCountdown } from '../../shared/hooks/useCountdown';

interface CustomScheduleCardProps {
  schedule: CustomStreamSchedule;
  customStreamTitle: string | null;
}

export const CustomScheduleCard = ({ schedule, customStreamTitle }: CustomScheduleCardProps) => {
  const locale = useDateLocale();
  const { isLive, isCompleted } = useCountdown(schedule.startTime, schedule.endTime);

  return (
    <div
      className={clsx('relative flex flex-col gap-[5px] rounded-sm px-[14px] py-2.5', {
        'bg-white-dirty': isLive,
        'opacity-40': isCompleted,
      })}
    >
      {isLive && <div className="bg-red-accent absolute inset-x-0 top-0 h-[3px] rounded-t-full lg:hidden" />}
      {isLive && (
        <div className="absolute inset-y-0 start-0 max-lg:hidden">
          <div
            className="bg-red-accent absolute inset-y-0 end-full my-auto h-3 w-1.5"
            style={{ clipPath: 'polygon(100% 0, 0 50%, 100% 100%)' }}
          />
          <div className="bg-red-accent h-full w-[3px] rounded-l-full" />
        </div>
      )}
      <div className="flex gap-2">
        <Badge
          className="bg-dark-default"
          text={schedule.startTime ? format(new Date(schedule.startTime), 'hh:mm a', { locale }) : ''}
        />
        {/* <Badge className="bg-dark-default text-white">
          {schedule.endTime ? format(new Date(schedule.endTime), 'h:mm a', { locale }) : ''}
        </Badge> */}
        <span className="font-base h-fit self-center text-[10px] font-extrabold">{customStreamTitle}</span>
        <Badge className="bg-gray-dark" text={schedule.language} />
      </div>
      <div className="font-primary text-[13px] leading-[110%] font-bold">{schedule.title}</div>
      <div className="font-base text-gray-dark text-[10px] font-extrabold">@{schedule.tag}</div>
    </div>
  );
};

const Badge = ({ text, className }: { text?: string | null; className?: string }) => {
  return (
    <div
      className={cn(
        'font-primary flex items-center justify-center rounded-[1px] p-0.5 text-[8px] font-bold text-white uppercase',
        className,
      )}
    >
      <p className="leading-none">{text}</p>
    </div>
  );
};
