'use client';

import { useMemo } from 'react';

import { VideoEmbed } from '@/components/VideoEmbed';
import { useTodaysCcRankings } from '@/hooks/tournaments/useTodaysCcRankings';
import { JsonFieldType } from '@/strapi/types/helper';
import { GameStreamHeroType } from '@/strapi/types/hero';
import SpinnerIcon from '@/ui/assets/icons/spinner.svg';

import { useSelectedMatch } from '../shared/hooks/useSelectedMatch';
import { Intermission } from '../shared/Intermission';
import { MatchesSidebar } from '../shared/Sidebar';
import { filterAndPrioritizeStreams } from '../shared/utils/filterAndPrioritizeStreams';
import { generateGameStream } from '../shared/utils/generateGameStream';
import { GAME_STREAM_HERO_ELEMENT_ID } from './const';
import { useMatchesData } from './hooks';
import { StreamDetails } from './StreamDetails';

type Props = GameStreamHeroType & {
  slug: string;
  noStreamLabel: string | null;
  gameTitle: string | null;
  intermissionText: string | null;
  ticketsUrl: string | null;
  isTopNavigationVisible?: boolean;
  translations: JsonFieldType;
  apiTranslations: JsonFieldType;
};

export function GameStreamHero({
  slug,
  gameTitle,
  intermissionText,
  ticketsUrl,
  games,
  gameStreams,
  isTopNavigationVisible,
  apiTranslations,
  translations,
}: Props) {
  const firstUpcomingStream = useMemo(() => {
    const streams = filterAndPrioritizeStreams(gameStreams);
    const firstUpcomingStream = streams?.at(0);
    return firstUpcomingStream ? generateGameStream(firstUpcomingStream) : null;
  }, [gameStreams]);

  const { todaysCCRankings } = useTodaysCcRankings(games);
  const { matches, loading, errorMessage } = useMatchesData();

  const { selectedMatchId, setSelectedMatchId } = useSelectedMatch(matches, firstUpcomingStream);

  const selectedMatch = matches.find((m) => m.id === selectedMatchId);
  const selectedStreamUrl = selectedMatch?.stream?.url ?? '';
  return (
    <section className="w-full" id={GAME_STREAM_HERO_ELEMENT_ID}>
      <div className="flex max-lg:flex-col lg:h-screen">
        <div className="flex min-w-0 flex-3/4 flex-col">
          {isTopNavigationVisible && <div className="bg-dark-default h-[46px] w-full shrink-0 md:h-[115.5px]" />}
          <div className="bg-dark-default relative flex w-full items-center justify-center max-lg:aspect-video lg:h-full">
            {!selectedMatchId && firstUpcomingStream ? (
              <Intermission
                apiTranslations={apiTranslations}
                isLoading={loading}
                matches={matches}
                stream={firstUpcomingStream}
                translations={translations}
              />
            ) : (
              <VideoEmbed
                autoplay
                controls
                fallback={<SpinnerIcon className="size-12 text-white" />}
                url={selectedStreamUrl}
              />
            )}
          </div>
          <StreamDetails
            apiTranslations={apiTranslations}
            gameName={gameTitle}
            intermissionText={intermissionText}
            selectedMatch={selectedMatch}
            ticketsUrl={ticketsUrl}
            translations={translations}
          />
        </div>
        <MatchesSidebar
          apiTranslations={apiTranslations}
          error={errorMessage}
          isLoading={loading}
          matches={matches}
          selectedGameStream={null}
          selectedMatchId={selectedMatchId}
          slug={slug}
          todaysCCRankings={todaysCCRankings}
          translations={translations}
          onMatchSelect={setSelectedMatchId}
        />
      </div>
    </section>
  );
}
