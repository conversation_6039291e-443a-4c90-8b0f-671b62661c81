export interface VenueMapConfig {
  id: string;
  name: string;
  svg: string;
  overlayBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
  interactionBox?: {
    widthPercent: number;
    heightPercent: number;
    offsetTopPercent: number;
    offsetLeftPercent: number;
    rotationDeg?: number;
  };
}

export const mapBase = {
  width: 2620,
  height: 1474,
};

export const venueMapConfigs: VenueMapConfig[] = [
  {
    id: 'venue-1',
    name: 'Venue 1',
    svg: '/assets/map/1.svg',
    overlayBox: {
      top: 530,
      left: 480,
      width: 382,
      height: 426,
    },
    interactionBox: {
      widthPercent: 84,
      heightPercent: 61,
      offsetTopPercent: 18,
      offsetLeftPercent: 5,
      rotationDeg: 42,
    },
  },
  {
    id: 'venue-2',
    name: 'Venue 2',
    svg: '/assets/map/2.svg',
    overlayBox: {
      top: 705,
      left: 848,
      width: 230,
      height: 120,
    },
    interactionBox: {
      widthPercent: 56,
      heightPercent: 88,
      offsetTopPercent: 0,
      offsetLeftPercent: 22,
      rotationDeg: 42,
    },
  },
  {
    id: 'venue-3',
    name: 'Venue 3',
    svg: '/assets/map/3.svg',
    overlayBox: {
      top: 640,
      left: 970,
      width: 150,
      height: 135,
    },
    interactionBox: {
      widthPercent: 98,
      heightPercent: 64,
      offsetTopPercent: 16,
      offsetLeftPercent: 2,
      rotationDeg: 41,
    },
  },
  {
    id: 'venue-4',
    name: 'Venue 4',
    svg: '/assets/map/4.svg',
    overlayBox: {
      top: 653,
      left: 1078,
      width: 100,
      height: 80,
    },
    interactionBox: {
      widthPercent: 100,
      heightPercent: 73,
      offsetTopPercent: 18,
      offsetLeftPercent: 5,
      rotationDeg: 43,
    },
  },
  {
    id: 'venue-5',
    name: 'Venue 5',
    svg: '/assets/map/5.svg',
    overlayBox: {
      top: 585,
      left: 1113,
      width: 137,
      height: 140,
    },
    interactionBox: {
      widthPercent: 76,
      heightPercent: 61,
      offsetTopPercent: 13,
      offsetLeftPercent: 10,
      rotationDeg: 36,
    },
  },
  {
    id: 'venue-6',
    name: 'Venue 6',
    svg: '/assets/map/6.svg',
    overlayBox: {
      top: 539,
      left: 1185,
      width: 132,
      height: 126,
    },
    interactionBox: {
      widthPercent: 94,
      heightPercent: 67,
      offsetTopPercent: 13,
      offsetLeftPercent: 10,
      rotationDeg: 36,
    },
  },
  {
    id: 'venue-7',
    name: 'Venue 7',
    svg: '/assets/map/7.svg',
    overlayBox: {
      top: 470,
      left: 1335,
      width: 162,
      height: 199,
    },
    interactionBox: {
      widthPercent: 78,
      heightPercent: 71,
      offsetTopPercent: 21,
      offsetLeftPercent: 11,
      rotationDeg: 56,
    },
  },
  {
    id: 'venue-8',
    name: 'Venue 8',
    svg: '/assets/map/8.svg',
    overlayBox: {
      top: 340,
      left: 1270,
      width: 210,
      height: 230,
    },
    interactionBox: {
      widthPercent: 61,
      heightPercent: 58,
      offsetTopPercent: 14,
      offsetLeftPercent: 19,
      rotationDeg: 49,
    },
  },
  {
    id: 'venue-9',
    name: 'Venue 9',
    svg: '/assets/map/9.svg',
    overlayBox: {
      top: 248,
      left: 1010,
      width: 312,
      height: 333,
    },
    interactionBox: {
      widthPercent: 47,
      heightPercent: 97,
      offsetTopPercent: 2,
      offsetLeftPercent: 27,
      rotationDeg: 49,
    },
  },
  {
    id: 'venue-10',
    name: 'Venue 10',
    svg: '/assets/map/10.svg',
    overlayBox: {
      top: 680,
      left: 1247,
      width: 115,
      height: 100,
    },
    interactionBox: {
      widthPercent: 47,
      heightPercent: 97,
      offsetTopPercent: 2,
      offsetLeftPercent: 27,
      rotationDeg: 49,
    },
  },
  {
    id: 'venue-11',
    name: 'Venue 11',
    svg: '/assets/map/11.svg',
    overlayBox: {
      top: 845,
      left: 1235,
      width: 280,
      height: 265,
    },
    interactionBox: {
      widthPercent: 47,
      heightPercent: 97,
      offsetTopPercent: 2,
      offsetLeftPercent: 27,
      rotationDeg: 49,
    },
  },
  {
    id: 'venue-12',
    name: 'Venue 12',
    svg: '/assets/map/12.svg',
    overlayBox: {
      top: 605,
      left: 1683,
      width: 118,
      height: 155,
    },
    interactionBox: {
      widthPercent: 74,
      heightPercent: 91,
      offsetTopPercent: 0,
      offsetLeftPercent: 10,
      rotationDeg: 354,
    },
  },
  {
    id: 'venue-13',
    name: 'Venue 13',
    svg: '/assets/map/13.svg',
    overlayBox: {
      top: 530,
      left: 1777,
      width: 123,
      height: 250,
    },
    interactionBox: {
      widthPercent: 80,
      heightPercent: 58,
      offsetTopPercent: 17,
      offsetLeftPercent: 12,
      rotationDeg: 346,
    },
  },
  {
    id: 'venue-14',
    name: 'Venue 14',
    svg: '/assets/map/14.svg',
    overlayBox: {
      top: 655,
      left: 1729,
      width: 215,
      height: 325,
    },
    interactionBox: {
      widthPercent: 93,
      heightPercent: 46,
      offsetTopPercent: 25,
      offsetLeftPercent: 4,
      rotationDeg: 346,
    },
  },
];
