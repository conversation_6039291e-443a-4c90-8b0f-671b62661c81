'use client';

import clsx from 'clsx';
import { useSearchParams } from 'next/navigation';
import { FaCircleUser } from 'react-icons/fa6';

import { LocalizedLink } from '@/components/LocalizedLink';
import { FANTASY_BASE_URL } from '@/config/env/client';
import { useCurrentUser } from '@/services/auth/hooks';

interface Props {
  isEmbedded?: boolean;
}

export const ProfileButton = ({ isEmbedded = false }: Props) => {
  const { user } = useCurrentUser();
  const loginLink = useLoginHref(isEmbedded);

  const profileLink = getProfileHref(isEmbedded);
  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Main Navigation', button_name: user ? `Profile Link` : 'Login Link' }}
      className={clsx(
        'flex cursor-pointer flex-col items-center gap-1 px-3 py-4 transition-colors',
        user ? 'text-gold-primary bg-white' : 'bg-dark-default text-white',
      )}
      href={user ? profileLink : loginLink}
    >
      <FaCircleUser className="size-[26px]" />
      <p className="font-primary max-w-full truncate text-[10px] leading-none font-bold">
        {user ? user.preferred_username || user.email : 'Login'}
      </p>
    </LocalizedLink>
  );
};

export const ProfileNavigationItem = ({ isEmbedded = false }: Props) => {
  const { user } = useCurrentUser();
  const loginLink = useLoginHref(isEmbedded);

  const profileLink = getProfileHref(isEmbedded);
  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Main Navigation', button_name: user ? `Profile Link` : 'Login Link' }}
      className={clsx(
        'flex cursor-pointer items-center gap-3 p-3 transition-colors',
        user ? 'text-gold-primary' : 'text-dark-default',
      )}
      href={user ? profileLink : loginLink}
    >
      <FaCircleUser className="size-6 shrink-0 p-0.5" />
      <p className="font-base mt-px text-[13px] leading-none font-bold">
        {user ? user.preferred_username || user.email : 'Login'}
      </p>
    </LocalizedLink>
  );
};

function getProfileHref(isEmbedded: boolean) {
  if (isEmbedded) {
    return `${FANTASY_BASE_URL}/my-account`;
  }
  return '/profile';
}

function useLoginHref(isEmbedded: boolean) {
  const searchParams = useSearchParams();

  if (isEmbedded) {
    return `/auth?${searchParams.toString()}`;
  }
  return '/login';
}
