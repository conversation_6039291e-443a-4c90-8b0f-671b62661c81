import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { RenderHero } from '@/app/[locale]/(app)/competitions/[slug]/_components/RenderHero';
import { BlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { GAME_STREAM_HERO_ELEMENT_ID } from '@/blocks/hero/StreamHero/GameStreamHero/const';
import TopNavigation from '@/components/TopNavigation';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getGamePageData, getGamePageSeo } from '@/strapi/api/collection/game';
import { redirectAccordingToConfig } from '@/strapi/api/collection/redirect';
import { getSiteConfig, SiteConfigData } from '@/strapi/api/single/siteConfig';
import { DynamicZoneBlock } from '@/strapi/types/block';
import { TopNavigationType } from '@/strapi/types/helper';
import { HOMEPAGE_STREAM_HERO_BLOCK_KEY } from '@/strapi/types/hero';

import { CompetitionSlugsProvider } from './_components/CompetitionSlugsProvider';
import { SelectedMatchProvider } from './_components/SelectedMatchProvider';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale<{ slug: string }>): Promise<Metadata> {
  const { locale, slug } = await params;
  const seo = await getGamePageSeo(locale, slug);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function GamePage({ params }: PageProps<{ slug: string }>) {
  const { locale, slug } = await params;
  await redirectAccordingToConfig(`/competitions/${slug}`, locale);

  const pageData = await getGamePageData(locale, slug);
  if (!pageData) {
    return notFound();
  }

  const siteConfig = await getSiteConfig(locale);
  const { hero, blocks, competitionSlugs } = pageData;

  const isLiveStateHero = hero?.__component === HOMEPAGE_STREAM_HERO_BLOCK_KEY;
  const navigationItems = getNavigationItems(blocks, isLiveStateHero, siteConfig);
  return (
    <div className="flex flex-col">
      <CompetitionSlugsProvider competitionSlugs={competitionSlugs}>
        <SelectedMatchProvider>
          <div className="relative flex pb-8 lg:pb-12">
            <TopNavigation navigationItems={navigationItems} />
            <RenderHero
              hero={hero}
              isTopNavigationVisible={navigationItems.length !== 0}
              locale={locale}
              pageData={pageData}
              siteConfig={siteConfig}
              slug={slug}
            />
          </div>
          {blocks.map((b) => (
            <BlockRenderer {...b} {...siteConfig} key={`${b.__component}-${b.id}`} />
          ))}
        </SelectedMatchProvider>
      </CompetitionSlugsProvider>
    </div>
  );
}
function getNavigationItems(blocks: DynamicZoneBlock[], isLiveStateHero: boolean, siteConfig: SiteConfigData | null) {
  const navigationItems = blocks
    .map((b) => b.section?.navigation)
    .filter((item): item is TopNavigationType => item !== null && item !== undefined);

  if (isLiveStateHero) {
    navigationItems.unshift({
      navigationId: GAME_STREAM_HERO_ELEMENT_ID,
      navigationLabel: siteConfig?.translations?.['topNavigation.liveStream'] ?? 'live stream',
    });
  }

  return navigationItems;
}
