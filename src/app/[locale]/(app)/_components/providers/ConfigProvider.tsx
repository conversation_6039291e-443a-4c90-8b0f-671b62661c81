'use client';

import { createContext, useContext } from 'react';

import { ConfigType } from '@/strapi/types/siteConfig';

export const ConfigContext = createContext<ConfigType | undefined | null>(undefined);

export const ConfigProvider = ({ config, children }: React.PropsWithChildren<{ config?: ConfigType | null }>) => {
  return <ConfigContext.Provider value={config}>{children}</ConfigContext.Provider>;
};

export function useConfig() {
  return useContext(ConfigContext);
}
