import {
  ANNOUNCED_GAMES_GRID_BLOCK_KEY,
  AUTOSCROLLING_SHOWCASE_KEY,
  CLUB_CHAMPION_OVERVIEW_BLOCK_KEY,
  COMPETITION_WINNERS_GRID_BLOCK_KEY,
  COMPETITIONS_GRID_BLOCK_KEY,
  CONTENT_NAVIGATION_GRID_BLOCK_KEY,
  CTA_BLOCK_KEY,
  FAQ_BLOCK_KEY,
  GREENFLY_MEDIA_BLOCK,
  KEY_STATISTICS_BLOCK_KEY,
  MEDIA_BLOCK_KEY,
  NEWS_BLOCK_KEY,
  PARTICIPATING_TEAMS_BLOCK_KEY,
  PARTNER_RIBBON_BLOCK_KEY,
  PARTNERS_BLOCK_KEY,
  PROMO_BLOCK_KEY,
  PULL_QUOTE_BLOCK_KEY,
  QUALIFIERS_GRID_BLOCK_KEY,
  QUALIFIERS_LIST_BLOCK_KEY,
  R<PERSON>BON_BLOCK_KEY,
  RICH_TEXT_BLOCK_KEY,
  SHOWCASE_GRID_BLOCK_KEY,
  STACKABLE_INFO_BLOCK_KEY,
  TICKET_SALES_BLOCK_KEY,
  TIMELINE_BLOCK_KEY,
  VISUAL_INFO_BLOCK_KEY,
  VOD_BLOCK_KEY,
} from '../types/block';
import { FESTIVAL_FILTER_BLOCK_KEY, FESTIVAL_LIST_BLOCK_KEY } from '../types/festivalBlock';
import {
  GAME_SCHEDULE_BLOCK_KEY,
  STANDINGS_TABLE_BLOCK_KEY,
  TOURNAMENT_VISUALIZER_BLOCK_KEY,
  WHERE_TO_WATCH_BLOCK_KEY,
} from '../types/gamePageBlock';

const section = { populate: '*' };

interface Params {
  includeGamePageBlocks?: boolean;
  includeFestivalPageBlocks?: boolean;
}

export function getDynamicZoneBlocks(params?: Params) {
  return {
    on: {
      ...blocksDynamicZone,
      ...(params?.includeGamePageBlocks ? gamePageBlocks : {}),
      ...(params?.includeFestivalPageBlocks ? festivalPageBlocks : {}),
    },
  };
}

const blocksDynamicZone = {
  [SHOWCASE_GRID_BLOCK_KEY]: { populate: { section, images: { populate: '*' } } },
  [NEWS_BLOCK_KEY]: {
    populate: {
      section,
      tag: true,
      excludedTags: true,
      featuredArticles: { populate: ['tags', 'cover'] },
    },
  },
  [MEDIA_BLOCK_KEY]: { populate: { section, media: { populate: true } } },
  [RIBBON_BLOCK_KEY]: { populate: { section, ribbon: { populate: '*' } } },
  [RICH_TEXT_BLOCK_KEY]: { populate: { section, columns: { populate: '*' } } },
  [FAQ_BLOCK_KEY]: {
    populate: { section, faq: { populate: { faqSections: { populate: '*' } } } },
  },
  [PULL_QUOTE_BLOCK_KEY]: { populate: { section } },
  [PROMO_BLOCK_KEY]: { populate: { section, backgroundImage: { populate: '*' }, media: { populate: '*' } } },
  [AUTOSCROLLING_SHOWCASE_KEY]: { populate: { section, button: { populate: '*' }, clubs: { populate: '*' } } },
  [CTA_BLOCK_KEY]: { populate: { section, ctas: { populate: '*' } } },
  [ANNOUNCED_GAMES_GRID_BLOCK_KEY]: { populate: { section, games: { populate: '*' } } },
  [VISUAL_INFO_BLOCK_KEY]: { populate: { section, button: true, media: true } },
  [STACKABLE_INFO_BLOCK_KEY]: {
    populate: { section, items: { populate: { media: true, cards: true } } },
  },
  [TICKET_SALES_BLOCK_KEY]: {
    populate: {
      section,
      tickets: {
        populate: {
          image: { fields: ['url', 'alternativeText', 'width', 'height'] },
          games: { populate: { game: { populate: '*' } } },
          button: { populate: '*' },
        },
      },
    },
  },
  [PARTNERS_BLOCK_KEY]: { populate: { section, partners: { populate: '*' } } },
  [KEY_STATISTICS_BLOCK_KEY]: { populate: { section, keyStatistics: { populate: '*' } } },
  [CONTENT_NAVIGATION_GRID_BLOCK_KEY]: { populate: { section, contentNavigationItems: { populate: '*' } } },
  [TIMELINE_BLOCK_KEY]: { populate: { section, items: { populate: '*' } } },
  [QUALIFIERS_GRID_BLOCK_KEY]: {
    populate: {
      section,
      qualifiers: {
        fields: ['title', 'startDate', 'endDate', 'region', 'teamsProgress'],
        populate: {
          logo: { fields: ['url', 'alternativeText', 'width', 'height'] },
          buttons: { populate: '*' },
          game: { fields: ['gradientHex'] },
        },
      },
    },
  },
  [QUALIFIERS_LIST_BLOCK_KEY]: {
    populate: {
      section,
      qualifiers: {
        fields: ['title', 'startDate', 'endDate', 'region', 'teamsProgress'],
        populate: { logo: { fields: ['url', 'alternativeText', 'width', 'height'] }, buttons: { populate: '*' } },
      },
    },
  },
  [PARTICIPATING_TEAMS_BLOCK_KEY]: {
    populate: {
      section,
      gridItems: {
        fields: ['teamName', 'subtitleText', 'qualifierName', 'playerName', 'isCurrentChampion', 'isPlayer', 'isTBD'],
        populate: {
          teamLogo: { fields: ['url', 'alternativeText', 'width', 'height'] },
          qualifierLogo: { fields: ['url', 'alternativeText', 'width', 'height'] },
          playerPhoto: { fields: ['url', 'alternativeText', 'width', 'height'] },
          club: { populate: { logo: { populate: '*' } } },
        },
      },
    },
  },
  [PARTNER_RIBBON_BLOCK_KEY]: { populate: { section, partnerRibbon: { populate: { partners: { populate: '*' } } } } },
  [GREENFLY_MEDIA_BLOCK]: { populate: { section } },
  [VOD_BLOCK_KEY]: {
    fields: ['languageBadge', 'itemsPerPage'],
    populate: {
      section,
      vods: { populate: { youtubeLink: { populate: '*' } } },
    },
  },
  [COMPETITION_WINNERS_GRID_BLOCK_KEY]: { populate: { section } },
  [CLUB_CHAMPION_OVERVIEW_BLOCK_KEY]: { populate: { section, heroImage: true, button: true } },
  [COMPETITIONS_GRID_BLOCK_KEY]: { populate: { section } },
};

const gamePageBlocks = {
  [STANDINGS_TABLE_BLOCK_KEY]: { populate: { section } },
  [TOURNAMENT_VISUALIZER_BLOCK_KEY]: { populate: { section } },
  [GAME_SCHEDULE_BLOCK_KEY]: { populate: { section } },
  [WHERE_TO_WATCH_BLOCK_KEY]: { populate: { section } },
};

const festivalPageBlocks = {
  [FESTIVAL_LIST_BLOCK_KEY]: { populate: { section } },
  [FESTIVAL_FILTER_BLOCK_KEY]: { populate: { section, weekTickets: true } },
};
