import { strapi } from '@strapi/client';

import { STRAPI_BASE_URL } from '@/config/env/client';
import { isStagingEnvironment, STRAPI_API_TOKEN } from '@/config/env/server';
import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';

export const strapiClient = strapi({ baseURL: `${STRAPI_BASE_URL}/api`, auth: STRAPI_API_TOKEN });

export function getStrapiDefaultParams(locale: Locale) {
  if (!ALL_LOCALES.includes(locale)) {
    throw new Error(`${locale} is not a valid locale!`);
  }

  return {
    locale: webToStrapiLocale(locale),
    status: isStagingEnvironment ? ('draft' as const) : ('published' as const),
  };
}

const STRAPI_CHINESE = 'zh-Hans-CN';
export const webToStrapiLocale = (locale: Locale) => (locale === 'zh' ? STRAPI_CHINESE : locale);
export const strapiToWebLocale = (locale: string) => (locale === STRAPI_CHINESE ? 'zh' : locale) as Locale;
