import { useQuery } from '@apollo/client';
import { useMemo } from 'react';

import { useCompetitionSlugs } from '@/app/[locale]/(app)/competitions/[slug]/_components/CompetitionSlugsProvider';

import { TOURNAMENTS_QUERY } from '../queries';
import { TournamentsQueryResponse } from '../types/tournament';
import { QueryOptions } from './matchSeries';
import { useTournamentsSubscription } from './subscribe';

export function useTournamentsData(tournamentIds: string[], options?: QueryOptions) {
  const { isDisabled = false, isSubscriptionDisabled = false } = options ?? {};
  const areTournamentIdsEmpty = tournamentIds.length === 0;

  const query = useQuery<TournamentsQueryResponse>(TOURNAMENTS_QUERY, {
    variables: { tournamentIds },
    skip: isDisabled || areTournamentIdsEmpty,
  });

  useTournamentsSubscription({
    enabled: !!query.data && !areTournamentIdsEmpty && !isDisabled && !isSubscriptionDisabled,
    tournamentIds,
  });

  return query;
}

export function useGameTournamentsData(options?: QueryOptions) {
  const competitionSlugs = useCompetitionSlugs();
  const tournamentIds = useMemo(() => competitionSlugs.map((s) => s.competitionId), [competitionSlugs]);

  return useTournamentsData(tournamentIds, options);
}
