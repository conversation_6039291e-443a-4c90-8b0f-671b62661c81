import { StrapiBase } from '../base';
import { DynamicZoneBlock } from '../block';
import { GameStreamHeroType, HeroWinnerHighlightBannerType } from '../hero';
import { MediaType } from '../media';
import { SeoType } from '../shared/seo';

export const GAMES_COLLECTION_KEY = 'games';
export interface GameType extends StrapiBase {
  title: string | null;
  intermissionText: string | null;
  slug: string | null;
  gradientHex: string | null;
  summary: string | null;
  gameFormat: string | null;
  prizePool: string | null;
  officialGameWebsite: string | null;
  tournamentStart: string | null;
  tournamentEnd: string | null;
  ticketsUrl: string | null;
  logoLight: MediaType | null;
  logoDark: MediaType | null;
  schedulePopupLogo: MediaType | null;
  keyArt: MediaType | null;
  defaultTournamentRuleset: MediaType | null;
  blocks: DynamicZoneBlock[];
  seo: SeoType | null;
  competingTeams: number | null;
  competingPlayers: number | null;
  icon: MediaType | null;
  iconFinals: MediaType | null;
  hideIcon: boolean;
  isLinkingToGamePageEnabled: boolean | null;
  numQualifiers: number | null;
  competitionSlugs: CompetitionSlug[];
  hero: HeroWinnerHighlightBannerType | GameStreamHeroType | null;
}

export interface CompetitionSlug {
  id: number;
  competitionId: string;
  competitionName: string;
}
