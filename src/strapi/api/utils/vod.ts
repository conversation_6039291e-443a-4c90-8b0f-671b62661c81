import { VodEnrichedItem } from '@/blocks/VodBlock/VodGrid';
import { VOD_BLOCK_KEY, VodItemType } from '@/strapi/types/block';

export async function enrichVodItems(vods: VodItemType[]): Promise<VodEnrichedItem[]> {
  return Promise.all(
    vods.map(async (vod): Promise<VodEnrichedItem> => {
      const metadata = await getYoutubeMetadata(vod.youtubeLink?.url || '');
      return {
        id: vod.id,
        name: vod.name,
        section: vod.section ?? null,
        __component: VOD_BLOCK_KEY,
        youtubeLink: vod.youtubeLink ? { url: vod.youtubeLink.url } : null,
        games: vod.games ?? [],
        fetchedTitle: metadata.title,
        fetchedThumbnail: metadata.thumbnail,
        fetchedAuthor: metadata.author,
        locale: vod.locale,
      };
    }),
  );
}

interface YoutubeMetadata {
  title: string;
  thumbnail: string;
  author: string;
  provider: string;
}

const EMPTY_METADATA: YoutubeMetadata = {
  title: '',
  thumbnail: '',
  author: '',
  provider: '',
};

export async function getYoutubeMetadata(videoUrl: string): Promise<YoutubeMetadata> {
  if (!videoUrl) return EMPTY_METADATA;

  try {
    const response = await fetch(`https://www.youtube.com/oembed?url=${videoUrl}&format=json`);
    if (!response.ok) {
      throw new Error(`Failed to fetch YouTube oEmbed for ${videoUrl}: ${response.status}`);
    }

    const data = await response.json();

    return {
      title: data.title,
      thumbnail: data.thumbnail_url,
      author: data.author_name,
      provider: data.provider_name,
    };
  } catch (error) {
    console.error(`[getYoutubeMetadata] Error for URL "${videoUrl}":`, error);
    return EMPTY_METADATA;
  }
}
