import {
  Match<PERSON><PERSON>,
  MatchSeriesContestant,
  MatchSeriesGameResult,
  MatchSeriesMatch,
  MatchSeriesStatus,
  MatchStatus,
} from './matchSeries';
import { TournamentContestant } from './tournament';

export interface SubscriptionQueryData {
  fixtureDataEvents: { data: SubscriptionData };
}

export type SubscriptionData =
  | MatchSeriesSubscriptionData
  | MatchSeriesContestantSubscriptionData
  | MatchContestantSubscriptionData
  | TournamentContestantSubscriptionData
  | MatchSubscriptionData;

export type TournamentContestantSubscriptionData = Pick<TournamentContestant, 'id' | 'rank'> & {
  __typename: 'TournamentContestant';
};

export type MatchSeriesSubscriptionData = Pick<MatchSeries, 'id' | 'startTime'> & {
  matchSeriesStatus: MatchSeriesStatus;
  __typename: 'MatchSeries';
};

export type MatchSubscriptionData = Pick<MatchSeriesMatch, 'id'> & {
  matchStatus: MatchStatus;
  __typename: 'Match';
};

type MatchContestantBase = Pick<MatchSeriesContestant, 'id' | 'points' | 'rank' | 'score'>;
export type MatchSeriesContestantSubscriptionData = MatchContestantBase & {
  matchSeriesContestantResult: MatchSeriesGameResult;
  __typename: 'MatchSeriesContestant';
};
export type MatchContestantSubscriptionData = MatchContestantBase & {
  matchContestantResult: MatchSeriesGameResult;
  __typename: 'MatchContestant';
};
