import { StrapiBase } from '../base';
import { VodItemType } from '../block';
import { MediaType } from '../media';
import { GameType } from './game';

export interface StreamType extends StrapiBase {
  internalName: string | null;
  logoLight: MediaType | null;
  logoDark: MediaType | null;
  backgroundImage: MediaType | null;
  startTime: string | null;
  endTime: string | null;
  tag: string | null;
  game: GameType | null;
  customSchedule: CustomStreamSchedule[] | null;
  streamUrl: string | null;
  intermissionText: string | null;
  buttonUrl: string | null;
  buttonText: string | null;
  intermissionType: StreamIntermissionType | null;
  vods: VodItemType[];
}

export enum StreamIntermissionType {
  TIMER = 'timer',
  VOD = 'vod',
}

export interface CustomStreamSchedule {
  startTime: string | null;
  endTime: string | null;
  language: string | null;
  title: string | null;
  tag: string | null;
}
