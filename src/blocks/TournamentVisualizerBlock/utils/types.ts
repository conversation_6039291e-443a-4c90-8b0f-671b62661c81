import { MatchSeriesGameResult } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';
import { LocalMatchStatus } from '@/utils/matchSeries';

export interface Phase {
  id: string;
  name: string;
  type: ViewType;
  groups: Group[];
}

export enum ViewType {
  FFA,
  BRACKETS,
  ROUND_ROBIN,
}

export interface Group {
  id: string;
  name: string;
  position: number;
  status: LocalMatchStatus;
}

export interface Contestant {
  id: string;
  name: string;
  logoUrl: string | null;
  score: number | null;
  result: MatchSeriesGameResult | string;
}

export interface Config {
  apiTranslations: JsonFieldType;
  translations: JsonFieldType;
}
