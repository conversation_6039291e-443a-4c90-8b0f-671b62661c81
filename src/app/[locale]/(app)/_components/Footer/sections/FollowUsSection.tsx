import { IconType } from 'react-icons';
import { FaDiscord, FaFacebook, FaInstagram, FaTiktok, FaYoutube } from 'react-icons/fa';
import { FaThreads, FaTwitch, FaXTwitter } from 'react-icons/fa6';

import { LocalizedLink } from '@/components/LocalizedLink';
import { FooterSection } from '@/strapi/api/single/footer';

const iconsMap: Record<string, IconType> = {
  x: FaXTwitter,
  twitter: FaXTwitter,
  instagram: FaInstagram,
  youtube: FaYoutube,
  tiktok: FaTiktok,
  discord: FaDiscord,
  threads: FaThreads,
  facebook: FaFacebook,
  twitch: FaTwitch,
};

export const FollowUsSection = ({ label, links }: FooterSection) => {
  return (
    <div className="flex flex-col items-center justify-between gap-3 md:items-end">
      {label && <p className="font-primary text-gold-primary text-xs leading-normal font-bold uppercase">{label}</p>}
      <div className="flex items-center gap-4 text-white lg:py-2">
        {links.map((l) => {
          const IconComponent = iconsMap[l.text.toLowerCase()];

          return IconComponent ? (
            <LocalizedLink
              brazeEventProperties={{
                location: 'Footer - Follow Us Section',
                button_name: `Follow Us Link (${l.url})`,
              }}
              href={l.url}
              key={l.id}
            >
              <IconComponent className="size-5" />
            </LocalizedLink>
          ) : null;
        })}
      </div>
    </div>
  );
};
