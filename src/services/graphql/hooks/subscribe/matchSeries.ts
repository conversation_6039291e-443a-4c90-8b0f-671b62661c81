import { useSubscription } from '@apollo/client';

import { MATCH_SERIES_QUERY, MATCH_SERIES_SUBSCRIPTION_QUERY } from '../../queries';
import { MatchSeriesQueryResponse } from '../../types/matchSeries';
import {
  MatchContestantSubscriptionData,
  MatchSeriesContestantSubscriptionData,
  MatchSeriesSubscriptionData,
  MatchSubscriptionData,
  SubscriptionQueryData,
} from '../../types/subscription';
import {
  isMatchContestantSubscriptionData,
  isMatchSeriesContestantSubscriptionData,
  isMatchSeriesSubscriptionData,
  isMatchSubscriptionData,
} from '../utils';
import { useDelay } from './hooks';

export interface SubscriptionParams {
  tournamentIds: string[];
  enabled: boolean;
}

export function useMatchSeriesSubscription({ enabled, tournamentIds }: SubscriptionParams) {
  const delayTime = useDelay(tournamentIds);

  useSubscription<SubscriptionQueryData>(MATCH_SERIES_SUBSCRIPTION_QUERY, {
    skip: !enabled,
    variables: { tournamentIds },
    onData: ({ data, client }) => {
      const subData = data.data?.fixtureDataEvents.data;
      if (!subData) {
        return;
      }

      setTimeout(() => {
        const prev = client.readQuery<MatchSeriesQueryResponse>({
          query: MATCH_SERIES_QUERY,
          variables: { tournamentIds },
        });

        if (!prev) {
          return;
        }

        const next = structuredClone(prev);
        switch (true) {
          case isMatchSeriesSubscriptionData(subData):
            patchMatchSeriesData(next, subData);
            break;
          case isMatchSubscriptionData(subData):
            patchMatchData(next, subData);
            break;
          case isMatchSeriesContestantSubscriptionData(subData):
            patchMatchSeriesContestantData(next, subData);
            break;
          case isMatchContestantSubscriptionData(subData):
            patchMatchContestantData(next, subData);
            break;
          default:
            return;
        }

        client.writeQuery({ query: MATCH_SERIES_QUERY, variables: { tournamentIds }, data: next });
      }, delayTime);
    },
  });
}

function patchMatchSeriesData(prev: MatchSeriesQueryResponse, subData: MatchSeriesSubscriptionData) {
  const matchSeries = prev.matchSeries.items.find((ms) => ms.id === subData.id);
  if (matchSeries) {
    matchSeries.startTime = subData.startTime;
    matchSeries.status = subData.matchSeriesStatus;
  }
}

function patchMatchData(prev: MatchSeriesQueryResponse, subData: MatchSubscriptionData) {
  for (const ms of prev.matchSeries.items) {
    const match = ms.matches.find((m) => m.id === subData.id);
    if (match) {
      match.status = subData.matchStatus;
      return;
    }
  }
}

function patchMatchSeriesContestantData(
  prev: MatchSeriesQueryResponse,
  subData: MatchSeriesContestantSubscriptionData,
) {
  for (const ms of prev.matchSeries.items) {
    const contestant = ms.contestants.find((c) => c.id === subData.id);
    if (contestant) {
      contestant.points = subData.points;
      contestant.rank = subData.rank;
      contestant.result = subData.matchSeriesContestantResult;
      contestant.score = subData.score;
      return;
    }
  }
}

function patchMatchContestantData(prev: MatchSeriesQueryResponse, subData: MatchContestantSubscriptionData) {
  for (const ms of prev.matchSeries.items) {
    for (const m of ms.matches) {
      const contestant = m.contestants.find((c) => c.id === subData.id);
      if (contestant) {
        contestant.points = subData.points;
        contestant.rank = subData.rank;
        contestant.result = subData.matchContestantResult;
        contestant.score = subData.score;
        return;
      }
    }
  }
}
