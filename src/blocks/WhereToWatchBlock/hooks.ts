import uniqBy from 'lodash/uniqBy';
import { useMemo } from 'react';

import { useCurrentLocale } from '@/hooks/i18n';
import { useGameTournamentsData } from '@/services/graphql/hooks/tournaments';
import { StreamProviderType } from '@/strapi/types/collection/streamProvider';

import { getLanguageFromCode } from './utils';

export interface StreamsByLanguage {
  language: string;
  languageCode: string;
  streamUrls: string[];
}

export function useGameStreams(providers: StreamProviderType[]) {
  const { data, loading } = useGameTournamentsData({ isSubscriptionDisabled: true });
  const locale = useCurrentLocale();

  const streamsByLanguage = useMemo(() => {
    if (!data) {
      return [];
    }

    const allStreams =
      data?.tournaments.result?.flatMap((t) =>
        (t.streams ?? []).map((s) => ({
          url: s.url,
          languageCode: s.language,
          language: getLanguageFromCode(s.language, locale),
        })),
      ) ?? [];
    const uniqueStreams = uniqBy(allStreams, (s) => s.url.replace('www.', '').replace('https', 'http'));

    const streamsByLanguage: StreamsByLanguage[] = [];
    for (const stream of uniqueStreams) {
      const existingLanguage = streamsByLanguage.find((sl) => sl.languageCode === stream.languageCode);
      if (existingLanguage) {
        existingLanguage.streamUrls.push(stream.url);
        continue;
      }

      streamsByLanguage.push({
        languageCode: stream.languageCode,
        language: stream.language,
        streamUrls: [stream.url],
      });
    }

    streamsByLanguage.sort((a, b) => sortByLanguagePrecedence(a.languageCode, b.languageCode));
    streamsByLanguage.forEach((sl) => sl.streamUrls.sort((a, b) => sortByProviderPrecedence(a, b, providers)));

    return streamsByLanguage;
  }, [data, locale, providers]);

  return { streamsByLanguage, isLoading: loading };
}

function sortByLanguagePrecedence(aCode: string, bCode: string) {
  const precedence: Record<string, number> = { en: 0, ar: 1, zh: 2 };
  const aRank = precedence[aCode] ?? 3;
  const bRank = precedence[bCode] ?? 3;

  if (aRank !== bRank) {
    return aRank - bRank;
  }
  return aCode.localeCompare(bCode);
}

function sortByProviderPrecedence(a: string, b: string, providers: StreamProviderType[]) {
  const aProvider = providers.find((p) => p.regex && new RegExp(p.regex).test(a));
  const bProvider = providers.find((p) => p.regex && new RegExp(p.regex).test(b));

  return (aProvider?.priority ?? 99) - (bProvider?.priority ?? 99);
}
