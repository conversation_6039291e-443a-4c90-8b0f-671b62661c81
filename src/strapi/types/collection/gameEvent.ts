import { EventType } from '@/strapi/types/helper/eventType';

import { StrapiBase } from '../base';
import { RichTextContentType } from '../helper';
import { GameType } from './game';

export interface GameEventType extends StrapiBase {
  title: string | null;
  format: string | null;
  summary: RichTextContentType;
  startDate: string | null;
  endDate: string | null;
  startDateTime: string | null;
  endDateTime: string | null;
  game: GameType | null;
  hideGameName: boolean;
  venue: string | null;
  eventType: EventType;
  tournamentId: string | null;
}
