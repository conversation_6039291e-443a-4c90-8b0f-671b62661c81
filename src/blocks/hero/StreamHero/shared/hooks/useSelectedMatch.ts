import { useEffect } from 'react';

import { useSelectedMatch as useSelectedMatchProvider } from '@/app/[locale]/(app)/competitions/[slug]/_components/SelectedMatchProvider';
import { StreamType } from '@/strapi/types/collection/stream';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { GameStream } from '../types/GameStream';
import { StreamHeroMatch } from '../types/StreamHeroMatch';
import { useCountdown } from './useCountdown';

export function useSelectedMatch(
  matches: StreamHeroMatch[],
  selectedStream: GameStream | StreamType | null | undefined,
) {
  const { selectedMatchId, setSelectedMatchId } = useSelectedMatchProvider();
  const { isLive } = useCountdown(selectedStream?.startTime ?? null, selectedStream?.endTime ?? null);

  useEffect(() => {
    if (matches.length === 0) {
      return;
    }

    const noneMatchSelected = selectedMatchId === '';
    if (noneMatchSelected) {
      setSelectedMatchId(getInitialSelectedMatchId(matches, isLive));
    }
  }, [matches, isLive, selectedMatchId, setSelectedMatchId]);

  return { selectedMatchId, setSelectedMatchId };
}

function getInitialSelectedMatchId(matches: StreamHeroMatch[], isLive: boolean) {
  const viableStreamedMatch = matches.find((m) => m.status === LocalMatchStatus.LIVE && m.stream);

  if (viableStreamedMatch) {
    return viableStreamedMatch.id;
  }

  const firstMatch = matches.find((m) => m.status === LocalMatchStatus.UPCOMING && m.stream);
  if (isLive && firstMatch) {
    return firstMatch.id;
  }

  return '';
}
