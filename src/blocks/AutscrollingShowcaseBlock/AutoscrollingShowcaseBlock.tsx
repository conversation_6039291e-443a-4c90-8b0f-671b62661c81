import './slick-slide.css';

import { AutoscrollingShowcaseType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { DescriptionSection } from './DescriptionSection';
import { ShowcaseGrid } from './ShowcaseGrid';

export const AutoscrollingShowcaseBlock = ({ section, clubs, ...rest }: AutoscrollingShowcaseType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div className="relative flex w-full gap-7 max-lg:flex-col lg:items-center">
        <DescriptionSection {...rest} />
        <ShowcaseGrid clubs={clubs} />
      </div>
    </BlockSectionWrapper>
  );
};
