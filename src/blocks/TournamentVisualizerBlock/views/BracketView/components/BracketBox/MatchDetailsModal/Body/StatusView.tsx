import clsx from 'clsx';

import { useDateLocaleFormatter } from '@/hooks/i18n/useDateLocale';
import { JsonFieldType } from '@/strapi/types/helper';
import { LocalMatchStatus } from '@/utils/matchSeries';

interface Props {
  status: LocalMatchStatus;
  startTime: string;
  translations: JsonFieldType;
}

export const StatusView = ({ status, startTime, translations }: Props) => {
  const format = useDateLocaleFormatter();
  return (
    <div className="flex flex-col items-center gap-0.5 text-[10px] md:gap-1.5">
      <div
        className={clsx('w-fit rounded-sm px-[3px] py-0.5', {
          'text-dark-default bg-white': status === LocalMatchStatus.COMPLETED,
          'bg-[#F40F30] text-white': status === LocalMatchStatus.LIVE,
          'bg-dark-default text-white': status === LocalMatchStatus.UPCOMING,
        })}
      >
        <p className="font-primary leading-[1.1] font-bold uppercase">{translations[status] ?? status}</p>
      </div>
      <p className="font-base leading-none font-extrabold text-black">
        {format(startTime, 'MMMM d, yyyy - HH:mm zzz')}
      </p>
    </div>
  );
};
