import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';

import PlaceholderLogo from '@/ui/assets/images/ewc-placeholder.png';
import { isWinningResult } from '@/utils/matchSeries';

import { RoundRobinMatch } from '../../../utils/roundRobin/types';
import { Contestant } from '../../../utils/types';

export const MatchRow = ({ contestants }: RoundRobinMatch) => {
  const winner = contestants.find((c) => isWinningResult(c.result));

  return (
    <div className="flex gap-1">
      {contestants.map((c, i) => (
        <MatchContestant key={c.id} {...c} isReversed={i === 1} isWinner={c.id === winner?.id} />
      ))}
    </div>
  );
};

type MatchContestantProps = Contestant & { isWinner: boolean; isReversed: boolean };

const MatchContestant = ({ name, logoUrl, score, isWinner, isReversed }: MatchContestantProps) => {
  return (
    <div
      className={clsx(
        'flex flex-1 items-center gap-2 bg-white p-0.5',
        'font-primary leading-none font-bold uppercase',
        isReversed && 'flex-row-reverse',
      )}
    >
      <p className={clsx('text-dark-default w-full text-xs', isReversed ? 'text-start' : 'text-end')}>{name}</p>
      <Image alt={`${name} logo`} className="size-5" height={24} src={logoUrl ?? PlaceholderLogo} width={24} />
      <p
        className={clsx(
          'flex size-6 shrink-0 items-center justify-center rounded-xs text-sm',
          isWinner ? 'bg-dark-default text-white' : 'bg-white-dirty text-dark-default',
        )}
      >
        {score ?? '-'}
      </p>
    </div>
  );
};
