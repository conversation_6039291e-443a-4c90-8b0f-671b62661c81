'use client';

import Script from 'next/script';

export function GoogleAdsTag() {
  return (
    <>
      <Script async src="https://www.googletagmanager.com/gtag/js?id=AW-17198840706" strategy="afterInteractive" />
      <Script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'AW-17198840706');
          `,
        }}
        id="google-ads-init"
        strategy="afterInteractive"
      />
    </>
  );
}
