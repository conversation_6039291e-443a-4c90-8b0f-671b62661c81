import { redirect } from 'next/navigation';

import { Locale } from '@/hooks/i18n/const';
import { RedirectType } from '@/strapi/types/collection/redirect';

import { getStrapiDefaultParams, strapiClient } from '../client';

export async function getRedirects(locale: Locale) {
  try {
    const redirectData = await strapiClient.collection('redirects').find({
      fields: ['fromPath', 'toPath', 'enabled'],
      ...getStrapiDefaultParams(locale),
    });

    const data = redirectData.data as RedirectType[];
    if (!data) {
      return [];
    }

    const enabledRedirects = data.filter((d) => d.enabled);
    return enabledRedirects;
  } catch (error) {
    console.error('Redirects not fetched!', error);
    throw new Error('Redirects not fetched!');
  }
}

export async function redirectAccordingToConfig(path: string, locale: Locale) {
  const redirects = await getRedirects(locale);
  const matchedRedirect = redirects.find(({ fromPath }) => fromPath === path);

  if (matchedRedirect?.toPath) {
    let redirectTo = matchedRedirect.toPath;
    if (redirectTo.startsWith('/') && !redirectTo.startsWith(`/${locale}`)) {
      redirectTo = `/${locale}${redirectTo}`;
    }

    redirect(redirectTo);
  }
}
