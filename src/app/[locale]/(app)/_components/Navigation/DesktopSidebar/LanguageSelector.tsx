'use client';

import clsx from 'clsx';
import { RefObject, useRef } from 'react';
import { FaGlobeAmericas } from 'react-icons/fa';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import { localeTextMap } from '@/components/LanguageDropdown/const';
import { useCurrentLocale, useReplaceLocale } from '@/hooks/i18n';
import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';
import { logButtonClickedEvent } from '@/services/braze';

export const LanguageSelector = ({ disabledLocales }: { disabledLocales?: Locale[] }) => {
  const [isOpen, toggleIsOpen, setIsOpen] = useToggle(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref as RefObject<HTMLElement>, () => setIsOpen(false));

  const locale = useCurrentLocale();
  const replaceLocale = useReplaceLocale();
  const otherLocales = ALL_LOCALES.filter((l) => l !== locale && !disabledLocales?.includes(l));

  return (
    <div className="relative" ref={ref}>
      <button
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label={`Change language from ${localeTextMap[locale]}`}
        className="bg-gray-easy flex w-full cursor-pointer flex-col items-center justify-center gap-1 px-3 py-2.5"
        onClick={toggleIsOpen}
      >
        <FaGlobeAmericas className="text-[#272727]" size={26} />
        <span className="font-primary text-dark-default text-[10px] leading-none capitalize">
          {localeTextMap[locale]}
        </span>
      </button>
      <div
        aria-orientation="vertical"
        className={clsx(
          'absolute start-[calc(100%+10px)] top-0 flex min-w-[240px] flex-col gap-0.5 rounded-2xl bg-white p-1 shadow-md transition-opacity duration-200',
          isOpen ? 'pointer-events-auto opacity-100' : 'pointer-events-none opacity-0',
        )}
        role="menu"
      >
        {otherLocales.map((l) => (
          <button
            className="hover:bg-white-dirty flex w-full cursor-pointer items-center gap-2 rounded-lg p-3 text-left transition-colors"
            key={l}
            role="menuitem"
            tabIndex={0}
            onClick={() => {
              replaceLocale(l);
              setIsOpen(false);
              logButtonClickedEvent({
                location: 'Desktop Sidebar Navigation - Language Selector',
                button_name: `Change Language to ${localeTextMap[l]} Button`,
              });
            }}
          >
            <FaGlobeAmericas className="text-[#272727]" size={26} />
            <span className="font-primary text-dark-default text-[10px] leading-none font-bold capitalize">
              {localeTextMap[l]}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};
