import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { PostBlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { NewsPageHeader } from '@/blocks/shared/Post/PostPageHeader';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { fetchNewsArticleSeo, fetchSingleNewsArticle } from '@/strapi/api/collection/news';
import { redirectAccordingToConfig } from '@/strapi/api/collection/redirect';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale<{ slug: string }>): Promise<Metadata> {
  const { locale, slug } = await params;
  const seo = await fetchNewsArticleSeo(locale, slug);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | News',
    description: seo?.metaDescription ?? 'Esports World Cup | News',
  };
}

export default async function SingleNewsPage({ params }: PageProps<{ slug: string }>) {
  const { locale, slug } = await params;
  await redirectAccordingToConfig(`/news/${slug}`, locale);

  const article = await fetchSingleNewsArticle(locale, slug);
  const siteConfig = await getSiteConfig(locale);

  if (!article) {
    return notFound();
  }

  const { title, date, summary, cover, blocks } = article;
  return (
    <div className="flex flex-col items-center gap-8 py-30 lg:gap-12">
      <div className="flex px-4 lg:px-8 xl:px-35 2xl:justify-center">
        <div className="w-full xl:max-w-[1158px]">
          <NewsPageHeader cover={cover} date={date} intro={summary} title={title} />
        </div>
      </div>
      {blocks.map((b) => (
        <PostBlockRenderer {...b} {...siteConfig} key={`${b.__component}-${b.id}`} />
      ))}
    </div>
  );
}
