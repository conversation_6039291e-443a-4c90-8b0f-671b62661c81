import Image from 'next/image';

import { JsonFieldType } from '@/strapi/types/helper';
import PlaceholderLogo from '@/ui/assets/images/ewc-placeholder.png';

import { FfaContestant } from '../../utils/ffa/types';

interface Props {
  contestants: FfaContestant[];
  translations: JsonFieldType;
  isConstrictedTable: boolean;
}

export const Table = ({ contestants, translations, isConstrictedTable }: Props) => {
  return (
    <table>
      <thead>
        <tr className="font-primary py-0.5 text-[8px] leading-none font-bold uppercase md:text-[9px]">
          <th className="w-5 px-1.5 text-start md:px-3">#</th>
          <th className="px-1.5 text-start uppercase md:px-3">{translations['team'] ?? 'Team'}</th>
          {!isConstrictedTable && (
            <th className="w-10 px-1.5 uppercase md:px-3">{translations['placementPointsAbbr'] ?? 'PP'}</th>
          )}
          {!isConstrictedTable && (
            <th className="w-10 px-1.5 uppercase md:px-3">{translations['killPointsAbbr'] ?? 'KP'}</th>
          )}
          <th className="w-10 px-1.5 uppercase md:px-3">{translations['total'] ?? 'total'}</th>
        </tr>
      </thead>
      <tbody className="before:block before:h-0.5 before:w-full md:before:h-1">
        {contestants.map((c) => (
          <Row key={c.id} {...c} isConstrictedTable={isConstrictedTable} />
        ))}
      </tbody>
    </table>
  );
};

const Row = ({
  name,
  logoUrl,
  kills,
  placement,
  points,
  rank,
  isConstrictedTable,
}: FfaContestant & { isConstrictedTable: boolean }) => {
  return (
    <tr className="font-primary text-dark-default border-white-dirty bg-white text-[9px] leading-none font-bold uppercase shadow-md not-last:border-b-2 md:text-[10px] md:not-last:border-b-4">
      <td className="rounded-s-xs px-1.5 py-1 md:rounded-s-sm md:px-3">{rank ? `${rank}.` : '-'}</td>
      <td className="px-1.5 py-1 md:px-3">
        <div className="5 flex items-center gap-2">
          <Image alt="logo" className="size-5 md:size-8" height={32} src={logoUrl ?? PlaceholderLogo} width={32} />
          <p className="text-[10px]">{name}</p>
        </div>
      </td>
      {!isConstrictedTable && <td className="px-1.5 text-center md:px-3">{placement}</td>}
      {!isConstrictedTable && <td className="px-1.5 text-center md:px-3">{kills}</td>}
      <td className="rounded-e-xs px-1.5 text-center md:rounded-e-sm md:px-3">{points}</td>
    </tr>
  );
};
