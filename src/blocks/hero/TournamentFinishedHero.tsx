import { ScrollForMoreFloat } from '@/blocks/hero/ScrollForMoreFloat';
import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import { HeroTournamentFinishedBlockType } from '@/strapi/types/hero';
import { Only } from '@/ui/components/Only';
import { isVideoMedia } from '@/utils/media';

export function TournamentFinishedHero({
  backgroundMedia,
  dateHeading,
  logo,
  subtitle,
  scrollForMoreText,
  stats,
}: HeroTournamentFinishedBlockType) {
  return (
    <section className="font-primary relative z-10 font-bold">
      <div className="absolute start-0 top-0 -z-10 size-full">
        {isVideoMedia(backgroundMedia.url) ? (
          <StrapiVideo autoPlay className="size-full object-cover" loop muted video={backgroundMedia} />
        ) : (
          <StrapiImage className="size-full object-cover" image={backgroundMedia} />
        )}
      </div>
      <div className="px-4 pt-[200px] pb-[100px] lg:px-8 lg:py-[126px] xl:py-[120px] 2xl:py-[204px]">
        <div className="mx-auto flex w-fit max-w-6xl flex-col items-center gap-5 md:gap-8">
          <div className="to-gold-primary rounded-[9px] bg-gradient-to-r from-[#4E442D] px-2.5 py-1.5">
            <p className="text-sm leading-tight tracking-tight text-white uppercase md:text-base">{dateHeading}</p>
          </div>
          <StrapiImage
            alt="esports world cup logo"
            className="aspect-[5/1] w-[630px] xl:w-[834px] 2xl:w-[1024px]"
            image={logo}
          />
          <div className="flex flex-wrap justify-center gap-x-2 gap-y-4">
            {stats?.map((stat, idx) => (
              <div
                className="mb-2 flex w-fit flex-col items-center justify-center overflow-hidden rounded-[18px] bg-white/10 p-4 text-white backdrop-blur-2xl"
                key={idx}
              >
                <span className="font-primary font-[28px] md:text-[36px] lg:text-[64px]">{stat.mainText}</span>
                <span className="font-base text-[12px] md:text-[14px]">{stat.subText}</span>
              </div>
            ))}
          </div>
          <div className="max-w-[802px] text-center">
            <p className="text-sm leading-tight text-white uppercase md:text-[21px]">{subtitle}</p>
          </div>
        </div>
      </div>
      {scrollForMoreText && (
        <Only for="xlAndAbove">
          <ScrollForMoreFloat text={scrollForMoreText} />
        </Only>
      )}
    </section>
  );
}
