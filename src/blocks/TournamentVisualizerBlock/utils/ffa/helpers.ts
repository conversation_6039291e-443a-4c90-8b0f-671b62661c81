import {
  MatchContestant,
  MatchSeries,
  MatchSeriesContestant,
  MatchSeriesMetadata,
  MatchSeriesMetadataType,
} from '@/services/graphql/types/matchSeries';
import { constructImageUrl } from '@/services/graphql/utils';
import { JsonFieldType } from '@/strapi/types/helper';
import { convertToLocalMatchStatus, LocalMatchStatus } from '@/utils/matchSeries';

import { getPlaceholderContestants } from '../const';
import { Config, Contestant } from '../types';
import { OVERALL_TABLE_ID } from './const';
import { FfaContestant, FfaGroup, FfaMatch } from './types';

export function getGroupDescriptor(metadata: MatchSeriesMetadata[], apiTranslations: JsonFieldType, id: string) {
  const groupMeta = metadata
    .filter((m) => m.type === MatchSeriesMetadataType.GROUP)
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));
  const groupNames = groupMeta.map((m) => apiTranslations[m.id] || m.name).join(' - ');

  const roundMeta = metadata
    .filter((m) => m.type === MatchSeriesMetadataType.ROUND)
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));
  const roundName = roundMeta.map((m) => apiTranslations[m.id] || m.name).join(' - ');

  const name = groupNames ? groupNames : roundName ? roundName : '#';

  return { id, name };
}

export function createGroup(serie: MatchSeries, descriptor: { name: string; id: string }, config: Config): FfaGroup {
  return {
    ...descriptor,
    position: serie.position as number,
    //  resolved to appropriate status based on matches in group
    status: LocalMatchStatus.UPCOMING,
    matches: createMatches(serie, config.translations),
  };
}

export function createMatches(serie: MatchSeries, translations: JsonFieldType) {
  const sortedMatches = serie.matches.toSorted((a, b) => (a.sequence ?? 0) - (b.sequence ?? 0));
  return sortedMatches.map((m, i) => {
    const contestants =
      m.contestants.length !== 0
        ? createContestants(serie.contestants, m.contestants)
        : (getPlaceholderContestants(10, {
            rank: null,
            kills: 0,
            placement: 0,
            points: 0,
            name: translations['tbd'] ?? 'tbd',
          } as Partial<Contestant>) as unknown as FfaContestant[]);

    contestants.sort((a, b) => (a?.rank ?? 999) - (b?.rank ?? 999));

    return {
      id: (i + 1).toString(),
      startTime: serie.startTime,
      status: convertToLocalMatchStatus(m.status),
      title: `${translations['match'] ?? 'Match'} ${i + 1}`,
      contestants,
    } as FfaMatch;
  });
}

export function createContestants(allContestants: MatchSeriesContestant[], matchContestants: MatchContestant[]) {
  const contestants = [];

  const sortedContestants = matchContestants.toSorted((a, b) => (a.rank ?? 0) - (b.rank ?? 0));
  for (const matchContestant of sortedContestants) {
    const allContestant = allContestants.find((c) => c.team.id === matchContestant.team.id);
    const contestant: FfaContestant = {
      id: matchContestant.team.id,
      rank: matchContestant.rank,
      kills: matchContestant.score ?? 0,
      points: matchContestant.points ?? 0,
      placement: (matchContestant.points ?? 0) - (matchContestant.score ?? 0),
      name: allContestant?.team.name ?? 'TBD',
      result: matchContestant.result,
      logoUrl: constructImageUrl(allContestant?.team.images, 'logo_transparent_whitebg'),
    };

    contestants.push(contestant);
  }

  return contestants;
}

export function insertOverallMatchInGroup(
  group: FfaGroup,
  allContestants: MatchSeriesContestant[],
  translations: JsonFieldType,
) {
  const contestants =
    allContestants.length !== 0
      ? allContestants.map((c) => ({
          id: c.team.id,
          rank: c.rank,
          kills: c.score ?? 0,
          points: c.points ?? 0,
          placement: (c.points ?? 0) - (c.score ?? 0),
          name: c?.team.name ?? 'TBD',
          result: c.result,
          logoUrl: constructImageUrl(c?.team.images, 'logo_transparent_whitebg'),
        }))
      : (getPlaceholderContestants(10, {
          rank: null,
          kills: 0,
          placement: 0,
          points: 0,
          name: translations['tbd'] ?? 'tbd',
        } as Partial<Contestant>) as unknown as FfaContestant[]);

  const overallMatch: FfaMatch = { id: OVERALL_TABLE_ID, title: translations['overall'] ?? 'Overall', contestants };

  overallMatch.contestants.sort((a, b) => (a?.rank ?? 999) - (b?.rank ?? 999));
  group.matches.unshift(overallMatch);
}
