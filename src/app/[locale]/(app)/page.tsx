import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { BlockRenderer } from '@/blocks/BlockRenderer';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getFinishedHomepageData } from '@/strapi/api/single/finished-homepage';
import { getHomepageData, getHomepageSeo } from '@/strapi/api/single/homepage';

import { getSiteConfig } from '../../../strapi/api/single/siteConfig';
import { RenderHero } from './_components/RenderHero';
import { SelectedMatchProvider } from './competitions/[slug]/_components/SelectedMatchProvider';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const seo = await getHomepageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function Home({ params }: PageProps) {
  const locale = (await params).locale;
  const finishedHomepageData = await getFinishedHomepageData(locale);
  const siteConfig = await getSiteConfig(locale);

  const data = finishedHomepageData?.showFinishedHomepage ? finishedHomepageData : await getHomepageData(locale);

  if (!data) {
    return notFound();
  }

  const { hero, blocks } = data;
  return (
    <SelectedMatchProvider>
      <RenderHero
        apiTranslations={siteConfig?.apiTranslations ?? {}}
        hero={hero}
        locale={locale}
        translations={siteConfig?.translations ?? {}}
      />
      {blocks.map((b) => (
        <BlockRenderer {...b} {...siteConfig} key={`${b.__component}-${b.id}`} />
      ))}
    </SelectedMatchProvider>
  );
}
