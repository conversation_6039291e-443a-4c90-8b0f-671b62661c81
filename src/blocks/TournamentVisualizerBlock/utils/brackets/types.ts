import { MatchSeriesGameResult } from '@/services/graphql/types/matchSeries';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { Contestant, Group, Phase } from '../types';

export interface BracketPhase extends Phase {
  groups: BracketGroup[];
}

export interface BracketGroup extends Group {
  brackets: Bracket[];
}

export interface Bracket {
  id: string;
  name: string;
  position: number;
  rounds: Round[];
}

export interface Round {
  id: string;
  name: string;
  position: number;
  matches: BracketMatch[];
}

export enum BracketMatchType {
  REGULAR,
  QUALIFIER,
  QUALIFIED,
  FINALS,
  WINNER,
  THIRD_PLACE,
}

export interface BracketMatch {
  id: string;
  status: LocalMatchStatus | string;
  type: BracketMatchType;
  startTime: string;
  topText?: string;
  position: number;
  descriptor: string;
  contestants: BracketContestant[];
  matches: BracketSingleMatch[];
}

export interface BracketSingleMatch {
  map: string;
  teams: { id: string; score: number | null; result: MatchSeriesGameResult }[];
}

export interface BracketContestant extends Contestant {
  result: MatchSeriesGameResult | string;
  players: { name: string; isCoach: boolean }[];
}
