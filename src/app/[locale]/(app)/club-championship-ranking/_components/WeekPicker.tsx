'use client';
import clsx from 'clsx';
import { formatDate, parseISO } from 'date-fns';
import { useRef } from 'react';

import { useScrollToToday } from '@/app/[locale]/(app)/schedule/hooks/useScrollToToday';

import { useDragToScroll } from '../../../../../components/ScrollWithFadeWrapper/useDragToScroll';
import { useDateLocale } from '../../../../../hooks/i18n/useDateLocale';
import { JsonFieldType } from '../../../../../strapi/types/helper';
import { WeekDates } from '../../schedule/_components/utils';
import { getToday } from './utils';

export const WeekPicker = ({
  weekDates,
  setActiveDay,
  activeDay,
  translations,
}: {
  weekDates: WeekDates[];
  setActiveDay: (day: string) => void;
  activeDay: string | null;
  translations?: JsonFieldType | null;
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  useDragToScroll(scrollContainerRef);
  const today = getToday();
  const allDates = weekDates.flatMap((w) => w.dates);

  useScrollToToday({
    allDates,
    scrollContainerRef,
    dayWidth: 100,
    isEnabled: true,
  });

  return (
    <div
      className="hide-scrollbar flex gap-1 divide-x divide-[#DBDBDB] overflow-x-auto select-none"
      ref={scrollContainerRef}
    >
      {weekDates.map((week) => {
        const isDisabled = week.dates.every((day) => day > today);

        return (
          <div className="flex flex-col gap-2.5" key={week.week}>
            <div
              className={clsx(
                'bg-gray font-primary mx-2 flex min-h-5 items-center rounded-xs px-2 text-[10px] leading-[130%] font-bold uppercase',
                {
                  'opacity-30': isDisabled,
                },
              )}
            >
              {/* // TODO: different bg and fg color depending on state  */}
              {translations?.week ?? 'WEEK'} #{week.week}
            </div>

            <div className="flex">
              {week.dates.map((day) => {
                const isActive = activeDay === day;
                const isDisabled = !isActive && day > today;
                return (
                  <div
                    className={clsx('min-w-[100px]', !isDisabled && !isActive && 'cursor-pointer')}
                    key={day}
                    onClick={isDisabled || isActive ? undefined : () => setActiveDay(day)}
                  >
                    <DateDescriptorCell date={day} isDisabled={isDisabled} isSelected={isActive} translations={null} />
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export const DateDescriptorCell = ({
  date,
  translations,
  isSelected,
  isDisabled,
}: {
  date: string;
  translations: JsonFieldType | null;
  isSelected: boolean;
  isDisabled: boolean;
}) => {
  const locale = useDateLocale();
  const today = getToday();
  return (
    <div
      className={clsx('flex flex-col gap-0.5 rounded-sm ps-2', date === today ? 'pt-0' : 'pt-3.5', {
        'bg-dark-default text-white': isSelected,
        'text-dark-default': !isSelected,
        'opacity-30': isDisabled,
      })}
    >
      {date === today && (
        <p className="w-fit rounded-[2px] bg-[#F2C575] px-0.5 text-[8px] font-extrabold uppercase">
          {translations?.today ?? 'Today'}
        </p>
      )}
      <div className="flex flex-col">
        <p className="font-primary text-lg leading-[1] font-bold">{formatDate(parseISO(date), 'do', { locale })}</p>
        <div className="flex gap-1 text-xs uppercase">
          <p className="font-normal">{formatDate(parseISO(date), 'MMM', { locale })}</p>
          <p className="font-extrabold">{formatDate(parseISO(date), 'iii', { locale })}</p>
        </div>
      </div>
    </div>
  );
};
