import { LocalizedLink } from '@/components/LocalizedLink';

import { ClubChampionshipTableProps } from '../ClubChampionshipTable';
import { MedalsSection } from './MedalsSection';
import { OverviewSection } from './OverviewSection';
import { PointsSection } from './PointsSection';

export type TableProps = Pick<
  ClubChampionshipTableProps,
  'winner' | 'competitions' | 'moneyWon' | 'pointsWon' | 'button' | 'translations'
>;

export const Table = (props: TableProps) => {
  const { winner, button } = props;
  return (
    <div className="overflow-clip rounded-lg bg-white md:rounded-2xl lg:rounded-4xl">
      <div className="flex flex-col">
        <OverviewSection {...props} />
        <MedalsSection {...winner.medals} />
        <PointsSection {...props} />
        {button && (
          <LocalizedLink
            brazeEventProperties={{
              button_name: `Bottom Section Button Link (${button.link})`,
              location: 'Club Championship Overview Block - Table',
            }}
            href={button.link ?? '#'}
            target={button.openInNewTab ? '_blank' : undefined}
          >
            <button className="bg-dark-default flex h-16 w-full cursor-pointer items-center justify-center">
              <p className="text-button-default text-white">{button.text}</p>
            </button>
          </LocalizedLink>
        )}
      </div>
    </div>
  );
};
