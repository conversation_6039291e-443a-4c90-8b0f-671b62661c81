'use client';

import clsx from 'clsx';
import { useState } from 'react';
import { useIsClient } from 'usehooks-ts';

import { logButtonClickedEvent } from '@/services/braze';
import { NavigationItem } from '@/strapi/types/helper/navigationItem';

import { MenuComponent } from './MenuComponent';

interface Props {
  label: string;
  icon: string;
  items: NavigationItem[];
  isEmbedded?: boolean;
}

export const MoreMenu = ({ label, icon, items, isEmbedded }: Props) => {
  const [isMoreMenuOpen, setIsMoreMenuOpen] = useState(false);
  const isClient = useIsClient();

  return (
    <div className={clsx('relative', !isClient && 'invisible')}>
      <button
        className={clsx(
          'group/more-menu relative h-full w-full rounded-2xl p-3',
          'flex flex-col items-center justify-center gap-1',
          'hover:shadow-sidebar-item cursor-pointer text-center',
        )}
        onClick={() => {
          setIsMoreMenuOpen(true);
          logButtonClickedEvent({
            location: 'Desktop Sidebar Navigation',
            button_name: `More Menu Button`,
          });
        }}
      >
        <span className="material-symbols-outlined text-dark-default group-hover/more-menu:text-gold-primary text-[26px]!">
          {icon}
        </span>
        <span className="font-base text-dark-default text-[10px] leading-none font-bold capitalize">{label}</span>
      </button>
      <MenuComponent
        isEmbedded={isEmbedded}
        isOpen={isMoreMenuOpen}
        items={items}
        onClose={() => setIsMoreMenuOpen(false)}
      />
    </div>
  );
};
