import clsx from 'clsx';
import React from 'react';

import Arrow from '@/ui/assets/icons/arrow.svg';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

type ScrollLabelProps = {
  onClick?: () => void;
  textColor: string;
  bgColor: string;
  text: string;
  position: 'top' | 'bottom';
};

export function ScrollLabel({ onClick, textColor, bgColor, text, position }: ScrollLabelProps) {
  const { isSm, isMd } = useScreenType();

  if (isSm || isMd) return null;

  return (
    <div
      className={clsx(
        'pointer-events-none sticky z-50 flex w-full justify-center bg-transparent max-lg:hidden',
        position === 'top' ? 'top-5' : 'bottom-5',
      )}
    >
      <button
        className={clsx(
          'font-riforma pointer-events-auto flex items-center gap-1 rounded-full px-3 py-2 text-[10px] font-bold uppercase',
          bgColor,
          textColor,
          onClick ? 'cursor-pointer' : 'cursor-default',
        )}
        type="button"
        onClick={onClick}
        {...(!onClick && { tabIndex: -1 })}
      >
        {position === 'top' ? (
          <Arrow className={clsx('text-[14px]')} />
        ) : (
          <Arrow className={clsx('rotate-180 text-[14px]')} />
        )}
        <span>{text}</span>
      </button>
    </div>
  );
}
