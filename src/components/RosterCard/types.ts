import { MediaType } from '@/strapi/types/media';

export interface Roster {
  team: { id: string; name: string; rank: number | null; players: RosterPlayer[] };
  game: {
    id: string;
    name: string | null;
    slug: string | null;
    logoLight: MediaType | null;
    logoDark: MediaType | null;
    teamsCount: number;
    isFinished: boolean;
    startDate: string | null;
    endDate: string | null;
  };
}

export interface RosterPlayer {
  name: string;
  isCoach?: boolean;
}
