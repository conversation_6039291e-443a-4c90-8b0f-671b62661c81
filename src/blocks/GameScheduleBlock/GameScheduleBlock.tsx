'use client';

import { format } from 'date-fns';
import { groupBy } from 'lodash';

import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { useMatchSeriesData } from '@/services/graphql/hooks/matchSeries';
import { TournamentVariant } from '@/services/graphql/types/matchSeries';
import { GameScheduleBlockType } from '@/strapi/types/gamePageBlock';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { MatchSeriesCard } from './MatchSeriesCard';
import { MatchSeriesCardFFA } from './MatchSeriesCardFFA';

const competitionVariantMapper = {
  [TournamentVariant.FFA_SQUAD]: 'ffa',
  [TournamentVariant.FFA_DUO]: 'ffa',
  [TournamentVariant.FFA_SOLO]: 'ffa',
  [TournamentVariant.CUSTOM_TEAM]: 'teams',
  [TournamentVariant['10_ON_10']]: 'teams',
  [TournamentVariant['11_ON_11']]: 'teams',
  [TournamentVariant['12_ON_12']]: 'teams',
  [TournamentVariant['1_ON_1']]: 'teams',
  [TournamentVariant['1_ON_1_TEAM']]: 'teams',
  [TournamentVariant['15_ON_15']]: 'teams',
  [TournamentVariant['3_ON_3']]: 'teams',
  [TournamentVariant['5_ON_5']]: 'teams',
  [TournamentVariant['6_ON_6']]: 'teams',
  [TournamentVariant['7_ON_7']]: 'teams',
};

type GameScheduleBlockProps = GameScheduleBlockType & { liveUpdatesDisabled?: boolean; gameLink?: string };

export const GameScheduleBlock = ({
  competitions,
  section,
  translations,
  apiTranslations,
  liveUpdatesDisabled,
  gameLink,
}: GameScheduleBlockProps) => {
  const { data, loading, error } = useMatchSeriesData(competitions?.map((c) => c.competitionId) ?? [], {
    isSubscriptionDisabled: liveUpdatesDisabled,
  });
  const locale = useDateLocale();

  if (loading) return null;
  if (error) return null;
  if (!data || !data.matchSeries || data.matchSeries.items.length === 0) return null;

  const groupedData = groupBy(
    data.matchSeries.items,
    (item) => item.startTime && format(new Date(item.startTime), 'yyyy-MM-dd'),
  );

  return (
    <BlockSectionWrapper {...section}>
      {competitions &&
        competitions?.length > 0 &&
        Object.entries(groupedData).map(([date, matchSeries]) => {
          return (
            <div className="flex flex-col gap-2.5" key={date}>
              <h4 className="font-riforma text-2xl font-bold max-md:text-[21px]" key={date}>
                {format(new Date(date), 'EEE, MMM do', { locale })}
              </h4>
              <div className="flex flex-col gap-2.5">
                {matchSeries.map((ms) =>
                  competitionVariantMapper[ms.tournament.variant as TournamentVariant] === 'ffa' ? (
                    <MatchSeriesCardFFA
                      apiTranslations={apiTranslations}
                      key={ms.id}
                      matchSeries={ms}
                      translations={translations}
                    />
                  ) : (
                    <MatchSeriesCard
                      apiTranslations={apiTranslations}
                      gameLink={gameLink}
                      key={ms.id}
                      matchSeries={ms}
                      translations={translations}
                    />
                  ),
                )}
              </div>
            </div>
          );
        })}
    </BlockSectionWrapper>
  );
};
