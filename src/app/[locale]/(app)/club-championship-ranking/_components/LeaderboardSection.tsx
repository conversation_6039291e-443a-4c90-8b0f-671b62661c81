'use client';
import { BlocksContent } from '@strapi/blocks-react-renderer';
import React, { useMemo, useState } from 'react';

import { Tournament } from '@/services/graphql/types/tournament';
import { GameType } from '@/strapi/types/collection/game';

import { SearchInput } from '../../../../../components/SearchInput';
import { JsonFieldType } from '../../../../../strapi/types/helper';
import { hasRichTextContent } from '../../../../../strapi/utils';
import { generateWeekDatesBetween } from '../../schedule/_components/utils';
import { useSimulator } from '../_context/SimulatorContext';
import { calculateRankingsForDay } from '../_utils/calculate-historical-rankings';
import { simulatePoints } from '../_utils/simulator';
import { ExplainerText } from './HelperComponents/ExplainerText';
import { LeaderboardTable } from './LeaderboardTable';
import { getToday } from './utils';
import { WeekPicker } from './WeekPicker';

export const LeaderboardSection = ({
  middleExplainerText,
  tournamentStart,
  tournamentEnd,
  translations,
  tournaments,
  gameCompetitions,
}: {
  middleExplainerText: BlocksContent | null;
  tournamentStart: string;
  tournamentEnd: string;
  translations?: JsonFieldType | null;
  tournaments: Tournament[];
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo' | 'title'>[];
}) => {
  const [searchValue, setSearchValue] = useState('');
  const { isSimulatorMode, simulatedRankings } = useSimulator();

  const today = getToday();
  const initialActiveDay = tournamentStart > today ? tournamentStart : today;
  const [activeDay, setActiveDay] = useState<string>(initialActiveDay);

  const weekDates = useMemo(
    () => generateWeekDatesBetween(tournamentStart, tournamentEnd),
    [tournamentStart, tournamentEnd],
  );

  const activeRankings = isSimulatorMode
    ? simulatedRankings.length > 0
      ? simulatedRankings
      : simulatePoints(calculateRankingsForDay(tournaments, today, gameCompetitions))
    : calculateRankingsForDay(tournaments, activeDay, gameCompetitions);

  return (
    <div className="flex flex-col gap-4">
      <SearchInput
        analyticsLocation="Club Championship Ranking - Search"
        placeholder="Search clubs..."
        value={searchValue}
        variant="flat"
        onChange={setSearchValue}
      />
      {!isSimulatorMode && (
        <WeekPicker
          activeDay={activeDay}
          setActiveDay={setActiveDay}
          translations={translations}
          weekDates={weekDates}
        />
      )}
      {middleExplainerText && hasRichTextContent(middleExplainerText) && !isSimulatorMode && (
        <ExplainerText content={middleExplainerText} paragraphClassName="text-gold-primary" />
      )}

      <LeaderboardTable rankings={activeRankings} searchValue={searchValue} translations={translations} />
    </div>
  );
};
