import {
  Match<PERSON><PERSON>,
  MatchSeries<PERSON>ontestant,
  MatchSeriesMatch,
  MatchSeriesMetadata,
} from '@/services/graphql/types/matchSeries';
import { constructImageUrl } from '@/services/graphql/utils';
import { JsonFieldType } from '@/strapi/types/helper';
import { getLocalizedMatchDescriptor } from '@/utils/localization/getLocalizedMatchDescriptor';
import { convertToLocalMatchStatus, LocalMatchStatus } from '@/utils/matchSeries';

import { getPlaceholderContestants } from '../const';
import { Config } from '../types';
import {
  Bracket,
  BracketContestant,
  BracketGroup,
  BracketMatch,
  BracketMatchType,
  BracketSingleMatch,
  Round,
} from './types';

interface MetaProps {
  groupMeta: MatchSeriesMetadata;
  bracketMeta: MatchSeriesMetadata;
  roundMeta: MatchSeriesMetadata;
}

export function createGroup(meta: MetaProps, serie: MatchSeries, config: Config): BracketGroup {
  return {
    id: meta.groupMeta.id,
    name: config.apiTranslations[meta.groupMeta.id] ?? (meta.groupMeta.name as string),
    position: meta.groupMeta.position as number,
    //  resolved to appropriate status based on matches in group
    status: LocalMatchStatus.UPCOMING,
    brackets: [createBracket(meta, serie, config)],
  };
}

export function createBracket(meta: MetaProps, serie: MatchSeries, config: Config): Bracket {
  return {
    id: meta.bracketMeta.id,
    name: config.apiTranslations[meta.bracketMeta.id] ?? (meta.bracketMeta.name as string),
    position: meta.bracketMeta.position as number,
    rounds: [createRound(meta, serie, config)],
  };
}

export function createRound(meta: MetaProps, serie: MatchSeries, config: Config): Round {
  return {
    id: meta.roundMeta.id,
    name: config.apiTranslations[meta.roundMeta.id] ?? (meta.roundMeta.name as string),
    position: meta.roundMeta.position as number,
    matches: [createMatch(meta, serie, config)],
  };
}

export function createMatch(meta: MetaProps, serie: MatchSeries, config: Config): BracketMatch {
  const { roundMeta } = meta;
  const type = getBracketRoundType(roundMeta?.name ?? '');
  return {
    id: serie.id,
    status: convertToLocalMatchStatus(serie.status),
    type,
    position: serie.position as number,
    startTime: serie.startTime as string,
    descriptor: getLocalizedMatchDescriptor(serie.metadata, config.apiTranslations),
    contestants: createContestants(serie.contestants, config.translations),
    matches: createSingleMatches(serie.matches, config.translations),
  };
}

function getBracketRoundType(roundName: string) {
  const lowercaseName = roundName.toLowerCase();
  switch (true) {
    case lowercaseName.includes('winner') || lowercaseName.includes('decider'):
      return BracketMatchType.QUALIFIER;
    case ['final', 'grand final'].includes(lowercaseName):
      return BracketMatchType.FINALS;
    case lowercaseName === '3rd place':
      return BracketMatchType.THIRD_PLACE;
    default:
      return BracketMatchType.REGULAR;
  }
}

function createContestants(contestants: MatchSeriesContestant[], translations: JsonFieldType): BracketContestant[] {
  const defaultName = translations['tbd'] ?? 'tbd';

  const matchContestants = contestants.map((c) => ({
    id: c.team.id,
    name: c.team.name ?? defaultName,
    score: c.score,
    result: c.result,
    logoUrl: constructImageUrl(c.team.images, 'logo_transparent_whitebg'),
    players: c.members.map((m) => ({
      name: m.player.name ?? defaultName,
      isCoach: m.role?.name?.toLowerCase() === 'coach',
    })),
  })) as BracketContestant[];

  const placeholdersCount = 2 - matchContestants.length;
  const placeholders = getPlaceholderContestants(placeholdersCount, {
    name: defaultName,
    result: '',
    players: Array.from({ length: 5 }).map(() => ({ name: defaultName, isCoach: false })),
  } as BracketContestant) as BracketContestant[];

  return [...matchContestants, ...placeholders];
}

function createSingleMatches(matches: MatchSeriesMatch[], translations: JsonFieldType): BracketSingleMatch[] {
  const sortedMatches = matches.toSorted((a, b) => (a.sequence ?? 0) - (b.sequence ?? 0));

  return sortedMatches.map((m) => ({
    map: m.gameMap?.name ?? translations['tbd'] ?? 'tbd',
    teams: m.contestants.map((c) => ({ id: c.team.id, score: c.score, result: c.result })),
  }));
}

export function sortMatchGroups(groups: BracketGroup[]) {
  groups.sort((a, b) => a.position - b.position);
  groups.forEach((gm) => {
    gm.brackets.sort((a, b) => a.position - b.position);
    gm.brackets.forEach((b) => {
      b.rounds.sort((a, b) => a.position - b.position);
      b.rounds.forEach((r) => r.matches.sort((a, b) => a.position - b.position));
    });
  });
}

export function patchGroupsStatus(groups: BracketGroup[]) {
  groups.forEach((g) => {
    const groupMatches = g.brackets.flatMap((b) => b.rounds.flatMap((r) => r.matches));

    const hasLiveMatches = groupMatches.some((m) => m.status === LocalMatchStatus.LIVE);
    if (hasLiveMatches) {
      g.status = LocalMatchStatus.LIVE;
      return;
    }

    const hasOpenMatches = groupMatches.some((m) => m.status === LocalMatchStatus.UPCOMING);
    if (hasOpenMatches) {
      g.status = LocalMatchStatus.UPCOMING;
      return;
    }

    g.status = LocalMatchStatus.COMPLETED;
  });
}
