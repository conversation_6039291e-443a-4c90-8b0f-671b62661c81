import { format } from 'date-fns';
import { FaPlay, FaStop } from 'react-icons/fa';
import { MdPinDrop } from 'react-icons/md';
import { useWindowSize } from 'usehooks-ts';

import { RichTextContent } from '@/blocks/RichTextBlock/RichTextContent';
import { GameType } from '@/strapi/types/collection/game';
import { GameEventType } from '@/strapi/types/collection/gameEvent';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';

export function TournamentEventDetails({
  gameEvent,
  game,
  translations,
}: {
  gameEvent: GameEventType;
  game: GameType;
  translations: JsonFieldType | null;
}) {
  const { width } = useWindowSize();
  const start = gameEvent.startDateTime ?? gameEvent.startDate;
  const end = gameEvent.endDateTime ?? gameEvent.endDate;
  const isFullWidthButton = width < 400;

  const formatDateTime = (date: string | null) => (date ? format(new Date(date), 'MMM d, h:mm a') : '');

  const learnMoreLink = game?.isLinkingToGamePageEnabled && game.slug ? `/competitions/${game.slug}` : null;

  return (
    <div className="mt-4 flex flex-col gap-2 rounded-xl bg-white p-4">
      <div className="color-[#151515] flex flex-wrap gap-x-3 text-start text-xs">
        {start && (
          <span className="font-base flex items-center gap-1 text-xs font-bold">
            <FaPlay /> {formatDateTime(start)}
          </span>
        )}
        {end && (
          <span className="font-base flex items-center gap-1 text-xs font-bold">
            <FaStop />
            {formatDateTime(end)}
          </span>
        )}
        {gameEvent.venue && (
          <span className="font-base flex items-center gap-1 text-xs font-bold">
            <MdPinDrop className="align-middle text-base" />
            {gameEvent.venue}
          </span>
        )}
      </div>

      {(gameEvent.title || gameEvent.summary) && (
        <div className="mt-5 flex flex-col gap-2">
          {gameEvent.title && (
            <h3 className="text-h3 text-start text-[28px] font-bold text-black">{gameEvent.title}</h3>
          )}
          {gameEvent.summary && (
            <RichTextContent
              content={gameEvent.summary}
              paragraphClassName="text-sm text-gray-700 text-start font-base font-normal"
            />
          )}
        </div>
      )}

      <div className="mt-4 flex w-full flex-wrap gap-3 max-[350px]:flex-col">
        {learnMoreLink && (
          <Button
            brazeEventProperties={{
              button_name: 'Learn More Button',
              location: 'Tournament Event Dialog',
            }}
            isFullWidth={isFullWidthButton}
            link={learnMoreLink}
            text={translations?.learnMore ?? 'Find out more'}
            variant="primary"
          />
        )}
        {game.ticketsUrl && (
          <Button
            brazeEventProperties={{
              button_name: 'Buy Tickets Button',
              location: 'Tournament Event Dialog',
            }}
            isFullWidth={isFullWidthButton}
            link={game.ticketsUrl}
            text={translations?.buyTickets ?? 'Buy Tickets'}
            variant="gold"
          />
        )}
      </div>
    </div>
  );
}
