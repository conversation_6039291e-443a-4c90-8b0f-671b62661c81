import clsx from 'clsx';
import Image from 'next/image';

import ContestantPlaceholderImage from '@/ui/assets/images/ewc-placeholder.png';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { Contestant } from '../../../../types/StreamHeroMatch';

type ContestantRowProps = Contestant & { status: LocalMatchStatus; isWinner: boolean | null };

export const ContestantRow = ({ name, score, logoUrl, status, isWinner }: ContestantRowProps) => {
  return (
    <div className="flex items-center justify-between gap-2">
      <div
        className={clsx('flex items-center gap-2', status === LocalMatchStatus.COMPLETED && !isWinner && 'opacity-50')}
      >
        <Image alt="" className="object-cover" height={24} src={logoUrl ?? ContestantPlaceholderImage} width={24} />
        <p className="font-primary text-dark-default text-sm leading-none font-bold uppercase">{name}</p>
      </div>
      <p
        className={clsx(
          'font-primary flex size-5 shrink-0 items-center justify-center rounded-xs text-xs leading-none font-bold uppercase',
          {
            'bg-dark-default text-white': status === LocalMatchStatus.COMPLETED && isWinner,
            'bg-gray text-dark-default':
              status === LocalMatchStatus.UPCOMING || (status === LocalMatchStatus.COMPLETED && !isWinner),
            'bg-[#F40F30] text-white': status === LocalMatchStatus.LIVE,
          },
        )}
      >
        {status === LocalMatchStatus.UPCOMING ? '-' : score}
      </p>
    </div>
  );
};
