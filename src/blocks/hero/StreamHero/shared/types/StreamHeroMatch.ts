import { MatchSeriesMetadata, TournamentVariant } from '@/services/graphql/types/matchSeries';
import { Stream } from '@/services/graphql/types/shared';
import { Member } from '@/services/graphql/types/tournament';
import { LocalMatchStatus } from '@/utils/matchSeries';

export interface StreamHeroMatch {
  tournamentName: string;
  tournamentId: string;
  id: string;
  status: LocalMatchStatus;
  startTime: string;
  stream: Stream | null;
  contestants: Contestant[];
  type: TournamentVariant;
  metadata: MatchSeriesMetadata[];
  mapResults: MapResult[];
}

export interface Contestant {
  id: string;
  name: string;
  logoUrl: string | null;
  points: number | null;
  score: number | null;
  rank: number | null;
  isWinner: boolean | null;
  members: Member[];
}

export interface MapResult {
  matchId: string;
  id: string;
  name: string;
  sequence: number | null;
  winnerId: string | null;
  status: LocalMatchStatus;
  results: {
    contestantId: string;
    score: number | null;
    isWinner: boolean;
    rank: number | null;
    points: number | null;
  }[];
}
