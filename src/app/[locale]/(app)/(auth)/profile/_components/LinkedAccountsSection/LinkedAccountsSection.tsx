import { User } from '@/services/auth/types';
import { ProfilePageData } from '@/strapi/api/single/profile/types';
import { JsonFieldType } from '@/strapi/types/helper';

import { ProfilePageSectionContainer } from '../ProfilePageSectionContainer';
import { AvailableAccountsList, LinkedAccountsList } from './AccountsList';

interface Props extends ProfilePageData {
  user: User;
  translations?: JsonFieldType | null;
}

export const LinkedAccountsSection = ({ user, linkedAccounts, translations }: Props) => {
  return (
    <ProfilePageSectionContainer subtitle={linkedAccounts?.subtitle} title={linkedAccounts?.title}>
      <div className="flex gap-8 max-lg:flex-col">
        <LinkedAccountsList
          buttonsCopy={linkedAccounts?.buttons ?? []}
          title="Linked accounts"
          translations={translations}
          user={user}
        />
        <AvailableAccountsList buttonsCopy={linkedAccounts?.buttons ?? []} title="Available accounts" user={user} />
      </div>
      {linkedAccounts?.footerText && (
        <>
          <div className="bg-gray h-px" />
          <p className="font-base text-sm font-medium">{linkedAccounts.footerText}</p>
        </>
      )}
    </ProfilePageSectionContainer>
  );
};
