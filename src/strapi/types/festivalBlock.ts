import { BlockBase } from './block';
import { FestivalType, VenueType } from './collection/festival';
import { WeekTicketsLinkType } from './helper/weekTickets';

export const FESTIVAL_LIST_BLOCK_KEY = 'festival.festival-list-block';
export interface FestivalListBlockType extends BlockBase {
  festival: FestivalType[];
}

export const FESTIVAL_FILTER_BLOCK_KEY = 'festival.festival-filter-block';
export interface FestivalFilterBlockType extends BlockBase {
  venues: VenueType[];
  tournamentStart: string | null;
  tournamentEnd: string | null;
  weekTickets: WeekTicketsLinkType[] | null;
}

export type FestivalPageBlock = FestivalListBlockType;
