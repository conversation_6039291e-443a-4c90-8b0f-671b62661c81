import { MatchSeries, TournamentType, TournamentVariant } from '@/services/graphql/types/matchSeries';

import { addWinnerRoundToGroups, getBracketViewGroups } from './brackets';
import { BracketGroup } from './brackets/types';
import { getFfaViewGroups } from './ffa';
import { isBracketsTournament, isFfaTournament, isRoundRobinTournament } from './helpers';
import { getRoundRobinViewGroups } from './roundRobin';
import { Config, Group, Phase, ViewType } from './types';

interface Params {
  matchSeries?: MatchSeries[];
  initialPhases: Omit<Phase, 'groups' | 'type'>[];
  config: Config;
}

export function getGroupedMatches({ matchSeries, initialPhases, config }: Params) {
  const phases = [...initialPhases] as Phase[];

  if (!matchSeries) {
    return phases;
  }

  for (const phase of phases) {
    const matchesInPhase = matchSeries.filter((d) => d.tournament.id === phase.id);
    const tournamentType = matchesInPhase[0].tournament.type as TournamentType;
    const tournamentVariant = matchesInPhase[0].tournament.variant as TournamentVariant;

    let groups: Group[] = [];
    let viewType = ViewType.ROUND_ROBIN;

    //! this relies on order of conditions
    //  eg. ffa variants can also comply to round robin tournament types
    switch (true) {
      case isFfaTournament(tournamentVariant):
        const sortedMatches = matchesInPhase.toSorted((a, b) => (a.position ?? 0) - (b.position ?? 0));
        groups = getFfaViewGroups(sortedMatches, config);
        viewType = ViewType.FFA;
        break;
      case isBracketsTournament(tournamentType):
        groups = getBracketViewGroups(matchesInPhase, config);

        //! one off case where a different string has to be injected to winner match
        const isHokSpecialCasePhase = phase.id === 'db21c641-02d2-4ca6-bc92-0d10047dff17';
        addWinnerRoundToGroups(groups as BracketGroup[], config, isHokSpecialCasePhase);

        viewType = ViewType.BRACKETS;
        break;
      case isRoundRobinTournament(tournamentType):
      default:
        groups = getRoundRobinViewGroups(matchesInPhase, config);
    }

    phase.groups = groups;
    phase.type = viewType;
  }

  return phases;
}
