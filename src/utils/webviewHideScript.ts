// in consuming components
// <nav className={clsx("fixed z-90 hidden h-[100dvh] items-center justify-center lg:flex", WEBVIEW_HIDE_CLASS)} ref={navigationRef}>
export const WEBVIEW_HIDE_CLASS = 'webview-hide';

// webview-hide class is used to hide footer and header elements when served through webview
// it's loaded through script tag because it needs to be defined early to avoid a flash of unwanted
// elements during loading
// for regular (non-webview) page loads, this css class does nothing
export const webviewHideScript = {
  __html: `
    if (location.search.includes('webview=true')) {
      const style = document.createElement('style');
      style.textContent = '.${WEBVIEW_HIDE_CLASS} { display: none !important; }';
      document.head.appendChild(style);
    }
  `,
};
