import { BlockBase } from './block';
import { StreamProviderType } from './collection/streamProvider';

export const STANDINGS_TABLE_BLOCK_KEY = 'game-page-blocks.standings-table';
export type StandingsTableBlockType = BlockBase;

export const GAME_SCHEDULE_BLOCK_KEY = 'game-page-blocks.game-schedule-block';
export interface GameScheduleBlockType extends BlockBase {
  competitions:
    | {
        competitionName: string;
        competitionId: string;
      }[]
    | null;
}

export const TOURNAMENT_VISUALIZER_BLOCK_KEY = 'game-page-blocks.tournament-visualizer';
export type TournamentVisualizerBlockType = BlockBase;

export const WHERE_TO_WATCH_BLOCK_KEY = 'game-page-blocks.where-to-watch-block';
export interface WhereToWatchBlockType extends BlockBase {
  providers: StreamProviderType[];
}

export type GamePageBlock =
  | StandingsTableBlockType
  | TournamentVisualizerBlockType
  | GameScheduleBlockType
  | WhereToWatchBlockType;
