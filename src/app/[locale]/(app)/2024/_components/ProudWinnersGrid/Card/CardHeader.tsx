import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';

import { CardProps } from './Card';

type Props = Pick<CardProps, 'gameImage' | 'gameLogoThumbnail' | 'gameStrapiThumbnail' | 'winnerLogo'>;

export const CardHeader = ({ gameImage, gameStrapiThumbnail, winnerLogo }: Props) => (
  <div className="relative rounded-tl-3xl rounded-tr-3xl">
    {gameStrapiThumbnail ? (
      <StrapiImage
        className="h-[150px] w-full rounded-tl-3xl rounded-tr-3xl object-cover grayscale-100"
        image={gameStrapiThumbnail}
        quality={25}
      />
    ) : (
      <Image
        alt="proud winners game card"
        className="h-[150px] w-full rounded-tl-3xl rounded-tr-3xl object-cover grayscale-100"
        quality={25}
        src={gameImage}
      />
    )}
    <div className="from-dark-default/0 to-dark-default absolute inset-0 rounded-tl-3xl rounded-tr-3xl bg-gradient-to-r rtl:bg-gradient-to-l" />
    {/* <div className="text-dark-default absolute start-5 top-5 size-[36px] rounded-lg bg-white p-1">
      {gameLogoThumbnail}
    </div> */}
    <div className="absolute inset-y-0 end-[18px] flex h-full w-[115px] items-center justify-center">
      <div className="flex size-[100px] items-center justify-center">
        <Image alt="" className="size-full object-contain" quality={25} src={winnerLogo} />
      </div>
    </div>
  </div>
);
