import clsx from 'clsx';
import Image from 'next/image';

import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { PreviousTournamentSection } from '@/strapi/api/single/previousTournament/types';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import WinnersImage from '@/ui/assets/images/winners2024-2.png';

import { Table } from './Table';

const borderStyles = 'rounded-lg md:rounded-2xl lg:rounded-4xl';

type Props = Partial<PreviousTournamentSection & Pick<SiteConfigData, 'translations'>>;

export const ClubChampionshipTable = ({ title, subtitle, translations }: Props) => {
  return (
    <>
      <BlockSectionWrapper
        className="mx-auto max-w-6xl px-4 pt-[50px] lg:py-[90px] xl:px-8"
        isSectionDividerVisible
        subtitle={subtitle ?? 'Get Ready for the Next Level of Competition!'}
        title={title ?? 'EWC 2024 Club Champions'}
      >
        <div className={clsx('relative flex max-md:h-[300px] md:p-8 md:max-lg:h-[500px] lg:justify-end', borderStyles)}>
          <Image
            alt="2024 winners with the EWC trophy"
            className={clsx('absolute inset-0 size-full object-cover object-bottom', borderStyles)}
            quality={25}
            src={WinnersImage}
          />
          <div className={clsx('absolute inset-0 bg-gradient-to-r from-black/0 to-black/33', borderStyles)} />
          <div className="relative max-lg:w-full max-md:top-[250px] md:max-lg:top-[400px]">
            <Table translations={translations} />
          </div>
        </div>
      </BlockSectionWrapper>
      <div className="max-lg:h-[800px]" />
    </>
  );
};
