import { RefObject, useEffect, useState } from 'react';

export function useScrollArrowVisibility(ref: RefObject<HTMLElement | null>) {
  const [showLeft, setShowLeft] = useState(false);
  const [showRight, setShowRight] = useState(false);

  useEffect(() => {
    const el = ref.current;
    if (!el) return;

    const update = () => {
      if (!el) return;
      const scrollLeft = el.scrollLeft;
      const maxScrollLeft = el.scrollWidth - el.clientWidth;
      setShowLeft(scrollLeft > 0);
      setShowRight(scrollLeft < maxScrollLeft - 1);
    };

    const onResizeOrScroll = () => requestAnimationFrame(update);

    update();
    el.addEventListener('scroll', onResizeOrScroll);
    window.addEventListener('resize', onResizeOrScroll);

    const observer = new MutationObserver(update);
    observer.observe(el, { childList: true, subtree: true });

    return () => {
      el.removeEventListener('scroll', onResizeOrScroll);
      window.removeEventListener('resize', onResizeOrScroll);
      observer.disconnect();
    };
  }, [ref]);

  return { showLeft, showRight };
}
