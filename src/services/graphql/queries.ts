import { gql } from '@apollo/client';

export const TOURNAMENTS_QUERY = gql`
  query Tournaments($tournamentIds: [String!]!) {
    tournaments(filter: [{ ids: $tournamentIds }], limit: 100, sort: [{ field: START_TIME, order: ORDER_ASC }]) {
      result {
        id
        name
        status
        startTime
        endTime
        prizePool {
          rank
          amount
          currency
        }
        streams {
          url
          language
          primary
        }
        contestants {
          id
          rank
          createdAt
          updatedAt
          team {
            id
            name
            club {
              id
              name
            }
            images {
              id
              type
            }
          }
          members {
            role {
              name
            }
            player {
              name
              nationality
            }
          }
        }
      }
    }
  }
`;

export const MATCH_SERIES_QUERY = gql`
  query MatchSeries($tournamentIds: [String!]!) {
    matchSeries(
      filters: [{ tournamentIds: $tournamentIds }]
      limit: 100
      sorts: [{ field: START_TIME, order: ORDER_ASC }]
    ) {
      items {
        id
        status
        startTime
        position
        tournament {
          id
          name
          variant
          type
        }
        streams {
          url
          language
          primary
        }
        metadata {
          id
          type
          position
          name
        }
        contestants {
          id
          score
          result
          rank
          points
          team {
            id
            name
            images {
              id
              type
            }
          }
          members {
            player {
              id
              name
              nationality
            }
            role {
              id
              name
            }
          }
        }
        links {
          direction
          result
          linkedMatchSeries {
            id
          }
        }
        matches {
          id
          sequence
          status
          contestants {
            id
            points
            rank
            result
            score
            team {
              id
            }
          }
          gameMap {
            id
            name
          }
        }
      }
    }
  }
`;

export const GAME_DATA_QUERY = gql`
  query GameData($matchId: String!) {
    gameData(matchId: $matchId, sort: [{ field: "sequence", order: ORDER_DESC }], limit: 1) {
      data {
        state {
          contestants {
            score
            players {
              id
              stats {
                kills: kills(victimTypes: [PLAYER], classifications: [REGULAR])
                headshots: kills(victimTypes: [PLAYER], classifications: [REGULAR], attributes: [HEADSHOT])
                assists(types: [DAMAGE])
                deaths
                damage
              }
              meta {
                character {
                  name
                }
              }
              attributes {
                gold: value(type: GOLD) {
                  current
                }
                xp: value(type: EXPERIENCE) {
                  current
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const MATCH_SERIES_SUBSCRIPTION_QUERY = gql`
  subscription FixtureDataEvents($tournamentIds: [ID]) {
    fixtureDataEvents(filters: { actions: [UPDATED], tournamentIds: $tournamentIds }) {
      data {
        ... on MatchSeries {
          id
          matchSeriesStatus: status
          startTime
        }
        ... on Match {
          id
          matchStatus: status
        }
        ... on MatchSeriesContestant {
          id
          score
          points
          rank
          matchSeriesContestantResult: result
        }
        ... on MatchContestant {
          id
          score
          points
          rank
          matchContestantResult: result
        }
      }
    }
  }
`;

export const TOURNAMENTS_SUBSCRIPTION_QUERY = gql`
  subscription FixtureDataEvents($tournamentIds: [ID]) {
    fixtureDataEvents(filters: { actions: [UPDATED], tournamentIds: $tournamentIds }) {
      data {
        ... on TournamentContestant {
          id
          rank
        }
      }
    }
  }
`;

export const GAME_DATA_SUBSCRIPTION_QUERY = gql`
  subscription GameDataUpdated($matchId: String!) {
    gameDataUpdated(matchId: $matchId) {
      state {
        contestants {
          score
          players {
            id
            stats {
              kills: kills(victimTypes: [PLAYER], classifications: [REGULAR])
              headshots: kills(victimTypes: [PLAYER], classifications: [REGULAR], attributes: [HEADSHOT])
              assists(types: [DAMAGE])
              deaths
              damage
            }
            meta {
              character {
                name
              }
            }
            attributes {
              gold: value(type: GOLD) {
                current
              }
              xp: value(type: EXPERIENCE) {
                current
              }
            }
          }
        }
      }
    }
  }
`;
