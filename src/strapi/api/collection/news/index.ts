import { Locale } from '@/hooks/i18n/const';

import { NEWS_ARTICLES_COLLECTION_KEY, NewsArticleType } from '../../../types/collection/news';
import { getDynamicZoneBlocks } from '../../blocks';
import { getStrapiDefaultParams, strapiClient } from '../../client';
import { assembleBlocksWithAdditionalData } from '../../utils/block';

export async function fetchNewsArticleSlugs(locale: Locale) {
  try {
    const newsArticles = await strapiClient
      .collection(NEWS_ARTICLES_COLLECTION_KEY)
      .find({ fields: ['slug'], ...getStrapiDefaultParams(locale) });

    const data = newsArticles.data as NewsArticleType[];
    if (!data) {
      return [];
    }

    const slugs = data.map((d) => d.slug);
    return slugs;
  } catch (error) {
    console.error('News Articles slugs not fetched!', error);
    throw new Error('News Articles slugs not fetched!');
  }
}

export async function fetchSingleNewsArticle(locale: Locale, slug: string) {
  try {
    const newsArticles = await strapiClient.collection(NEWS_ARTICLES_COLLECTION_KEY).find({
      filters: { slug: { $eq: slug } },
      populate: { tags: { populate: '*' }, cover: { populate: '*' }, blocks: getDynamicZoneBlocks() },
      ...getStrapiDefaultParams(locale),
    });

    const article = newsArticles.data[0] as NewsArticleType;
    if (!article) {
      return null;
    }

    await assembleBlocksWithAdditionalData(article.blocks, locale);
    return article;
  } catch (error) {
    console.error('News single articles data not fetched!', error);
    throw new Error('News single articles data not fetched!');
  }
}

export async function fetchNewsArticleSeo(locale: Locale, slug: string) {
  try {
    const newsArticles = await strapiClient.collection(NEWS_ARTICLES_COLLECTION_KEY).find({
      filters: { slug: { $eq: slug } },
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    const article = newsArticles.data[0] as NewsArticleType;
    if (!article) {
      return null;
    }

    return article.seo;
  } catch (error) {
    console.error('News Articles SEO not fetched!', error);
    return null;
  }
}

export { fetchNewsArticles } from './fetchNewsArticles';
