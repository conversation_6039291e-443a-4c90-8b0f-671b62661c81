import { StrapiImage } from '@/components/StrapiImage';
import { VideoEmbed } from '@/components/VideoEmbed';
import { MediaBlockType } from '@/strapi/types/block';
import { isVideoMedia } from '@/utils/media';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { Video } from './Video';

export const MediaBlock = ({ media, mediaUrl, autoplay, muted, loop, section, controls }: MediaBlockType) => {
  if (!media && !mediaUrl) {
    return null;
  }

  if (mediaUrl) {
    return (
      <Container section={section}>
        <div className="aspect-video">
          <VideoEmbed url={mediaUrl} {...{ controls, loop, autoplay, muted }} />
        </div>
      </Container>
    );
  }

  if (media) {
    return (
      <Container section={section}>
        {isVideoMedia(media.url) ? (
          <Video autoplay={autoplay} controls={!!controls} loop={loop} muted={muted} video={media} />
        ) : (
          <StrapiImage className="aspect-auto w-full object-cover" image={media} />
        )}
      </Container>
    );
  }
};

const Container = ({ section, children }: React.PropsWithChildren<Pick<MediaBlockType, 'section'>>) => {
  return (
    <BlockSectionWrapper {...section} className="mx-auto max-w-7xl px-4 lg:px-8">
      <div className="relative w-full overflow-clip rounded-lg md:rounded-2xl lg:rounded-4xl xl:rounded-[64px]">
        {children}
      </div>
    </BlockSectionWrapper>
  );
};
