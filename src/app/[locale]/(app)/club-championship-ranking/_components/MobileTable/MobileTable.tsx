import { AnimatePresence, motion } from 'framer-motion';

import { JsonFieldType } from '../../../../../../strapi/types/helper';
import { ClubData } from '../../_utils/types';
import { MobileTableRow } from './MobileTableRow';

export const MobileTable = ({
  rankings,
  translations,
}: {
  rankings: ClubData[];
  translations?: JsonFieldType | null;
}) => {
  return (
    <div>
      <div className="bg-dark-default font-primary flex gap-1.5 py-2 text-[10px] font-bold text-white uppercase">
        <span className="w-[44px] text-center">{translations?.rank ?? 'rank'}</span>
        <span className="grow">{translations?.club ?? 'club'}</span>
        <span className="w-[100px] pe-4 text-right">{translations?.ccPoints ?? 'CC POINTS'}</span>
      </div>
      <AnimatePresence>
        <div className="divide-gray-easy divide-y">
          {rankings.map((club) => (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              initial={{ opacity: 0, y: 20 }}
              key={club.club.id}
              layout
              transition={{
                type: 'spring',
                stiffness: 500,
                damping: 50,
                mass: 1,
              }}
            >
              <MobileTableRow club={club} translations={translations} />
            </motion.div>
          ))}
        </div>
      </AnimatePresence>
    </div>
  );
};
