import { differenceInSeconds, intervalToDuration } from 'date-fns';
import { useEffect, useMemo, useState } from 'react';

const SECOND = 1000;

export type TimeLeft = {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
} | null;

const calculateTimeLeft = (targetDate: Date, now: Date): TimeLeft => {
  const diffInSeconds = differenceInSeconds(targetDate, now);

  if (diffInSeconds <= 0) {
    return null;
  }

  const duration = intervalToDuration({ start: now, end: targetDate });
  return {
    days: duration.days ?? 0,
    hours: duration.hours ?? 0,
    minutes: duration.minutes ?? 0,
    seconds: duration.seconds ?? 0,
  };
};

export function useCountdown(
  startTime: string | null,
  endTime: string | null,
  frequencyInSeconds: number = 1, // Default to 1 second updates
): {
  timeLeft: TimeLeft;
  isLive: boolean;
  isUpcoming: boolean;
  isCompleted: boolean;
} {
  const [now, setNow] = useState(() => new Date());

  const startDate = useMemo(() => (startTime ? new Date(startTime) : undefined), [startTime]);
  const endDate = useMemo(() => (endTime ? new Date(endTime) : undefined), [endTime]);

  const [timeLeft, setTimeLeft] = useState(() => (startDate ? calculateTimeLeft(startDate, now) : null));

  useEffect(() => {
    if (!startDate) return;

    const updateTime = () => {
      const currentNow = new Date();
      setNow(currentNow);
      if (startDate) {
        setTimeLeft(calculateTimeLeft(startDate, currentNow));
      }
    };

    const interval = setInterval(updateTime, frequencyInSeconds * SECOND);

    return () => clearInterval(interval);
  }, [startDate, frequencyInSeconds]);

  const hasStarted = startDate ? startDate <= now : false;
  const hasEnded = endDate ? endDate <= now : false;

  //todo separate booleans from timeLeft
  // sometimes just the booleans are used, they can be memoed and will not cause re-renders
  return {
    timeLeft,
    isLive: !timeLeft && hasStarted && !hasEnded,
    isUpcoming: timeLeft !== null,
    isCompleted: hasEnded,
  };
}
