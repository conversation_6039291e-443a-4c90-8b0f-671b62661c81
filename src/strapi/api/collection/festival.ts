import { Locale } from '@/hooks/i18n/const';
import { FestivalType, VenueData } from '@/strapi/types/collection/festival';
import { GameType } from '@/strapi/types/collection/game';
import { SeoType } from '@/strapi/types/shared/seo';

import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils/block';

export async function getFestivalSinglePageData(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('festivals').find({
      filters: { slug: { $eq: slug } },
      populate: {
        blocks: getDynamicZoneBlocks({ includeGamePageBlocks: true }),
        posterImage: { fields: ['url', 'alternativeText', 'width', 'height'] },
        icon: { fields: ['url', 'alternativeText', 'width', 'height'] },
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as GameType;

    if (!data) {
      return null;
    }

    await assembleBlocksWithAdditionalData(data.blocks, locale, data);
    return data;
  } catch (error) {
    console.error('Festival data not fetched', error);
    throw new Error('Festival data not fetched');
  }
}

export async function getFestivalSinglePageSeo(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('festivals').find({
      filters: { slug: { $eq: slug } },
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as FestivalType;
    if (!data) {
      return null;
    }
    return data.seo as SeoType;
  } catch (error) {
    console.error('Game Page SEO not fetched!', error);
    return null;
  }
}

export async function getVenueListData(locale: Locale) {
  try {
    const venues = await strapiClient.collection('venues').find({
      populate: '*',
      ...getStrapiDefaultParams(locale),
    });
    return venues.data as VenueData[];
  } catch (error) {
    console.error('Venue list not fetched', error);
    throw new Error('Venue list not fetched');
  }
}
