import React, { useId } from 'react';

type Props = React.SVGProps<SVGSVGElement>;

/**
 * React component rendering a gold medal SVG icon with unique gradient and clipPath IDs.
 *
 * @remarks
 * This component should be used instead of embedding a regular SVG file directly.
 * The SVG uses internal references (IDs) for gradients and clip paths. When the same SVG is rendered
 * multiple times on a page, static IDs can cause conflicts—subsequent instances may reference the
 * definitions of the first rendered SVG. If the first instance is hidden or removed, the others may break
 * due to missing references.
 *
 * By generating unique IDs for each instance using React's `useId`, this component ensures that
 * gradients and clip paths are correctly scoped and do not interfere with each other, maintaining
 * visual integrity regardless of how many times the icon is rendered or manipulated in the DOM.
 *
 * @param props - Standard SVG and React component props.
 * @returns A gold medal SVG icon with isolated definitions for safe multiple usage.
 */
const GoldMedal: React.FC<Props> = (props) => {
  const id = useId();
  const clipPathId = `gold-medal-clip-${id}`;
  const gradientId = `gold-medal-gradient-${id}`;

  return (
    <svg fill="none" height="100%" viewBox="0 0 33 32" width="100%" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_5007_27606)">
        <path
          clipRule="evenodd"
          d="M16.9258 2.37036L12.7777 10.0741L16.9258 19.5555H26.4073L16.9258 2.37036ZM27.5925 21.926H16.3332L12.7777 29.6297H32.3332L27.5925 21.926ZM0.333344 29.6296L11 12.4444L14.5556 20.7407L9.81482 29.6296H0.333344Z"
          fill={`url(#${gradientId})`}
          fillRule="evenodd"
        />
      </g>
      <defs>
        <clipPath id={clipPathId} transform="translate(31.6667 29.6296)">
          <path
            clipRule="evenodd"
            d="M16.9258 2.37036L12.7777 10.0741L16.9258 19.5555H26.4073L16.9258 2.37036ZM27.5925 21.926H16.3332L12.7777 29.6297H32.3332L27.5925 21.926ZM0.333344 29.6296L11 12.4444L14.5556 20.7407L9.81482 29.6296H0.333344Z"
            fillRule="evenodd"
          />
        </clipPath>
        <radialGradient
          cx="0"
          cy="0"
          gradientTransform="translate(16.402 2.37036) rotate(90) scale(48.7942 39.2889)"
          gradientUnits="userSpaceOnUse"
          id={gradientId}
          r="1"
        >
          <stop offset="0.085" stopColor="#F2C575" />
          <stop offset="0.375" stopColor="#987C4B" />
          <stop offset="0.805" stopColor="#4E442D" />
        </radialGradient>
        <clipPath id="clip0_5007_27606">
          <rect fill="white" height="32" transform="translate(0.333344)" width="32" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default GoldMedal;
