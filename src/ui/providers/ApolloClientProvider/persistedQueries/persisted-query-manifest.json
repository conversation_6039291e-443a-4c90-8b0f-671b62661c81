{"format": "apollo-persisted-query-manifest", "version": 1, "operations": [{"id": "9f7c95e6f78f5199d75209478c03754d1ca8326dc2be730d7c58fcb94649cba5", "name": "GameData", "type": "query", "body": "query GameData($matchId: String!) {\n  gameData(\n    matchId: $matchId\n    sort: [{field: \"sequence\", order: ORDER_DESC}]\n    limit: 1\n  ) {\n    data {\n      state {\n        contestants {\n          score\n          players {\n            id\n            stats {\n              kills: kills(victimTypes: [PLAYER], classifications: [REGULAR])\n              headshots: kills(\n                victimTypes: [PLAYER]\n                classifications: [REGULAR]\n                attributes: [HEADSHOT]\n              )\n              assists(types: [DAMAGE])\n              deaths\n              damage\n            }\n            meta {\n              character {\n                name\n              }\n            }\n            attributes {\n              gold: value(type: GOLD) {\n                current\n              }\n              xp: value(type: EXPERIENCE) {\n                current\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}"}, {"id": "48a028dd531ba27242b908d22c175384e78014e53615d6c933200b43a1fc29c2", "name": "MatchSeries", "type": "query", "body": "query MatchSeries($tournamentIds: [String!]!) {\n  matchSeries(\n    filters: [{tournamentIds: $tournamentIds}]\n    limit: 100\n    sorts: [{field: START_TIME, order: ORDER_ASC}]\n  ) {\n    items {\n      id\n      status\n      startTime\n      position\n      tournament {\n        id\n        name\n        variant\n        type\n      }\n      streams {\n        url\n        language\n        primary\n      }\n      metadata {\n        id\n        type\n        position\n        name\n      }\n      contestants {\n        id\n        score\n        result\n        rank\n        points\n        team {\n          id\n          name\n          images {\n            id\n            type\n          }\n        }\n        members {\n          player {\n             id\n            name\n            nationality\n          }\n          role {\n            id\n            name\n          }\n        }\n      }\n      links {\n        direction\n        result\n        linkedMatchSeries {\n          id\n        }\n      }\n      matches {\n        id\n        sequence\n        status\n        contestants {\n          id\n          points\n          rank\n          result\n          score\n          team {\n            id\n          }\n        }\n        gameMap {\n          id\n          name\n        }\n      }\n    }\n  }\n}"}, {"id": "67a72428b2be2a6e4c4b8723eb7d1f2deca9f544be07390ed2fe844a48d0886d", "name": "Tournaments", "type": "query", "body": "query Tournaments($tournamentIds: [String!]!) {\n  tournaments(\n    filter: [{ids: $tournamentIds}]\n    limit: 100\n    sort: [{field: START_TIME, order: ORDER_ASC}]\n  ) {\n    result {\n      id\n      name\n      status\n      startTime\n      endTime\n      prizePool {\n        rank\n        amount\n        currency\n      }\n      streams {\n        url\n        language\n        primary\n      }\n      contestants {\n        id\n        rank\n        createdAt\n        updatedAt\n        team {\n          id\n          name\n          club {\n            id\n            name\n          }\n          images {\n            id\n            type\n          }\n        }\n        members {\n          role {\n            name\n          }\n          player {\n            name\n            nationality\n          }\n        }\n      }\n    }\n  }\n}"}]}