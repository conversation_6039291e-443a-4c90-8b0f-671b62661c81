'use client';

import clsx from 'clsx';
import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';
import { useContentDirection } from '@/hooks/i18n';
import { TeamGridItemType } from '@/strapi/types/collection/participatingTeams';
import ChampionCrown from '@/ui/assets/images/champion-crown.png';
import TBDImage from '@/ui/assets/images/participating-team-tbd.png';

interface Props {
  item: TeamGridItemType;
  onClick?: () => void;
}

export const GridItem = ({ item, onClick }: Props) => {
  const { isRTL } = useContentDirection();
  return (
    <div
      className={clsx(
        'relative flex min-h-[210px] w-full min-w-[170px] flex-col gap-3 rounded-4xl px-4 pt-4 pb-6 text-center shadow-sm',
        item.isTBD ? 'bg-gray-easy justify-end overflow-hidden' : 'justify-between bg-white',
        { 'cursor-pointer': item.club && onClick },
      )}
      onClick={onClick}
    >
      {item.isCurrentChampion && (
        <Image
          alt=""
          className={clsx(
            'absolute -top-[38px] z-10 h-auto w-[65px] object-contain',
            isRTL ? '-right-[30px] scale-x-[-1]' : '-left-[30px]',
          )}
          quality={100}
          src={ChampionCrown}
        />
      )}
      <div className="relative z-10 flex flex-col items-center gap-1">
        {!item.isPlayer && item.teamLogo && (
          <div className="h-25 w-25 overflow-hidden rounded-xl">
            <StrapiImage className="h-full w-full object-contain" image={item.teamLogo} />
          </div>
        )}
        {item.isPlayer && item.playerPhoto && (
          <div className="relative h-25 w-25 overflow-hidden rounded-xl">
            <StrapiImage className="h-full w-full object-contain" image={item.playerPhoto} />
            {item.teamLogo && (
              <div className="absolute right-1 bottom-1 h-6 w-6">
                <StrapiImage className="h-full w-full object-contain" image={item.teamLogo} />
              </div>
            )}
          </div>
        )}
        {item.teamName && (
          <span className="font-riforma text-dark-default text-sm leading-[110%] font-bold">{item.teamName}</span>
        )}
        {item.playerName && (
          <span className="font-riforma text-dark-default text-sm leading-[110%] font-bold">{item.playerName}</span>
        )}
      </div>
      <div className="z-10 flex flex-col items-center gap-1">
        {item.subtitleText && <span className="text-[10px] leading-[120%]">{item.subtitleText}</span>}
        {item.qualifierName && (
          <div
            className={clsx(
              'flex items-center gap-2 rounded-sm px-2 py-1',
              item.isCurrentChampion ? 'bg-gold-primary' : 'bg-dark-default',
            )}
          >
            {item.qualifierLogo && (
              <div className="h-4 min-h-4 w-4 min-w-4">
                <StrapiImage className="h-full w-full object-contain" image={item.qualifierLogo} />
              </div>
            )}
            {item.qualifierName && (
              <span className="font-riforma text-[10px] font-bold text-white">{item.qualifierName}</span>
            )}
          </div>
        )}
      </div>
      {item.isTBD && (
        <Image alt="" className="absolute inset-0 h-full w-full object-cover" quality={100} src={TBDImage} />
      )}
    </div>
  );
};
