import { Locale } from '@/hooks/i18n/const';
import { ButtonType } from '@/strapi/types/shared';

import { StrapiBase } from '../base';
import { DynamicZoneBlock } from '../block';
import { RichTextContentType } from '../helper';
import { MediaType } from '../media';
import { SeoType } from '../shared/seo';

export const FESTIVALS_COLLECTION_KEY = 'festivals';
export const VENUES_COLLECTION_KEY = 'venues';

export enum FestivalTypeEnum {
  FESTIVAL = 'festival',
  ESPORT = 'esport',
}

export interface FestivalType extends StrapiBase {
  title: string | null;
  description: RichTextContentType | null;
  startDate: string | null;
  endDate: string | null;
  startDateTime: string | null;
  endDateTime: string | null;
  type: FestivalTypeEnum;
  slug: string | null;
  shortSummary: string | null;
  posterImage: MediaType | null;
  location: string | null;
  isHighlight: boolean;
  isShownOnSchedule: boolean;
  icon: MediaType | null;
  ticketsUrl: string | null;
  venue: string | null;
  subtitle: string | null;
  excludedDays: string[] | null;
  venues: VenueType[] | null;
  buttonText: string | null;
  seo: SeoType | null;
  blocks: DynamicZoneBlock[];
}

export interface VenueType extends StrapiBase {
  name: string | null;
  type: FestivalTypeEnum | null;
  venueId: string | null;
  latitude: string | null;
  longitude: string | null;
  description: string | null;
  content: RichTextContentType | null;
  ticketsUrl: string | null;
  festivals: FestivalType[] | null;
}

export interface VenueData extends StrapiBase {
  name: string;
  type: string;
  venueId: string;
  latitude: string;
  longitude: string;
  description: string;
  content: RichTextContentType;
  ticketsUrl: string | null;
  localizations: Locale[];
  mapCtaButton: ButtonType | null;
}
