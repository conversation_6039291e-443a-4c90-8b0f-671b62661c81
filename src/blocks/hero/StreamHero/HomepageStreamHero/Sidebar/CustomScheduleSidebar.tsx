import { ClubData } from '@/app/[locale]/(app)/club-championship-ranking/_utils/types';
import { CustomStreamSchedule } from '@/strapi/types/collection/stream';
import { JsonFieldType } from '@/strapi/types/helper';

import { CCRankingsSection } from '../../shared/Sidebar/CCRankingsSection';
import { CustomScheduleCard } from './CustomScheduleCard';

interface CustomScheduleSidebarProps {
  customSchedule: CustomStreamSchedule[] | null;
  todaysCCRankings: ClubData[] | null | undefined;
  translations?: JsonFieldType;
  customStreamTitle: string | null;
  isLoading: boolean;
  error?: string;
}

export const CustomScheduleSidebar = ({
  customSchedule,
  todaysCCRankings,
  translations,
  customStreamTitle,
  isLoading,
  error,
}: CustomScheduleSidebarProps) => {
  const isLoadingOrError = isLoading || !!error;
  return (
    <div className="flex flex-1/4 flex-col gap-6 bg-white pt-4 pb-2 lg:max-w-[450px] lg:pt-8 lg:pb-5">
      <CCRankingsSection todaysCCRankings={todaysCCRankings} translations={translations} />
      {isLoadingOrError && <div>{isLoading ? 'loading...' : error}</div>}
      {!isLoadingOrError && customSchedule && customSchedule.length > 0 && (
        <div className="flex gap-4 px-4 max-lg:overflow-x-auto lg:flex-col lg:overflow-y-auto lg:px-6">
          {customSchedule.map((schedule, index) => (
            <CustomScheduleCard customStreamTitle={customStreamTitle} key={index} schedule={schedule} />
          ))}
        </div>
      )}
    </div>
  );
};
