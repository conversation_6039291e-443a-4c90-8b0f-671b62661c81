import { MatchSeriesMetadata, MatchSeriesMetadataType } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';

export function getLocalizedMatchDescriptor(
  metadata: MatchSeriesMetadata[],
  apiTranslations: JsonFieldType = {},
): string {
  const groupNames = metadata
    .filter((m) => m.type === MatchSeriesMetadataType.GROUP)
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0))
    .map((m) => apiTranslations[m.id] || m.name);

  const bracketItem = metadata.find((m) => m.type === MatchSeriesMetadataType.BRACKET);
  const bracketLabel = bracketItem?.name?.toLowerCase();
  const bracket =
    bracketItem && !['bracket', 'regular'].includes(bracketLabel || '')
      ? apiTranslations[bracketItem.id] || bracketItem.name
      : null;

  const roundItem = metadata.find((m) => m.type === MatchSeriesMetadataType.ROUND);
  const round = roundItem ? apiTranslations[roundItem.id] || roundItem.name : null;

  const parts = [groupNames.join(' ').trim(), bracket, round].filter(Boolean);
  return parts.length ? parts.join(' – ') : 'Match';
}
