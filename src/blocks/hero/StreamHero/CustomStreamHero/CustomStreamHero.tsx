'use client';

import { useMemo } from 'react';

import { VideoEmbed } from '@/components/VideoEmbed';
import { useTodaysCcRankings } from '@/hooks/tournaments/useTodaysCcRankings';
import { JsonFieldType } from '@/strapi/types/helper';
import { CustomStreamHeroBlockType } from '@/strapi/types/hero';
import SpinnerIcon from '@/ui/assets/icons/spinner.svg';

import { useCountdown } from '../shared/hooks/useCountdown';
import { Intermission } from '../shared/Intermission';
import { Sidebar } from './Sidebar';
import { StreamDetails } from './StreamDetails';
import { getActiveOrUpcomingDailyStream } from './utils';

type Props = CustomStreamHeroBlockType & {
  translations: JsonFieldType;
};

export const CustomStreamHero = ({ weekStream, translations, games }: Props) => {
  const { todaysCCRankings } = useTodaysCcRankings(games);
  const stream = useMemo(() => getActiveOrUpcomingDailyStream(weekStream?.streams), [weekStream]);

  const { isLive, isUpcoming } = useCountdown(stream?.startTime ?? null, stream?.endTime ?? null);
  if (!stream) return <div>no stream found</div>;
  return (
    <section className="flex max-h-screen w-full max-lg:flex-col">
      <div className="flex min-h-0 min-w-0 flex-3/4 flex-col">
        <div className="bg-dark-default relative flex aspect-video h-fit max-h-full min-h-0 w-full items-center justify-center">
          {isUpcoming ? (
            <Intermission apiTranslations={{}} matches={[]} stream={stream} translations={translations} />
          ) : (
            <VideoEmbed
              autoplay
              controls
              fallback={<SpinnerIcon className="size-12 text-white" />}
              url={stream?.streamUrl}
            />
          )}
        </div>
        <StreamDetails
          button={{ text: stream?.buttonText ?? null, url: stream?.buttonUrl ?? null }}
          customSchedule={stream?.customSchedule ?? null}
          title={isLive ? stream?.internalName : stream?.intermissionText}
          translations={translations ?? {}}
        />
      </div>
      <Sidebar
        customSchedule={stream?.customSchedule ?? null}
        customStreamTitle={stream?.internalName ?? null}
        todaysCCRankings={todaysCCRankings}
        translations={translations ?? {}}
      />
    </section>
  );
};
