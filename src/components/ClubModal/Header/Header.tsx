import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';
import { JsonFieldType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import PlaceholderLogo from '@/ui/assets/images/ewc-placeholder.png';
import { Button } from '@/ui/components/Button';

import { CcRankings } from '../types';
import { CloseButton } from './CloseButton';

interface Props {
  name: string | null;
  description: string | null;
  websiteUrl: string | null;
  logo: MediaType | null;
  rankings: CcRankings;
  translations: JsonFieldType;
  onClose: () => void;
}

export const Header = ({ name, description, websiteUrl, logo, rankings, translations, onClose }: Props) => {
  return (
    <div className="relative flex flex-col">
      <CloseButton text={translations.close ?? 'close'} onClose={onClose} />
      <div className="flex flex-col gap-2 md:flex-row md:gap-8">
        {logo ? (
          <StrapiImage alt={`Club ${name} logo`} className="size-[128px] object-contain lg:size-40" image={logo} />
        ) : (
          <Image alt={`Club ${name} logo`} className="size-[128px] object-contain lg:size-40" src={PlaceholderLogo} />
        )}
        <div className="flex grow flex-col gap-4">
          {(name || description) && (
            <div className="flex flex-col gap-1">
              {name && <h3 className="text-h3">{name}</h3>}
              {description && <p className="text-paragraph">{description}</p>}
            </div>
          )}
          <div className="flex flex-col gap-4 lg:flex-row lg:items-end lg:justify-between">
            <div className="flex gap-2">
              <HighlightBox
                description={translations.ccRank ?? 'cc rank'}
                text={rankings.rank ? `#${rankings.rank}` : '-'}
              />
              <HighlightBox
                description={translations.medalsWon ?? 'medals won'}
                text={rankings.medals?.toString() ?? '-'}
              />
              <HighlightBox
                description={translations.ccPoints ?? 'cc points'}
                text={rankings.points?.toLocaleString() ?? '-'}
              />
            </div>
            <div className="flex flex-col gap-2 md:flex-row md:gap-4">
              {/* <div className="max-lg:flex-1 md:max-lg:max-w-1/2">
                <Button
                  brazeEventProperties={{ button_name: `CC Standings link`, location: `Club Modal (${name})` }}
                  isFullWidth
                  link="club-championship-rankings"
                  text={translations.ccStandings ?? 'cc standings'}
                />
              </div> */}
              {websiteUrl && (
                <div className="max-lg:flex-1 md:max-lg:max-w-1/2">
                  <Button
                    brazeEventProperties={{ button_name: `View Website link`, location: `Club Modal (${name})` }}
                    isFullWidth
                    link={websiteUrl}
                    openInNewTab
                    text={translations.viewWebsite ?? 'View Website'}
                    variant="secondary"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface HightLightBoxProps {
  text: string;
  description: string;
}

const HighlightBox = ({ text, description }: HightLightBoxProps) => {
  return (
    <div className="bg-white-dirty text-dark-default flex shrink-0 flex-col items-center gap-1 rounded-lg px-3 py-2.5 max-md:flex-1">
      <h5 className="text-h5">{text}</h5>
      <span className="font-primary text-[8px] leading-none font-bold uppercase">{description}</span>
    </div>
  );
};
