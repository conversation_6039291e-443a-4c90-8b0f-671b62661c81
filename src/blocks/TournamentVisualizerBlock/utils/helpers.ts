import {
  Match<PERSON><PERSON>,
  MatchSeriesContestant,
  MatchSeriesMetadata,
  MatchSeriesMetadataType,
  TournamentType,
  TournamentVariant,
} from '@/services/graphql/types/matchSeries';
import { constructImageUrl } from '@/services/graphql/utils';

import { DEFAULT_META_NAME } from './const';
import { Contestant } from './types';

export function extractMetadata(match: MatchSeries) {
  return {
    groupMeta: getMetaOfType(match.metadata, MatchSeriesMetadataType.GROUP),
    bracketMeta: getMetaOfType(match.metadata, MatchSeriesMetadataType.BRACKET),
    roundMeta: getMetaOfType(match.metadata, MatchSeriesMetadataType.ROUND),
  };
}

function getMetaOfType(metadata: MatchSeriesMetadata[], type: MatchSeriesMetadataType) {
  return metadata.find((md) => md.type === type) ?? { name: DEFAULT_META_NAME, position: 1, id: '1', type };
}

export function createContestants(contestants: MatchSeriesContestant[]): Contestant[] {
  return contestants.map((c) => ({
    id: c.team.id,
    name: c.team.name ?? 'TBD',
    score: c.score,
    result: c.result,
    logoUrl: constructImageUrl(c.team.images, 'logo_transparent_whitebg'),
  }));
}

export function isFfaTournament(variant: TournamentVariant) {
  return variant.includes('ffa_');
}

export function isBracketsTournament(type: TournamentType) {
  return [
    TournamentType.SINGLE_ELIMINATION,
    TournamentType.DOUBLE_ELIMINATION,
    TournamentType.DOUBLE_ELIMINATION_WITH_GROUPS,
    TournamentType.DUAL_TOURNAMENT,
  ].includes(type);
}

export function isRoundRobinTournament(type: TournamentType) {
  return [TournamentType.ROUND_ROBIN, TournamentType.ROUND_ROBIN_IN_GROUPS].includes(type);
}
