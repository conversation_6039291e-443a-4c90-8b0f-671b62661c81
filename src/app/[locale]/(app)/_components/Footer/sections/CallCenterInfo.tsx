import { MdCall, MdSchedule } from 'react-icons/md';

import { FooterData } from '@/strapi/api/single/footer';

type Props = FooterData['callCenterInfo'];

export const CallCenterInfo = ({ label, telephone, workingHours }: Props) => (
  <div className="flex flex-col items-center gap-1.5 text-[13px] leading-[1] font-extrabold uppercase md:items-start">
    {label && <p className="text-gold-primary">{label}</p>}
    {telephone && (
      <div className="flex items-center gap-2 text-white">
        <MdCall className="size-4" />
        <p className="mt-px">{telephone}</p>
      </div>
    )}
    {workingHours && (
      <div className="flex items-center gap-2 text-white">
        <MdSchedule className="size-4" />
        <p className="mt-px">{workingHours}</p>
      </div>
    )}
  </div>
);
