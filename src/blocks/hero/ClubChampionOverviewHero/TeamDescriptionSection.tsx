import clsx from 'clsx';

import { SimpleRichTextContent } from '@/components/SimpleRichTextContent';
import { RichTextContentType } from '@/strapi/types/helper';

interface Props {
  topLabel: string | null;
  clubName: string | null;
  clubDescription: RichTextContentType | null;
  gamePlacements: { game: string; placement: number }[];
}

export const TeamDescriptionSection = ({ topLabel, clubName, clubDescription, gamePlacements }: Props) => {
  return (
    <div className="flex grow flex-col gap-5 p-4 md:p-8 lg:p-10">
      {(topLabel || clubName) && (
        <div className="flex flex-col gap-2 max-md:items-center">
          {topLabel && <p className="text-button-big">{topLabel}</p>}
          {clubName && <p className="text-h2 text-radial-gold capitalize">{clubName}</p>}
        </div>
      )}
      <div className="flex flex-wrap gap-1 max-md:justify-center">
        {gamePlacements.map((gp) => (
          <Badge
            key={gp.game}
            text={`#${gp.placement} ${gp.game}`}
            variant={gp.placement === 1 ? 'dark' : gp.placement === 2 ? 'medium' : 'light'}
          />
        ))}
      </div>
      {clubDescription && (
        <SimpleRichTextContent
          content={clubDescription}
          paragraphClassname="text-dark-default text-paragraph max-md:text-center"
        />
      )}
    </div>
  );
};

interface BadgeProps {
  text: string;
  variant: 'light' | 'medium' | 'dark';
}

const Badge = ({ text, variant }: BadgeProps) => (
  <p
    className={clsx(
      'font-primary rounded-sm px-[5px] py-[3px] text-[10px] leading-[1.1] font-bold whitespace-nowrap text-white uppercase',
      {
        'bg-dark-default': variant === 'dark',
        'bg-[#616161]': variant === 'medium',
        'bg-[#949494]': variant === 'light',
      },
    )}
  >
    {text}
  </p>
);
