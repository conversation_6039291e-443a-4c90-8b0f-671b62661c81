'use client';

import clsx from 'clsx';

import { GameType } from '@/strapi/types/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';

import { GameWinner } from '../fetchCompletedGamesData';
import { ActiveCardContent } from './ActiveCardContent';
import { ButtonsSection } from './ButtonsSection';
import { CompletedCardContent } from './CompletedCardContent';

interface Props {
  game: GameType;
  isCompleted: boolean;
  isCompletedBadgeVisible?: boolean;
  winners: Record<number, GameWinner[]>;
  translations: JsonFieldType;
}

export function CompetitionCard({ game, isCompleted, isCompletedBadgeVisible, winners, translations }: Props) {
  return (
    <CardContainer isCompleted={isCompleted}>
      <div
        className={clsx(
          'flex h-full flex-col items-center justify-between gap-3',
          isCompleted ? 'md:gap-[17px]' : 'md:gap-6',
        )}
      >
        {isCompleted ? (
          <CompletedCardContent
            game={game}
            isCompletedBadgeVisible={isCompletedBadgeVisible}
            translations={translations}
            winners={winners}
          />
        ) : (
          <ActiveCardContent game={game} translations={translations} />
        )}
        <ButtonsSection
          gamePageSlug={game.slug}
          gameTitle={game.title}
          isCompleted={isCompleted}
          isLinkingToGamePageEnabled={game.isLinkingToGamePageEnabled}
          ticketsUrl={game.ticketsUrl}
          translations={translations}
        />
      </div>
    </CardContainer>
  );
}

const CardContainer = ({ isCompleted, children }: React.PropsWithChildren<{ isCompleted: boolean }>) => {
  return (
    <div
      className={clsx(
        'w-full overflow-hidden rounded-sm shadow-md md:rounded-2xl lg:rounded-3xl',
        isCompleted ? 'bg-dark-default' : 'bg-white',
      )}
    >
      {children}
    </div>
  );
};
