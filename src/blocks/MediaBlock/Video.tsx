'use client';

import clsx from 'clsx';
import { useEffect, useRef } from 'react';
import { FaPlay } from 'react-icons/fa';
import { useToggle } from 'usehooks-ts';

import { StrapiVideo } from '@/components/StrapiVideo';
import { MediaType } from '@/strapi/types/media';

interface Props {
  video: MediaType;
  autoplay: boolean;
  loop: boolean;
  muted: boolean;
  controls: boolean;
}

export const Video = ({ video, loop, muted, autoplay, controls }: Props) => {
  const [isPlaying, toggleIsPlaying] = useToggle(autoplay);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (controls) {
      return;
    }

    if (isPlaying) {
      videoRef.current?.play();
    } else {
      videoRef.current?.pause();
    }
  }, [isPlaying, controls]);

  return (
    <div
      className={clsx('group size-full', !controls && 'cursor-pointer')}
      onClick={!controls ? toggleIsPlaying : undefined}
    >
      {!controls && !isPlaying && (
        <div className="absolute inset-0 z-10 flex items-center justify-center">
          <FaPlay className="size-[74px] text-white transition-transform group-hover:scale-110 md:size-[234px]" />
        </div>
      )}
      <StrapiVideo
        autoPlay={autoplay ?? undefined}
        className="size-full"
        controls={controls}
        loop={loop ?? undefined}
        muted={autoplay ? true : (muted ?? undefined)}
        playsInline={autoplay ? true : undefined}
        ref={videoRef}
        video={video}
      />
    </div>
  );
};
