'use client';

import { clsx } from 'clsx';
import Image from 'next/image';

import { JsonFieldType } from '@/strapi/types/helper';

import { VodEnrichedItem } from './VodGrid';

type Props = VodEnrichedItem & { isSliderMode: boolean; translations: JsonFieldType; showLanguageBadge?: boolean };

export function VodCard({
  isSliderMode,
  youtubeLink,
  fetchedTitle,
  fetchedThumbnail,
  translations,
  showLanguageBadge,
  locale,
  games,
  fetchedAuthor,
}: Props) {
  const gameTitle = games?.at(0)?.title;

  return (
    <div
      className={clsx(
        'group relative h-full overflow-hidden rounded-xl bg-white lg:max-xl:rounded-sm',
        isSliderMode && 'w-[266px] flex-shrink-0 lg:max-xl:w-[195px]',
      )}
    >
      <a href={youtubeLink?.url} rel="noopener noreferrer" target="_blank">
        <div className="relative aspect-[2.1] w-full">
          <Image
            alt={fetchedTitle}
            className="rounded-t-xl object-cover transition group-hover:opacity-90 lg:max-xl:rounded-t-sm"
            fill
            sizes="(max-width: 768px) 266px, 100vw"
            src={fetchedThumbnail}
          />
        </div>
        <Badge isBlueBg text={translations.vod ?? 'VOD'} />
        {showLanguageBadge && <Badge alignToEnd text={translations[locale ?? 'en'] ?? 'en'} />}
        <div className="flex flex-col justify-between gap-0.5 p-2.5 lg:max-xl:gap-1 lg:max-xl:p-1.5">
          {gameTitle && (
            <div className="font-base text-dark-default text-[9px] leading-none font-extrabold">{gameTitle}</div>
          )}
          <div className="flex flex-col justify-end gap-0.5 lg:max-xl:gap-1">
            <div className="font-base text-dark-default max-w-full truncate text-[10px] leading-none font-bold">
              {fetchedTitle}
            </div>
            <div className="font-primary text-gray-dark text-[9px] leading-none font-extrabold">{fetchedAuthor}</div>
          </div>
        </div>
      </a>
    </div>
  );
}

const Badge = ({ text, alignToEnd, isBlueBg }: { text: string; alignToEnd?: boolean; isBlueBg?: boolean }) => (
  <div
    className={clsx(
      'font-primary absolute top-2.5 rounded-sm px-[5px] py-[3px] text-[10px] leading-none font-bold text-white uppercase',
      alignToEnd ? 'end-2.5' : 'start-2.5',
      isBlueBg ? 'bg-[#0F58F4]' : 'bg-dark-default',
    )}
  >
    {text}
  </div>
);
