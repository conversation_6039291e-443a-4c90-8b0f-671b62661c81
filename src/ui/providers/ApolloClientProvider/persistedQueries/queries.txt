292628aaad5633821cfc142cb4b73af375ab860edbababc0cfe6920cc4e23609
query GameData($matchId: String!) {
  gameData(
    matchId: $matchId
    sort: [{field: "sequence", order: ORDER_DESC}]
    limit: 1
  ) {
    data {
      state {
        contestants {
          score
          players {
            id
            stats {
              kills: kills(victimTypes: [PLAYER], classifications: [REGULAR])
              headshots: kills(
                victimTypes: [PLAYER]
                classifications: [REGULAR]
                attributes: [HEADSHOT]
              )
              assists(types: [DAMAGE])
              deaths
              damage
            }
            meta {
              character {
                name
              }
            }
            attributes {
              gold: value(type: GOLD) {
                current
              }
              xp: value(type: EXPERIENCE) {
                current
              }
            }
          }
        }
      }
    }
  }
}

27678a7d980b26f66f87ef01af39084d0d468e373dd11a7dc110b8397398fa1c
query MatchSeries($tournamentIds: [String!]!) {
  matchSeries(
    filters: [{tournamentIds: $tournamentIds}]
    limit: 100
    sorts: [{field: START_TIME, order: ORDER_ASC}]
  ) {
    items {
      id
      status
      startTime
      position
      tournament {
        id
        name
        variant
        type
      }
      streams {
        url
        language
        primary
      }
      metadata {
        id
        type
        position
        name
      }
      contestants {
        id
        score
        result
        rank
        points
        team {
          id
          name
          images {
            id
            type
          }
        }
        members {
          player {
            id
            name
            nationality
          }
          role {
            id
            name
          }
        }
      }
      links {
        direction
        result
        linkedMatchSeries {
          id
        }
      }
      matches {
        id
        sequence
        status
        contestants {
          id
          points
          rank
          result
          score
          team {
            id
          }
        }
        gameMap {
          id
          name
        }
      }
    }
  }
}

2b53606f9db7a45a9f746a858519224588950f346af4ee2c3a731412dbc72252
query Tournaments($tournamentIds: [String!]!) {
  tournaments(
    filter: [{ids: $tournamentIds}]
    limit: 100
    sort: [{field: START_TIME, order: ORDER_ASC}]
  ) {
    result {
      id
      name
      status
      startTime
      endTime
      prizePool {
        rank
        amount
        currency
      }
      streams {
        url
        language
        primary
      }
      contestants {
        id
        rank
        createdAt
        updatedAt
        team {
          id
          name
          club {
            id
            name
          }
          images {
            id
            type
          }
        }
        members {
          role {
            name
          }
          player {
            name
            nationality
          }
        }
      }
    }
  }
}

