import { Locale } from '@/hooks/i18n/const';
import { enrichVodItems } from '@/strapi/api/utils/vod';
import { DynamicZoneBlock, VOD_BLOCK_KEY, VodBlockType } from '@/strapi/types/block';

import { getVodData } from '../../collection/vods';

export async function assembleVodBlocks(blocks: DynamicZoneBlock[], locale: Locale, data?: any) {
  const isVodBlockPresent = blocks.some((b) => b.__component === VOD_BLOCK_KEY);
  if (!isVodBlockPresent) {
    return blocks;
  }

  const gameSlug = data?.slug;
  const fetchedVodData = await getVodData(locale, gameSlug);

  const vodBlocks = blocks.filter((b) => b.__component === VOD_BLOCK_KEY) as VodBlockType[];
  for (const block of vodBlocks) {
    const hasExistingVods = Array.isArray(block.vods) && block.vods.length > 0;

    if (hasExistingVods) {
      block.vods = await enrichVodItems(block.vods);
    } else {
      block.vods = await enrichVodItems(fetchedVodData ?? []);
    }
  }
}
