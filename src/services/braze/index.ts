import 'client-only';

import { AuthMethod, UpdateUserValues } from '../auth/types';

const apiKey = process.env.NEXT_PUBLIC_BRAZE_API_KEY as string;
const baseUrl = process.env.NEXT_PUBLIC_BRAZE_BASE_URL as string;

if (!apiKey || !baseUrl) {
  throw new Error('Missing BRAZE env variables!');
}

//! NEVER IMPORT BRAZE DIRECTLY
//  braze import depends on the window.navigator, which produces an error when Next tries to bundle it on the server
//  all braze consumers have to do dynamic imports (see below and `InitializeBraze`) and ensure that deps are loaded on the client only!

export async function initializeBraze() {
  const braze = await import('@braze/web-sdk');

  braze.initialize(apiKey, { baseUrl, enableLogging: false, allowUserSuppliedJavascript: true });
  braze.automaticallyShowInAppMessages();

  braze.logCustomEvent('app_opened', { platform: 'web', source: '' });
}

export async function changeBrazeUser(id: string, loginMethod: AuthMethod) {
  const braze = await import('@braze/web-sdk');

  braze.changeUser(id);
  braze.openSession();

  braze.logCustomEvent('user_signed_in_web', { method: loginMethod, timestamp: Date.now() });
}

export async function logBrazeEvent(eventName: string, data: any) {
  const braze = await import('@braze/web-sdk');

  braze.logCustomEvent(eventName, data);
}

export async function logButtonClickedEvent(data: { location: string; button_name: string }) {
  logBrazeEvent('button_clicked', { page_name: document.location.pathname, ...data });
}

export async function updateBrazeUser({
  email,
  username,
  name,
  surname,
  birthdate,
  gender,
  address,
  zipcode,
  city,
  state,
  country,
  favoriteGames,
  favoriteClubs,
  favoritePlayers,
}: Omit<UpdateUserValues, 'emailNotificationsPermitted' | 'pushNotificationsPermitted'>) {
  const braze = await import('@braze/web-sdk');

  const user = braze.getUser();
  const updatedAttributes = [];

  if (email !== undefined) {
    user?.setEmail(email);
    updatedAttributes.push('email');
  }

  if (name !== undefined) {
    user?.setFirstName(name);
    updatedAttributes.push('name');
  }

  if (surname !== undefined) {
    user?.setLastName(surname);
    updatedAttributes.push('surname');
  }

  if (country !== undefined) {
    user?.setCountry(country);
    updatedAttributes.push('country');
  }

  if (username !== undefined) {
    user?.setCustomUserAttribute('username', username);
    updatedAttributes.push('username');
  }

  if (birthdate !== undefined) {
    const [month, day, year] = birthdate.split('/');
    user?.setDateOfBirth(Number(year), Number(month), Number(day));
    updatedAttributes.push('birthdate');
  }

  if (gender !== undefined) {
    let brazeGender: 'm' | 'f' | 'o' | 'p';
    switch (gender) {
      case 'male':
        brazeGender = 'm';
        break;
      case 'female':
        brazeGender = 'f';
        break;
      case 'other':
        brazeGender = 'o';
        break;
      default:
        brazeGender = 'p';
        break;
    }
    user?.setGender(brazeGender);
    updatedAttributes.push('gender');
  }

  const [address1, address2] = address?.split('\n') ?? ['', ''];
  if (address1 !== undefined) {
    user?.setCustomUserAttribute('address1', address1);
    updatedAttributes.push('address1');
  }
  if (address2 !== undefined) {
    user?.setCustomUserAttribute('address2', address2);
    updatedAttributes.push('address2');
  }

  if (zipcode !== undefined) {
    user?.setCustomUserAttribute('zipcode', zipcode);
    updatedAttributes.push('zipcode');
  }

  if (city !== undefined) {
    user?.setHomeCity(city);
    updatedAttributes.push('city');
  }

  if (state !== undefined) {
    user?.setCustomUserAttribute('state', state);
    updatedAttributes.push('state');
  }

  if (favoriteGames !== undefined) {
    user?.setCustomUserAttribute('favoriteGames', favoriteGames);
    updatedAttributes.push('favoriteGames');
  }

  if (favoriteClubs !== undefined) {
    user?.setCustomUserAttribute('favoriteClubs', favoriteClubs);
    updatedAttributes.push('favoriteClubs');
  }

  if (favoritePlayers !== undefined) {
    user?.setCustomUserAttribute('favoritePlayers', favoritePlayers);
    updatedAttributes.push('favoritePlayers');
  }

  braze.logCustomEvent('profile_updated', { fields_updated: updatedAttributes });
}

export type UpdateUserNotificationPreferencesValues = Pick<
  UpdateUserValues,
  'emailNotificationsPermitted' | 'pushNotificationsPermitted'
>;

export async function updateBrazeUserNotificationPreferences({
  emailNotificationsPermitted,
  pushNotificationsPermitted,
}: UpdateUserNotificationPreferencesValues) {
  const braze = await import('@braze/web-sdk');
  const user = braze.getUser();

  if (emailNotificationsPermitted !== undefined) {
    user?.setEmailNotificationSubscriptionType(emailNotificationsPermitted ? 'opted_in' : 'unsubscribed');
  }

  if (pushNotificationsPermitted !== undefined) {
    user?.setPushNotificationSubscriptionType(pushNotificationsPermitted ? 'opted_in' : 'unsubscribed');
  }
}
