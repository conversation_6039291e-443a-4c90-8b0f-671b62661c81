import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { PostBlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { NewsPageHeader } from '@/blocks/shared/Post/PostPageHeader';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getSponsor, getSponsorSeo } from '@/strapi/api/collection/partner';
import { redirectAccordingToConfig } from '@/strapi/api/collection/redirect';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale<{ slug: string }>): Promise<Metadata> {
  const { locale, slug } = await params;
  const seo = await getSponsorSeo(locale, slug);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | Sponsors',
    description: seo?.metaDescription ?? 'Esports World Cup | Sponsors',
  };
}

export default async function SponsorPage({ params }: PageProps<{ slug: string }>) {
  const { locale, slug } = await params;
  await redirectAccordingToConfig(`/sponsors/${slug}`, locale);

  const sponsor = await getSponsor(slug, locale);
  const siteConfig = await getSiteConfig(locale);

  if (!sponsor) {
    return notFound();
  }

  const { summary, coverImage, blocks, name } = sponsor;
  return (
    <div className="flex flex-col items-center gap-8 py-30 lg:gap-12">
      <div className="flex px-4 lg:px-8 xl:px-35 2xl:justify-center">
        <div className="w-full xl:max-w-[1158px]">
          <NewsPageHeader cover={coverImage} date={null} intro={summary} title={name} />
        </div>
      </div>
      {blocks.map((b) => (
        <PostBlockRenderer {...b} key={`${b.__component}-${b.id}`} {...siteConfig} />
      ))}
    </div>
  );
}
