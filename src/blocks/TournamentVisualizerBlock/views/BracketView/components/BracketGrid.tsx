import clsx from 'clsx';
import React from 'react';

import { JsonFieldType } from '@/strapi/types/helper';

import { Bracket, BracketMatchType, Round } from '../../../utils/brackets/types';
import { ConnectorColumn } from './ConnectorColumn';
import { RoundColumn } from './RoundColumn';

type Props = Bracket & { translations: JsonFieldType };

export const BracketGrid = ({ name, rounds, translations }: Props) => {
  const roundsWithoutWinner = rounds.slice(0, -1);
  const bracketCounts = roundsWithoutWinner.map((r) => r.matches.length);
  const isFunnelledBracket = !bracketCounts.every((count) => count === bracketCounts[0]);

  const finalRoundBracketCount = rounds.at(-2)?.matches.length as number;
  const winnerRound = rounds.at(-1) as Round;

  const isThirdPlaceBracket = rounds.some((b) => b.matches.some((m) => m.type === BracketMatchType.THIRD_PLACE));
  return (
    <div
      className={clsx('flex flex-col gap-4', isThirdPlaceBracket && 'ms-[calc(220px+40px)] md:ms-[calc(264px+60px)]')}
    >
      <div className="flex flex-col ps-4 md:ps-8">
        <p className="font-primary text-dark-default text-sm leading-none font-bold uppercase md:text-lg">{name}</p>
        {/* <p>subtitle</p> */}
      </div>
      <div className="flex">
        {roundsWithoutWinner.map((r, i) => (
          <React.Fragment key={r.id}>
            <RoundColumn {...r} isFunnelled={isFunnelledBracket} tier={i} translations={translations} />
            {i !== roundsWithoutWinner.length - 1 && (
              <ConnectorColumn
                bracketCount={r.matches.length}
                connectorType={isFunnelledBracket ? 'dual' : 'single'}
                tier={i + 1}
              />
            )}
          </React.Fragment>
        ))}
        <ConnectorColumn bracketCount={finalRoundBracketCount} connectorType="single" tier={0} />
        <RoundColumn
          {...winnerRound}
          isFunnelled={isFunnelledBracket}
          tier={roundsWithoutWinner.length - 1}
          translations={translations}
        />
      </div>
    </div>
  );
};
