import clsx from 'clsx';
import { format } from 'date-fns';
import { forwardRef } from 'react';
import { useToggle } from 'usehooks-ts';

import { isFFATournament, isPVPTournament } from '@/blocks/hero/StreamHero/shared/utils/tournamentType';
import { SIMPLE_FFA_TOURNAMENTS } from '@/config/tournaments';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { Stream } from '@/services/graphql/types/shared';
import { LocalMatchStatus } from '@/utils/matchSeries';

import {
  MatchSeriesMetadata,
  MatchSeriesMetadataType,
  TournamentVariant,
} from '../../../../../../../../services/graphql/types/matchSeries';
import { JsonFieldType } from '../../../../../../../../strapi/types/helper';
import { cn } from '../../../../../../../../utils/cn';
import { getLocalizedMatchDescriptor } from '../../../../../../../../utils/localization/getLocalizedMatchDescriptor';
import { GameStream } from '../../../../types/GameStream';
import { Contestant, MapResult } from '../../../../types/StreamHeroMatch';
import { ContestantRow } from './ContestantRow';
import { DetailsView } from './DetailsView';
import { FFAContestantRow } from './FFAContestantRow';
import { FFAStatsModal } from './FFAStatsModal';
import { StatsModal } from './StatsModal';
interface MatchBoxProps {
  tournamentId: string;
  isSelected: boolean;
  onSelect: () => void;
  startTime: string;
  // stage: string;
  status: LocalMatchStatus;
  stream: Stream | null;
  contestants: Contestant[];
  type: TournamentVariant;
  metadata: MatchSeriesMetadata[];
  mapResults: MapResult[];
  translations?: JsonFieldType;
  apiTranslations?: JsonFieldType;
  selectedGameStream: GameStream | null;
  slug: string | null;
}

const shouldRenderStatsButton = (gameName: string) =>
  ['valorant', 'cs2', 'league-of-legends', 'dota2', 'mlbb', 'mlbb-women'].includes(gameName);

export const MatchBox = forwardRef(
  (
    {
      tournamentId,
      isSelected,
      onSelect,
      startTime,
      stream,
      contestants,
      status,
      type,
      metadata,
      mapResults,
      translations,
      apiTranslations,
      selectedGameStream,
      slug,
    }: MatchBoxProps,
    ref: React.Ref<HTMLDivElement>,
  ) => {
    const [showDetails, toggleDetails] = useToggle(false);
    const [isStatsModalOpen, toggleIsStatsModalOpen] = useToggle(false);
    const [isFFAStatsModalOpen, toggleIsFFAStatsModalOpen] = useToggle(false);
    const locale = useDateLocale();

    const isPVP = isPVPTournament(type);
    const isFFA = isFFATournament(type);

    const { stage, groupA, groupB } = getStageAndGroups(metadata, apiTranslations ?? {});

    return (
      <div
        className={clsx(
          'border-white-dirty group relative h-full rounded-sm max-lg:min-w-[220px]',
          !isSelected && 'border',
        )}
        ref={ref}
      >
        {isSelected && (
          <div className="absolute start-[-4px] top-[calc(50%-6px)] hidden size-3 rotate-45 bg-[#F40F30] lg:block" />
        )}
        <div
          className={clsx(
            'relative flex h-full flex-col gap-1.5 rounded-sm',
            isSelected ? 'bg-white-dirty px-[15px] py-[11px]' : 'px-[14px] py-[10px]',
            !isSelected && status !== LocalMatchStatus.UPCOMING && 'cursor-pointer',
          )}
          onClick={isSelected || status === LocalMatchStatus.UPCOMING ? undefined : onSelect}
        >
          {isSelected && (
            <div className="absolute start-0 top-0 h-1 w-full bg-[#F40F30] max-lg:rounded-t-sm lg:h-full lg:w-1 lg:rounded-s-sm" />
          )}
          <div className="flex items-center gap-1">
            <p
              className={clsx(
                'font-primary rounded-[1px] p-0.5 text-[8px] leading-none font-bold text-white uppercase',
                status === LocalMatchStatus.LIVE ? 'bg-[#F40F30]' : 'bg-dark-default',
              )}
            >
              {status === LocalMatchStatus.LIVE
                ? (translations?.['liveNow'] ?? 'live now')
                : format(new Date(startTime), 'hh:mm a', { locale })}
            </p>
            {stage && <p className="font-base text-dark-default text-[10px] leading-none font-extrabold">{stage}</p>}
            {stream && (
              <p className="bg-gray-dark font-primary rounded-[1px] p-0.5 text-[8px] leading-none font-bold text-white uppercase">
                {stream.language}
              </p>
            )}

            {isPVP && (
              <p
                className="bg-dark-default font-primary ms-auto cursor-pointer rounded-[1px] p-0.5 text-[8px] leading-none font-bold text-white uppercase"
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDetails();
                }}
              >
                {showDetails ? (translations?.['hide'] ?? 'hide') : (translations?.['detailed'] ?? 'detailed')}
              </p>
            )}
          </div>
          <div className={clsx('h-px w-full', isSelected ? 'bg-gray' : 'bg-white-dirty')} />
          {isPVP && (
            <div className="flex flex-col">
              {showDetails ? (
                <DetailsView
                  contestants={contestants}
                  mapResults={mapResults}
                  status={status}
                  translations={translations}
                />
              ) : (
                contestants.map((c) => <ContestantRow {...c} isWinner={c.isWinner} key={c.id} status={status} />)
              )}
            </div>
          )}
          {isFFA && (
            <FFAContestantRow apiTranslations={apiTranslations} groupA={groupA} groupB={groupB} metadata={metadata} />
          )}

          <div className={clsx('h-px w-full', isSelected ? 'bg-gray' : 'bg-white-dirty')} />

          {isFFA ? (
            <button
              className={cn(
                'bg-white-dirty text-dark-default font-primary hover:bg-dark-default w-full cursor-pointer rounded-sm px-4 py-2 text-[10px] font-bold uppercase transition-colors hover:text-white',
                {
                  'hover:bg-[#F40F30]! hover:text-white!': status === LocalMatchStatus.LIVE,
                  'bg-dark-default text-white': isSelected,
                },
              )}
              onClick={(e) => {
                e.stopPropagation();
                toggleIsFFAStatsModalOpen();
              }}
            >
              {translations?.['seeDetails'] ?? 'see details'}
            </button>
          ) : (
            shouldRenderStatsButton(selectedGameStream?.slug ?? slug ?? '') &&
            status !== LocalMatchStatus.UPCOMING && (
              <button
                className={cn(
                  'bg-white-dirty text-dark-default font-primary hover:bg-dark-default w-full cursor-pointer rounded-sm px-4 py-2 text-[10px] font-bold uppercase transition-colors hover:text-white',
                  {
                    'hover:bg-[#F40F30]! hover:text-white!': status === LocalMatchStatus.LIVE,
                    'bg-dark-default text-white': isSelected,
                  },
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleIsStatsModalOpen();
                }}
              >
                {translations?.['stats'] ?? 'stats'}
              </button>
            )
          )}
          {isFFAStatsModalOpen && (
            <FFAStatsModal
              contestants={contestants}
              isRestrictedTable={SIMPLE_FFA_TOURNAMENTS.includes(tournamentId)}
              mapResults={mapResults}
              startTime={startTime}
              status={status}
              title={
                <div className="font-primary text-dark-default flex justify-center gap-[26px] rounded-md bg-white py-[21px] text-[14px] font-bold uppercase">
                  {groupA && groupB && (
                    <>
                      <span className="text-[32px]">{groupA}</span>
                      <span className="self-center text-[14px]">{translations?.['vs'] ?? 'vs'}</span>
                      <span className="text-[32px]">{groupB}</span>
                    </>
                  )}
                  {(!groupA || !groupB) && (
                    <span className="text-[32px]">{getLocalizedMatchDescriptor(metadata, apiTranslations ?? {})}</span>
                  )}
                </div>
              }
              translations={translations}
              onClose={toggleIsFFAStatsModalOpen}
            />
          )}
        </div>
        {isStatsModalOpen && (
          <StatsModal
            contestants={contestants}
            locale={locale}
            mapResults={mapResults}
            selectedGameStream={selectedGameStream}
            slug={slug}
            startTime={startTime}
            status={status}
            tournamentId={tournamentId}
            translations={translations}
            onClose={toggleIsStatsModalOpen}
          />
        )}
      </div>
    );
  },
);

function getStageAndGroups(meta: MatchSeriesMetadata[], apiTranslations: JsonFieldType) {
  const stageMeta =
    meta.find((m) => m.type === MatchSeriesMetadataType.ROUND) ??
    meta.find((m) => m.type === MatchSeriesMetadataType.BRACKET);

  const stage = stageMeta ? (apiTranslations[stageMeta.id] ?? stageMeta.name) : null;

  const groupsMetas = meta
    .filter((m) => m.type === MatchSeriesMetadataType.GROUP)
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0));

  const groupAMeta = groupsMetas.at(0);
  const groupBMeta = groupsMetas.at(1);

  const groupA = groupAMeta ? (apiTranslations[groupAMeta.id] ?? groupAMeta.name) : null;
  const groupB = groupBMeta ? (apiTranslations[groupBMeta.id] ?? groupBMeta.name) : null;

  return { stage, groupA, groupB };
}

MatchBox.displayName = 'MatchBox';
