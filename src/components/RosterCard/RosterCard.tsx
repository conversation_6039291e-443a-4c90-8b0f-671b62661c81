import clsx from 'clsx';

import { JsonFieldType } from '@/strapi/types/helper';

import { Header, PositionedSection, ViewSection } from './components';
import { PlayerSection } from './PlayersSection';
import { Roster } from './types';

type Props = Roster & { translations: JsonFieldType };

export const RosterCard = ({ game, team, translations }: Props) => {
  const isResultsVariant = game.isFinished;
  return (
    <div
      className={clsx(
        'flex w-full flex-col gap-2 rounded-lg p-2 text-white md:gap-4 md:rounded-2xl md:p-4',
        isResultsVariant ? 'bg-dark-default' : 'bg-white-dirty',
      )}
    >
      <Header game={game} teamName={team.name} theme={isResultsVariant ? 'dark' : 'light'} />
      {isResultsVariant ? (
        <PositionedSection gameSlug={game.slug} position={team.rank} translations={translations} />
      ) : (
        <ViewSection
          dateFrom={game.startDate}
          dateTo={game.endDate}
          gameSlug={game.slug}
          teamsCount={game.teamsCount}
          translations={translations}
        />
      )}
      <PlayerSection players={team.players} translations={translations} />
    </div>
  );
};
