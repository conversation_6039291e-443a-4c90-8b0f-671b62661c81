import { makeApolloClient } from '@/ui/providers/ApolloClientProvider/client';

import { TOURNAMENTS_QUERY } from './queries';
import { TournamentsQueryResponse } from './types/tournament';

export async function fetchTournamentsData(tournamentIds: string[]) {
  const client = makeApolloClient();
  const { data } = await client.query<TournamentsQueryResponse>({
    query: TOURNAMENTS_QUERY,
    variables: { tournamentIds },
  });

  return data;
}
