import { Tournament } from '@/services/graphql/types/tournament';
import { GameWithCompetitions } from '@/strapi/api/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';

import { useClubRankings } from './useClubRankings';
import { useClubRosters } from './useClubRosters';
import { useClubTournamentsAndGames } from './useClubTournamentsAndGames';

export function useClubFilteredData(
  clubId: string | null,
  allTournaments: Tournament[],
  allGames: GameWithCompetitions[],
  translations: JsonFieldType,
) {
  const { tournaments, games } = useClubTournamentsAndGames(clubId, allTournaments, allGames);
  const rosters = useClubRosters(clubId, tournaments, games, translations);
  const rankings = useClubRankings(clubId, allTournaments, allGames);

  return { rosters, games, rankings };
}
