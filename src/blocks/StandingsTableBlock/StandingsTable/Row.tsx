import clsx from 'clsx';
import Image from 'next/image';

import { useOrdinalFormatter } from '@/hooks/i18n/useOrdinalFormatter';
import ClubLogoPlaceholderImage from '@/ui/assets/images/ewc-placeholder.png';
import { formatToCurrency } from '@/utils/format';

import { TableStanding } from '../types';

export const Row = ({ ranks, clubs, prize, points }: TableStanding) => {
  const format = useOrdinalFormatter();

  const firstRank = ranks[0];
  const lastRank = ranks.at(-1);
  const isMultipleRanks = lastRank && firstRank !== lastRank;

  return (
    <tr
      className={clsx(
        'border-gray-easy not-last:border-b',
        'last:[&>td:first-child]:rounded-es-lg last:[&>td:last-child]:rounded-ee-lg',
        'md:last:[&>td:first-child]:rounded-es-2xl md:last:[&>td:last-child]:rounded-ee-2xl',
        'font-primary text-dark-default text-sm leading-none font-bold',
      )}
    >
      <RowCell>
        <div className="flex flex-col px-1 text-[10px] md:text-sm">
          <p>{format(firstRank)}</p>
          {isMultipleRanks && <p>-{format(lastRank)}</p>}
        </div>
      </RowCell>
      <RowCell>
        <div className="flex flex-col gap-1">
          {clubs.map((c, i) => (
            <div className="flex items-center gap-1 md:gap-3" key={c?.id ?? i}>
              <Image
                alt=""
                className="size-[28px] object-cover md:size-8"
                height={32}
                src={c?.logo ?? ClubLogoPlaceholderImage}
                width={32}
              />
              <p className="text-start text-xs md:text-lg">{c?.name ?? 'TBD'}</p>
            </div>
          ))}
        </div>
      </RowCell>
      <RowCell>
        <p className="text-end text-xs md:text-[21px]">${formatToCurrency(prize)}</p>
      </RowCell>
      <RowCell>
        <p className="text-gold-primary text-end text-xs md:text-[21px]">
          {points ? `+${formatToCurrency(points)}` : '-'}
        </p>
      </RowCell>
    </tr>
  );
};

const RowCell = ({ children }: React.PropsWithChildren) => {
  return (
    <td className="bg-white px-2.5 py-1.5 first:ps-2.5 last:pe-2.5 md:py-4 md:first:ps-6 md:last:pe-6">{children}</td>
  );
};
