'use client';

import clsx from 'clsx';
import { useRef } from 'react';

import { DateDescriptorCell } from '@/app/[locale]/(app)/schedule/_components/GridContent/DateDescriptorCell';
import { generateWeekDatesBetween } from '@/app/[locale]/(app)/schedule/_components/utils';
import { WeekLabels } from '@/app/[locale]/(app)/schedule/_components/WeekLabels';
import { useScrollToToday } from '@/app/[locale]/(app)/schedule/hooks/useScrollToToday';
import { useScrollIntoViewOnSelect } from '@/blocks/FestivalFilterBlock/useScrollIntoViewOnSelect';
import { useDragToScroll } from '@/components/ScrollWithFadeWrapper/useDragToScroll';
import { useFestivalFilter } from '@/context/FestivalFilterContext';
import { FestivalFilterBlockType } from '@/strapi/types/festivalBlock';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { VenueCard } from './VenueCard';

export const FestivalFilterBlock = ({
  section,
  venues,
  translations,
  tournamentStart,
  tournamentEnd,
  weekTickets,
}: FestivalFilterBlockType) => {
  const weekDates = generateWeekDatesBetween(tournamentStart ?? '', tournamentEnd ?? '');

  const { selectedVenueId, setSelectedVenueId, selectedDate, setSelectedDate } = useFestivalFilter();
  const { isSm } = useScreenType();

  const { itemRefs, scrollContainerRef: venueScrollRef } = useScrollIntoViewOnSelect<number>(selectedVenueId);
  const weekScrollRef = useRef<HTMLDivElement>(null);
  const allDates = weekDates.flatMap((wd) => wd.dates);

  useDragToScroll(venueScrollRef);
  useDragToScroll(weekScrollRef);

  useScrollToToday({
    allDates,
    scrollContainerRef: weekScrollRef,
    dayWidth: 100,
    isEnabled: !!tournamentStart && !!tournamentEnd,
  });

  return (
    <BlockSectionWrapper {...section} styleConfig={{ paddingBottom: 0, paddingTop: isSm ? 4 : 40 }}>
      <div
        className="flex items-stretch gap-2 max-md:overflow-x-auto max-md:overflow-y-hidden md:grid md:grid-cols-5 md:gap-2 md:overflow-visible lg:grid-cols-8 lg:gap-4"
        ref={venueScrollRef}
      >
        <div
          className={clsx(
            'col-start-1 row-start-1 flex w-full cursor-pointer flex-col justify-end rounded-lg p-3 shadow-sm max-lg:row-span-1! max-md:min-w-[150px]',
            selectedVenueId === null ? 'bg-dark-default text-white' : 'text-dark-default bg-white',
          )}
          style={{
            gridRow: `span ${Math.ceil(venues.length / 7)} / span ${Math.ceil(venues.length / 7)}`,
          }}
          onClick={() => setSelectedVenueId(null)}
        >
          <span className="font-riforma text-sm font-bold lg:text-lg">
            {translations?.['showAllVenues'] ?? 'Show all Venues'}
          </span>
        </div>

        {venues.map((venue) => (
          <div
            key={venue.id}
            ref={(el) => {
              if (el) itemRefs.current.set(venue.id, el);
              else itemRefs.current.delete(venue.id);
            }}
          >
            <VenueCard
              isSelected={selectedVenueId === venue.id}
              translations={translations}
              venue={venue}
              onSelect={() => setSelectedVenueId(selectedVenueId === venue.id ? null : venue.id)}
            />
          </div>
        ))}
      </div>

      {tournamentStart && tournamentEnd && (
        <div className="hide-scrollbar mt-6 w-full overflow-hidden overflow-x-auto" ref={weekScrollRef}>
          <div className="w-max">
            <WeekLabels
              dayColumnWidth={100}
              translations={translations!}
              weekDates={weekDates}
              weekTickets={weekTickets ?? []}
            />
            <div className="mt-2 flex">
              {weekDates.flatMap((wd, groupIndex) =>
                wd.dates.map((date, idx) => (
                  <div
                    className={clsx('relative flex h-full cursor-pointer items-stretch', {
                      'border-l border-[#DBDBDB]': idx !== 0 || weekDates.indexOf(wd) !== 0,
                    })}
                    key={`${groupIndex}-${date}`}
                    style={{ width: 100 }}
                    onClick={() => setSelectedDate(selectedDate === date ? null : date)}
                  >
                    <DateDescriptorCell date={date} isSelected={selectedDate === date} translations={translations!} />
                  </div>
                )),
              )}
            </div>
          </div>
        </div>
      )}
    </BlockSectionWrapper>
  );
};
