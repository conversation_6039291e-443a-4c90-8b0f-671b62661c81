'use client';

import capitalize from 'lodash/capitalize';
import { useState } from 'react';
import { useToggle } from 'usehooks-ts';

import { SearchInput } from '@/components/SearchInput';
import { WhereToWatchBlockType } from '@/strapi/types/gamePageBlock';
import { Button } from '@/ui/components/Button';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { useGameStreams } from './hooks';
import { LanguageStreamsList } from './LanguageStreamsList';
import { WhereToWatchModal } from './WhereToWatchModal';

const VISIBLE_LANGUAGES_COUNT = 5;

export const WhereToWatchBlock = ({ section, providers, translations }: WhereToWatchBlockType) => {
  const [searchValue, setSearchValue] = useState('');
  const [isModalOpen, toggleIsModalOpen] = useToggle(false);

  const { streamsByLanguage } = useGameStreams(providers);

  const normalizedSearch = searchValue.length >= 2 ? searchValue.toLowerCase() : '';
  const filteredStreams = streamsByLanguage.filter((sl) => sl.language.toLowerCase().includes(normalizedSearch));

  const isFiltered = !!searchValue;
  const isLanguagesToggleVisible = !isFiltered && filteredStreams.length > VISIBLE_LANGUAGES_COUNT;

  const visibleLanguages = filteredStreams.slice(0, VISIBLE_LANGUAGES_COUNT);
  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-col gap-2.5 md:gap-4">
        <SearchInput
          placeholder={capitalize(translations?.['searchLanguages'] ?? 'Search Languages')}
          value={searchValue}
          onChange={setSearchValue}
        />
        <div className="flex flex-col gap-1 md:gap-2" key={searchValue}>
          {visibleLanguages.map((l, i) => (
            <LanguageStreamsList
              isExpanded={i === 0}
              key={l.languageCode}
              language={l.language}
              providers={providers}
              streams={l.streamUrls}
              translations={translations ?? {}}
            />
          ))}
        </div>
        {isLanguagesToggleVisible && (
          <Button
            brazeEventProperties={{ button_name: 'Show All Languages button', location: 'Where to Watch Block' }}
            text={translations?.['showAllLanguages'] ?? 'Show all languages'}
            onClick={toggleIsModalOpen}
          />
        )}
      </div>
      {isModalOpen && (
        <WhereToWatchModal
          providers={providers}
          streams={streamsByLanguage}
          subtitle={section?.subtitle}
          title={section?.title}
          translations={translations ?? {}}
          onClose={toggleIsModalOpen}
        />
      )}
    </BlockSectionWrapper>
  );
};
