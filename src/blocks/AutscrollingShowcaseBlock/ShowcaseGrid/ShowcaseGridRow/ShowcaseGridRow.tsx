'use client';

import { ClubType } from '@/strapi/types/collection/club';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { Card } from './Card';
import { ScrollingGridRow } from './ScrollingGridRow';

export interface ShowcaseGridRowProps {
  items: ClubType[];
  isRTL: boolean;
  isPaused: boolean;
  isLoading: boolean;
}

export const ShowcaseGridRow = (props: ShowcaseGridRowProps) => {
  const shouldAutoscroll = useIsMinimumScrollingCount(props.items.length);

  if (props.items.length === 0) {
    return null;
  }

  if (props.isLoading) {
    return <div className="box-content h-[182px] w-full py-2.5" />;
  }

  if (shouldAutoscroll) {
    return <ScrollingGridRow {...props} />;
  }

  return (
    <div className="grid grid-cols-2 gap-5 overflow-hidden py-2.5 md:grid-cols-4 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
      {props.items.map((item, index) => {
        return <Card {...item} key={index} />;
      })}
    </div>
  );
};

function useIsMinimumScrollingCount(itemsCount: number) {
  const { isSm, isMd, isLg, isXl } = useScreenType();

  switch (true) {
    case isSm:
      return itemsCount > 2;
    case isMd:
      return itemsCount > 4;
    case isLg:
      return itemsCount > 2;
    case isXl:
      return itemsCount > 3;
    default:
      return itemsCount > 4;
  }
}
