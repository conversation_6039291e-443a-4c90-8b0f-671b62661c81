'use client';

import { LocalizedLink } from '@/components/LocalizedLink';

interface Props {
  icon: string;
  title: string;
  url: string;
  onClick?: () => void;
  layout?: 'row' | 'column';
  isEmbedded?: boolean;
}

export function NavigationItem({ icon, title, url, onClick, layout = 'column', isEmbedded }: Props) {
  if (layout === 'row') {
    return (
      <LocalizedLink
        brazeEventProperties={{
          location: `Main Navigation - Navigation Item (${title})`,
          button_name: `Link (${url})`,
        }}
        href={url}
        target={isEmbedded ? '_parent' : undefined}
        onClick={onClick}
      >
        <div className="lg:hover:shadow-sidebar-item group flex h-full items-center gap-3 p-3 lg:rounded-2xl">
          <span className="material-symbols-outlined text-dark-default shrink-0 text-[24px]! lg:text-[26px]! lg:group-hover:text-[var(--color-gold-primary)]!">
            {icon}
          </span>
          <span className="font-base lg:font-primary text-dark-default mt-px text-[13px] leading-none font-bold capitalize lg:text-[10px]">
            {title}
          </span>
        </div>
      </LocalizedLink>
    );
  }

  return (
    <LocalizedLink
      brazeEventProperties={{ location: `Main Navigation - Navigation Item (${title})`, button_name: `Link (${url})` }}
      href={url}
      target={isEmbedded ? '_parent' : undefined}
      onClick={onClick}
    >
      <div className="lg:hover:shadow-sidebar-item group grid h-full items-start justify-center justify-items-center gap-1 rounded-2xl p-3 text-center lg:flex-col lg:items-center lg:justify-center">
        <span className="material-symbols-outlined text-dark-default text-[24px]! lg:text-[26px]! lg:group-hover:text-[var(--color-gold-primary)]!">
          {icon}
        </span>
        <span className="font-base text-dark-default text-[10px] leading-none font-normal capitalize lg:font-bold">
          {title}
        </span>
      </div>
    </LocalizedLink>
  );
}
