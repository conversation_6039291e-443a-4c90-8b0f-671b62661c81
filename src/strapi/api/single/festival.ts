import { Locale } from '@/hooks/i18n/const';
import { DynamicZoneBlock } from '@/strapi/types/block';
import {
  FESTIVALS_COLLECTION_KEY,
  FestivalType,
  VENUES_COLLECTION_KEY,
  VenueType,
} from '@/strapi/types/collection/festival';
import { SeoType } from '@/strapi/types/shared/seo';

import { StrapiBase } from '../../types/base';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils/block';

interface FestivalsPageData extends StrapiBase {
  blocks: DynamicZoneBlock[];
  showInteractiveMap: boolean;
  interactiveMapShowRibbon: boolean;
}

export async function getFestivalPageData(locale: Locale) {
  try {
    const festivalsPage = await strapiClient.single('festivals-page').find({
      populate: { blocks: getDynamicZoneBlocks({ includeFestivalPageBlocks: true }) },

      ...getStrapiDefaultParams(locale),
    });

    const pageData = festivalsPage.data as FestivalsPageData;
    if (!pageData) {
      return null;
    }

    const festivalData = await fetchFestival(locale);
    const venuesData = await fetchVenues(locale);

    await assembleBlocksWithAdditionalData(pageData.blocks, locale, { festivalData, venuesData });
    return { pageData, festivalData };
  } catch (error) {
    console.error('Festivals Page data not fetched!', error);
    throw new Error('Festivals Page data not fetched!');
  }
}

export async function fetchFestival(locale: Locale) {
  try {
    const allFestivals: FestivalType[] = [];
    let page = 1;
    let pageCount = 1;

    do {
      const festival = await strapiClient.collection(FESTIVALS_COLLECTION_KEY).find({
        sort: ['startDate:asc'],
        populate: ['posterImage', 'icon', 'venues'],
        pagination: { pageSize: 100, withCount: true, page },
        ...getStrapiDefaultParams(locale),
      });

      const { data, meta } = festival;
      allFestivals.push(...(data as unknown as FestivalType[]));
      pageCount = meta?.pagination?.pageCount || 1;
      page += 1;
    } while (page <= pageCount);

    return { festival: allFestivals };
  } catch (error) {
    console.error('Festival not fetched!', error);
    throw new Error('Festival not fetched!');
  }
}

export async function fetchVenues(locale: Locale) {
  try {
    const venues = await strapiClient.collection(VENUES_COLLECTION_KEY).find({
      ...getStrapiDefaultParams(locale),
    });
    const { data } = venues;
    return { venues: data as unknown as VenueType[] };
  } catch (error) {
    console.error('Venues not fetched!', error);
    throw new Error('Venues not fetched!');
  }
}

export async function getFestivalsPageSeo(locale: Locale) {
  try {
    const festivalsPage = await strapiClient
      .single('festivals-page')
      .find({ populate: 'seo', ...getStrapiDefaultParams(locale) });
    return festivalsPage.data.seo as SeoType | null;
  } catch (error) {
    console.error('Festival Page SEO not fetched!', error);
    return null;
  }
}
