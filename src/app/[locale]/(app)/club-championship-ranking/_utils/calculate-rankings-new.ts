import { PrizeCurrency, Tournament, TournamentContestant, TournamentStatus } from '@/services/graphql/types/tournament';
import { GameType } from '@/strapi/types/collection/game';

import { isLateSubmissionClub } from './isLateSubmissionClub';
import { ClubData, Competition } from './types';

interface ContestantWithTimestamps extends TournamentContestant {
  updatedAt: string;
  createdAt: string;
}

interface ProcessedTournamentContestant extends TournamentContestant {
  rank: number | null;
  updatedAt: Date;
  createdAt: Date;
}

interface ProcessedTournament extends Omit<Tournament, 'contestants'> {
  contestants: ProcessedTournamentContestant[];
}

export function calculateRankings(
  competitionsSlugs: Record<string, string[]>,
  tournaments: Tournament[],
  maxTimestamp: Date,
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo' | 'title'>[],
): ClubData[] {
  // Filter and process tournaments based on maxTimestamp
  const processedTournaments = tournaments.map((tournament) => {
    const processedContestants = (tournament.contestants || [])
      .map((contestant) => {
        const contestantWithTimestamps = contestant as ContestantWithTimestamps;
        const updatedAt = new Date(
          contestantWithTimestamps.updatedAt || tournament.startTime || tournament.endTime || maxTimestamp,
        );
        const createdAt = new Date(contestantWithTimestamps.createdAt || tournament.startTime || maxTimestamp);

        return {
          ...contestant,
          rank: updatedAt <= maxTimestamp ? contestant.rank : null,
          updatedAt,
          createdAt,
        };
      })
      .filter((contestant) => contestant.createdAt <= maxTimestamp);

    return {
      ...tournament,
      contestants: processedContestants,
    };
  }) as ProcessedTournament[];

  // Compute competition ranking based on multiple tournaments
  const competitions: Record<string, Competition> = {};

  Object.entries(competitionsSlugs).forEach(([slug, ids]) => {
    // Find the corresponding game competition to get metadata
    const gameCompetition = gameCompetitions.find((comp) => comp.slug === slug);

    // Find tournaments for this competition and get the earliest startTime
    const competitionTournaments = processedTournaments.filter((tournament) => ids.includes(tournament.id));

    const timestamp = competitionTournaments.length
      ? Math.min(...competitionTournaments.map((t) => new Date(t.startTime || '').getTime()))
      : null;

    competitions[slug] = {
      prizePool: [],
      contestants: {},
      // Add competition metadata
      logoDark: gameCompetition?.logoDark || null,
      logoLight: gameCompetition?.schedulePopupLogo || null,
      slug: gameCompetition?.slug || slug,
      title: gameCompetition?.title || null,
      timestamp,
    };

    competitionTournaments.forEach((tournament) => {
      const sortedPrizePool = (tournament.prizePool || []).toSorted((a, b) => (a.rank || 0) - (b.rank || 0));
      competitions[slug].prizePool = sortedPrizePool.concat(competitions[slug].prizePool);

      const contestants = (tournament.contestants || []).filter(
        (c): c is ProcessedTournamentContestant =>
          !!c &&
          !!c.team &&
          !!c.team.club &&
          typeof c.team.club.id === 'string' &&
          'createdAt' in c &&
          'updatedAt' in c,
      );

      // Compute the worst possible rank for this tournament
      // Take the highest rank from the prize pool that is not already assigned to a contestant in this tournament
      const worstPossibleRank = sortedPrizePool
        .map((p) => p.rank || 0)
        .reverse()
        .find((rank) => !contestants.find((c) => c.rank === rank));

      // Merge contestants from all tournaments of the same competition
      contestants.forEach((contestant) => {
        if (!contestant.team) return;

        competitions[slug].contestants[contestant.team.id] = {
          team: contestant.team,
          rank: {
            current: contestant.rank, // Final rank which should be displayed in the ranking
            min: worstPossibleRank || null, // Minimum rank which will be used for computing the minimum guarantee prizes
          },
          tournamentId: tournament.id, // Store tournament ID for reference
        };
      });
    });
  });

  const clubChampionship: Record<string, ClubData> = {};
  // Group contestant data from multiple competitions by club
  Object.entries(competitions).forEach(([slug, competition]) => {
    const allContestants = Object.values(competition.contestants);

    const bestByClub: Record<string, (typeof allContestants)[0]> = {};

    allContestants.forEach((contestant) => {
      const clubId = contestant.team.club.id;
      const effectiveRank = typeof contestant.rank.current === 'number' ? contestant.rank.current : contestant.rank.min;

      const points = competition.prizePool
        .filter((p) => p.rank === effectiveRank && p.currency === 'XTS')
        .reduce((sum, p) => sum + (p.amount || 0), 0);

      const prev = bestByClub[clubId];
      const prevEffectiveRank = prev
        ? typeof prev.rank.current === 'number'
          ? prev.rank.current
          : prev.rank.min
        : null;

      const prevPoints = prevEffectiveRank
        ? competition.prizePool
            .filter((p) => p.rank === prevEffectiveRank && p.currency === 'XTS')
            .reduce((sum, p) => sum + (p.amount || 0), 0)
        : 0;

      if (!prev || points > prevPoints) {
        bestByClub[clubId] = contestant;
      }
    });

    Object.values(bestByClub).forEach((contestant) => {
      const clubId = contestant.team.club.id;
      if (!clubChampionship[clubId]) {
        clubChampionship[clubId] = {
          isSimulated: false,
          club: { id: clubId, name: contestant.team.club.name },
          competitions: {},
          prizes: {},
          medals: { gold: 0, silver: 0, bronze: 0 },
          top8Placements: 0,
          shouldBeGrayedOut: true,
          isChampionshipEligible: false,
          images: contestant.team.images || [],
          tournamentParticipated: new Set(),
        };
      }

      if (!clubChampionship[clubId].competitions[slug]) {
        clubChampionship[clubId].competitions[slug] = {
          isSimulated: false,
          rank: [],
          logoDark: competition.logoDark,
          logoLight: competition.logoLight,
          slug: competition.slug,
          title: competition.title,
          timestamp: competition.timestamp,
          prizes: {},
          isFinished: tournaments
            .filter((t) => t.id === contestant.tournamentId)
            .every((t) => t.status === TournamentStatus.FINISHED),
        };
      }

      // Calculate prizes for this contestant
      const contestantPrizes: Record<string, number> = {};
      const effectiveRank = typeof contestant.rank.current === 'number' ? contestant.rank.current : contestant.rank.min;

      if (effectiveRank !== null) {
        // Calculate the prizes for this contestant based on their calculatedRank
        competition.prizePool
          .filter((p) => p.rank === effectiveRank)
          .forEach((p) => {
            if (!p.currency) return;

            const currency = p.currency as PrizeCurrency;
            let prizeAmount = p.amount || 0;

            //! this is a special case concerning some clubs missing the deadline (see function doc)
            //! could be removed in future use cases
            const isLateSubmission = isLateSubmissionClub(slug, clubId);
            const isPointsPrize = currency === PrizeCurrency.XTS;
            if (isLateSubmission && isPointsPrize) {
              prizeAmount = 0;
            }

            if (!contestantPrizes[currency]) {
              contestantPrizes[currency] = 0;
            }
            contestantPrizes[currency] += prizeAmount;

            // Update global prizes
            if (!clubChampionship[clubId].prizes[currency]) {
              clubChampionship[clubId].prizes[currency] = 0;
            }
            clubChampionship[clubId].prizes[currency] += prizeAmount;
          });
      }

      // Store rank and prizes for this contestant
      clubChampionship[clubId].competitions[slug].rank.push({
        rank: contestant.rank.current,
        prizes: contestantPrizes,
      });

      // Track tournament participation
      clubChampionship[clubId].tournamentParticipated.add(contestant.tournamentId);
      // Update medals and top8 placements
      if (contestant.rank.current === 1) clubChampionship[clubId].medals.gold++;
      else if (contestant.rank.current === 2) clubChampionship[clubId].medals.silver++;
      else if (contestant.rank.current === 3) clubChampionship[clubId].medals.bronze++;

      if (contestant.rank.current !== null && contestant.rank.current <= 8) {
        clubChampionship[clubId].top8Placements++;
      }
    });
  });

  // Update championship eligibility for all clubs
  Object.values(clubChampionship).forEach((club) => {
    club.isChampionshipEligible = club.top8Placements >= 2 && club.medals.gold > 0;
    club.shouldBeGrayedOut = club.top8Placements < 2;
    club.tournamentCount = club.tournamentParticipated.size;
  });
  // Sort club championship ranking with extended criteria
  const clubChampionshipRanking = Object.values(clubChampionship).sort((a, b) => {
    const pointsDiff = (b.prizes['XTS'] || 0) - (a.prizes['XTS'] || 0);
    if (pointsDiff !== 0) return pointsDiff;

    const goldDiff = b.medals.gold - a.medals.gold;
    if (goldDiff !== 0) return goldDiff;

    const silverDiff = b.medals.silver - a.medals.silver;
    if (silverDiff !== 0) return silverDiff;

    const bronzeDiff = b.medals.bronze - a.medals.bronze;
    if (bronzeDiff !== 0) return bronzeDiff;

    if (a.isChampionshipEligible !== b.isChampionshipEligible) {
      return b.isChampionshipEligible ? 1 : -1;
    }

    // Then prioritize non-grayed out clubs
    if (a.shouldBeGrayedOut !== b.shouldBeGrayedOut) {
      return a.shouldBeGrayedOut ? 1 : -1;
    }

    // If all medals are equal, sort alphabetically by club name
    return (a.club.name || '').localeCompare(b.club.name || '');
  });

  // Compute rank based on total points with gaps in case of same points as previous club
  let currentRank = 0;
  let previousRank: number | null = null;
  clubChampionshipRanking.forEach((club, index) => {
    const currentPoints = club.prizes['XTS'] || 0;
    const previousPoints = clubChampionshipRanking[index - 1]?.prizes['XTS'] || 0;
    if (currentPoints !== previousPoints) currentRank = index + 1;

    club.rank = currentRank;
    club.rankDiff = previousRank !== null ? previousRank - currentRank : 0;
    previousRank = currentRank;
  });

  return clubChampionshipRanking;
}
