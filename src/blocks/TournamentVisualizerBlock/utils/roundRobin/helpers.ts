import { MatchSeries, MatchSeriesMetadata, MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';
import { convertToLocalMatchStatus, LocalMatchStatus } from '@/utils/matchSeries';

import { getPlaceholderContestants } from '../const';
import { createContestants } from '../helpers';
import { Config, Contestant } from '../types';
import { RoundRobinContestant, RoundRobinGroup, RoundRobinMatch } from './types';

export function createGroup(groupMeta: MatchSeriesMetadata, serie: MatchSeries, config: Config): RoundRobinGroup {
  const { contestants, match } = createMatchAndContestants(serie, config.translations);

  return {
    id: groupMeta.id,
    name: config.apiTranslations[groupMeta.id] ?? (groupMeta.name as string),
    position: groupMeta.position as number,
    //  resolved to appropriate status based on matches in group
    status: LocalMatchStatus.UPCOMING,
    contestants,
    matches: [match],
  };
}

export function createMatchAndContestants(matchSeries: MatchSeries, translations: JsonFieldType) {
  const contestants = createContestants(matchSeries.contestants);
  const roundRobinContestants = createRoundRobinContestants(contestants, matchSeries.status);

  const match: RoundRobinMatch = {
    id: matchSeries.id,
    contestants:
      contestants.length !== 0
        ? [...contestants]
        : (getPlaceholderContestants(2, { name: translations['tbd'] ?? 'tbd', result: '' }) as Contestant[]),
    startTime: matchSeries.startTime ?? '',
    status: convertToLocalMatchStatus(matchSeries.status),
  };

  return { match, contestants: roundRobinContestants };
}

export function createRoundRobinContestants(
  contestants: Contestant[],
  status: MatchSeriesStatus,
): RoundRobinContestant[] {
  if (contestants.length === 0) {
    return [];
  }

  const [contestantA, contestantB] = contestants;
  const isMatchSeriesFinished = status === MatchSeriesStatus.FINISHED;

  const totalPointsScoredA = isMatchSeriesFinished && contestantA.score ? contestantA.score : 0;
  const totalPointsScoredB = isMatchSeriesFinished && contestantB.score ? contestantB.score : 0;

  const isWinnerA = totalPointsScoredA > totalPointsScoredB;
  const isWinnerB = totalPointsScoredB > totalPointsScoredA;

  const isDraw = isMatchSeriesFinished && !isWinnerA && !isWinnerB;

  return [
    {
      ...contestantA,
      pointsScored: totalPointsScoredA,
      pointsLost: totalPointsScoredB,
      wins: isWinnerA ? 1 : 0,
      draws: isDraw ? 1 : 0,
      losses: isWinnerB ? 1 : 0,
      diff: totalPointsScoredA - totalPointsScoredB,
    },
    {
      ...contestantB,
      pointsScored: totalPointsScoredB,
      pointsLost: totalPointsScoredA,
      wins: isWinnerB ? 1 : 0,
      draws: isDraw ? 1 : 0,
      losses: isWinnerA ? 1 : 0,
      diff: totalPointsScoredB - totalPointsScoredA,
    },
  ];
}

export function sortMatchGroups(groups: RoundRobinGroup[]) {
  groups.sort((a, b) => a.position - b.position);
  groups.forEach((g) => {
    g.matches.sort((a, b) => (a.startTime < b.startTime ? -1 : a.startTime > b.startTime ? 1 : 0));
  });
}
