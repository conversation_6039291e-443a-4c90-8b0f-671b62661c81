import { ClubData, SimulatedClubCompetition, SimulatedClubData } from './types';

export const sortAndUpdateRanks = (rankings: SimulatedClubData[]): SimulatedClubData[] => {
  // First sort by points in descending order
  const sortedRankings = rankings.sort((a, b) => {
    // this is somewhat shared between calculate-rankings-new.ts consider extracting
    const aPoints = a.simulatedPoints ?? a.prizes.XTS ?? 0;
    const bPoints = b.simulatedPoints ?? b.prizes.XTS ?? 0;

    const pointsDiff = bPoints - aPoints;
    if (pointsDiff !== 0) return pointsDiff;

    if (a.isChampionshipEligible !== b.isChampionshipEligible) {
      return b.isChampionshipEligible ? 1 : -1;
    }

    if (a.shouldBeGrayedOut !== b.shouldBeGrayedOut) {
      return a.shouldBeGrayedOut ? 1 : -1;
    }

    return (a.club.name || '').localeCompare(b.club.name || '');
  });

  // Create an array to store ranks
  const ranks: number[] = [];
  const points: number[] = sortedRankings.map((r) => r.simulatedPoints ?? r.prizes.XTS ?? 0);

  // Calculate ranks
  for (let i = 0; i < points.length; i++) {
    if (i === 0 || points[i] !== points[i - 1]) {
      ranks[i] = i + 1;
    } else {
      ranks[i] = ranks[i - 1];
    }
  }

  // Apply ranks to clubs
  return sortedRankings.map((club, index) => ({
    ...club,
    rank: ranks[index],
    rankDiff: club.originalRank ? club.originalRank - ranks[index] : 0,
  }));
};

export const simulatePoints = (rankings: ClubData[]): SimulatedClubData[] => {
  const simulatedRankings = rankings.map((ranking) => {
    const simulatedCompetitions = Object.fromEntries(
      Object.entries(ranking.competitions).map(([competitionSlug, competition]) => [
        competitionSlug,
        {
          ...competition,
          isSimulated: true,
          simulatedPoints:
            competition.rank.sort(
              (a: { prizes: { XTS: number } }, b: { prizes: { XTS: number } }) =>
                (a.prizes?.XTS ?? 0) - (b.prizes?.XTS ?? 0),
            )[0]?.prizes?.XTS ?? null,
        } as SimulatedClubCompetition,
      ]),
    );

    return {
      ...ranking,
      isSimulated: true,
      simulatedPoints: null,
      competitions: simulatedCompetitions,
      originalRank: ranking.rank,
    } as SimulatedClubData;
  });

  // Sort and calculate initial ranks
  return sortAndUpdateRanks(simulatedRankings);
};
