'use client';

import clsx from 'clsx';

import { useDateLocaleFormatter } from '@/hooks/i18n/useDateLocale';
import { JsonFieldType } from '@/strapi/types/helper';

import { FfaMatch } from '../../utils/ffa/types';
import { Table } from './Table';

type Props = FfaMatch & { translations: JsonFieldType; isConstrictedTable: boolean };

export const FreeForAllTable = ({ startTime, status, contestants, title, translations, isConstrictedTable }: Props) => {
  const format = useDateLocaleFormatter();

  return (
    <div className="bg-white-dirty flex flex-col gap-0.5 rounded-sm p-2 md:gap-1 md:rounded-lg">
      <div className="font-primary flex items-center justify-between text-[10px] leading-none font-bold uppercase">
        <p className="text-dark-default">{startTime ? format(startTime, 'eee, MMM d - h:mm a') : ''}</p>
        <p
          className={clsx(
            'bg-dark-default flex h-[17px] items-center rounded-sm px-[5px] leading-[1.1] text-white',
            !status && 'invisible',
          )}
        >
          {(status && translations[status]) || status}
        </p>
      </div>
      <p className="bg-dark-default font-primary rounded-xs p-0.5 text-center text-[10px] leading-none font-bold text-white uppercase md:rounded-sm md:p-1 md:text-xs">
        {title}
      </p>
      <Table contestants={contestants} isConstrictedTable={isConstrictedTable} translations={translations} />
    </div>
  );
};
