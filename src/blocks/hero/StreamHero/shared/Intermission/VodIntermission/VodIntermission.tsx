import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';
import DefaultBackgroundImage from '@/ui/assets/images/abstract-background-5.png';
import { Only } from '@/ui/components/Only';

import { IntermissionProps } from '../Intermission';
import { UpcomingMatchesRow } from './UpcomingMatchesRow';
import { VodsRow } from './VodsRow';

export const VodIntermission = ({ isLoading, ...rest }: IntermissionProps) => {
  return (
    <>
      <div className="bg-dark-default absolute inset-0 size-full">
        {rest.stream.backgroundImage ? (
          <StrapiImage className="size-full object-cover" image={rest.stream.backgroundImage} />
        ) : (
          <Image alt="" className="size-full object-cover" src={DefaultBackgroundImage} />
        )}
      </div>
      <div className="bg-dark-default absolute inset-0 size-full opacity-70" />
      {!isLoading && (
        <div className="relative flex w-full flex-col gap-8 max-md:py-8">
          <Only for="lgAndAbove">
            <UpcomingMatchesRow {...rest} />
          </Only>
          <VodsRow {...rest} />{' '}
        </div>
      )}
    </>
  );
};
