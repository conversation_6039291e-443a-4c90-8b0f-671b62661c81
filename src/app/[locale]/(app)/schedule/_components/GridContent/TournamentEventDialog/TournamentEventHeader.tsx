import { StrapiImage } from '@/components/StrapiImage';
import { GameType } from '@/strapi/types/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';

export function TournamentEventHeader({
  game,
  onClose,
  translations,
}: {
  game: GameType;
  onClose: () => void;
  translations: JsonFieldType | null;
}) {
  return (
    <div className="flex items-start justify-between gap-6 px-1">
      {game.logoDark && (
        <div className="h-10 max-w-[160px]">
          <StrapiImage alt="" className="h-full w-full object-contain" image={game.logoDark} />
        </div>
      )}
      <button
        className="hover:bg-gray bg-gray font-primary ms-auto shrink-0 cursor-pointer rounded-lg px-8 py-4 text-xs font-bold text-black uppercase transition"
        onClick={onClose}
      >
        {translations?.['close'] ?? 'Close'}
      </button>
    </div>
  );
}
