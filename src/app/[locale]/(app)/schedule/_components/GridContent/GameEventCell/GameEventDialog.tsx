import { GameType } from '@/strapi/types/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';

import { GameEventType } from '../../../../../../../strapi/types/collection/gameEvent';
import { BaseEventDialog } from '../shared/BaseEventDialog';

type Props = {
  gameEvent: GameEventType;
  game: GameType;
  onClose: () => void;
  translations: JsonFieldType | null;
};

export const GameEventDialog = ({ gameEvent, game, onClose, translations }: Props) => {
  const actions =
    game.isLinkingToGamePageEnabled && game.slug
      ? {
          secondary: {
            text: translations?.learnMore ?? 'learn more',
            link: `competitions/${game.slug}`,
            variant: 'glassy' as const,
          },
        }
      : undefined;

  return (
    <BaseEventDialog
      actions={actions}
      content={gameEvent.summary}
      endDate={gameEvent.endDateTime ?? gameEvent.endDate}
      isGameEvent
      keyArt={game?.keyArt}
      logo={game?.schedulePopupLogo ?? game?.logoDark}
      startDate={gameEvent.startDateTime ?? gameEvent.startDate}
      ticketsUrl={game?.ticketsUrl}
      title={gameEvent.title}
      translations={translations}
      onClose={onClose}
    />
  );
};
