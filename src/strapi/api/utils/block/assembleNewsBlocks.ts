import { Locale } from '@/hooks/i18n/const';
import { DynamicZoneBlock, NEWS_BLOCK_KEY, NewsBlockType } from '@/strapi/types/block';

import { fetchNewsArticles } from '../../collection/news';

export async function assembleNewsBlocks(blocks: DynamicZoneBlock[], locale: Locale) {
  const newsBlocks = blocks.filter((b) => b.__component === NEWS_BLOCK_KEY) as NewsBlockType[];
  for (const block of newsBlocks) {
    await assembleNewsBlock(block, locale);
  }
}

async function assembleNewsBlock(block: NewsBlockType, locale: Locale) {
  if (block.featuredArticles.length === 0) {
    const excludedTagNames = block.excludedTags?.map((tag) => tag.name).filter(Boolean);
    let excludedArticleIds: number[] = [];

    if (excludedTagNames?.length) {
      const excluded = await fetchNewsArticles(locale, {
        pagination: { start: 0, limit: 4 },
        additionalFilters: { tags: { name: { $in: excludedTagNames } } },
      });

      excludedArticleIds = excluded.articles.map((article) => article.id);
    }

    const andFilters = [];
    if (block.tag?.name) {
      andFilters.push({ tags: { name: { $eq: block.tag.name } } });
    }

    if (excludedArticleIds.length > 0) {
      andFilters.push({ id: { $notIn: excludedArticleIds } });
    }

    const data = await fetchNewsArticles(locale, {
      pagination: { start: 0, limit: 4 },
      additionalFilters: andFilters.length > 0 ? { $and: andFilters } : {},
    });

    block.featuredArticles = data?.articles ?? [];
  }

  return block;
}
