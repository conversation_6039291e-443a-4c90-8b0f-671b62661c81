import { useMemo } from 'react';

import { useConfig } from '@/app/[locale]/(app)/_components/providers/ConfigProvider';

const DEFAULT_DELAY_MS = 300000;

export function useDelay(tournamentIds: string[]) {
  const config = useConfig();
  const delayedGamesConfig = config?.delayedGames;

  return useMemo(() => {
    if (tournamentIds.length === 0) {
      return 0;
    }

    if (!delayedGamesConfig) {
      return 0;
    }

    const delayedGameConfig = Object.values(delayedGamesConfig).find((config) =>
      config.ids.every((id) => tournamentIds.includes(id)),
    );
    if (!delayedGameConfig) {
      return 0;
    }

    return delayedGameConfig.wsDelayInMilliseconds ?? DEFAULT_DELAY_MS;
  }, [delayedGamesConfig, tournamentIds]);
}
