import { RefObject, useEffect, useRef } from 'react';

import { WeekDates } from '@/app/[locale]/(app)/schedule/_components/utils';
import { useContentDirection } from '@/hooks/i18n';

interface UseScrollToCurrentWeekOptions {
  weekDates: WeekDates[];
  scrollContainerRef: RefObject<HTMLDivElement | null>;
  onCurrentWeekFound?: (weekIndex: number, today: string) => void;
  dayWidth?: number;
  shouldUseScrollIntoView?: boolean;
  isEnabled?: boolean;
}

/**
 * A hook that scrolls to the current week in a horizontal scroll container
 * @param options Configuration options for the scroll behavior
 * - weekDates: Array of week dates to search through
 * - scrollContainerRef: Reference to the scroll container
 * - onCurrentWeekFound: Optional callback when current week is found, receives weekIndex and today's date
 * - dayWidth: Width of each day column (default: 100)
 * - shouldUseScrollIntoView: Whether to use scrollIntoView instead of scrollTo (default: false)
 * - isEnabled: Whether the scroll functionality is enabled (default: true)
 */
export const useScrollToCurrentWeek = ({
  weekDates,
  scrollContainerRef,
  onCurrentWeekFound,
  dayWidth = 100,
  shouldUseScrollIntoView = false,
  isEnabled = true,
}: UseScrollToCurrentWeekOptions) => {
  const { isRTL } = useContentDirection();
  const hasScrolledRef = useRef(false);

  useEffect(() => {
    const scrollToCurrentWeek = () => {
      if (hasScrolledRef.current) return;

      const today = new Date().toISOString().split('T')[0];
      const currentWeekIndex = weekDates.findIndex((week) => week.dates.some((date) => date === today));

      if (currentWeekIndex !== -1 && scrollContainerRef.current) {
        if (shouldUseScrollIntoView) {
          const weekElement = scrollContainerRef.current.children[currentWeekIndex];
          if (weekElement instanceof HTMLElement) {
            weekElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
          }
        } else {
          const scrollPosition = currentWeekIndex * (dayWidth * 7);
          scrollContainerRef.current.scrollTo({
            left: isRTL ? -scrollPosition : scrollPosition,
            behavior: 'smooth',
          });
        }

        onCurrentWeekFound?.(currentWeekIndex, today);
        hasScrolledRef.current = true;
      }
    };

    if (isEnabled) {
      scrollToCurrentWeek();
    }
  }, [weekDates, scrollContainerRef, onCurrentWeekFound, dayWidth, shouldUseScrollIntoView, isEnabled, isRTL]);
};
