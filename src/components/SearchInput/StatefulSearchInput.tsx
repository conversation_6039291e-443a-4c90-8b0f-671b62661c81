'use client';

import { useState } from 'react';

import { SearchInput, SearchInputProps } from './SearchInput';

type StatefulSearchInputProps = Omit<SearchInputProps, 'value' | 'onChange' | 'onConfirm'> & {
  initialValue?: string;
  onSearch: (value: string) => void;
};

export const StatefulSearchInput = ({ initialValue = '', onSearch, ...rest }: StatefulSearchInputProps) => {
  const [searchValue, setSearchValue] = useState(initialValue);

  return (
    <SearchInput value={searchValue} onChange={setSearchValue} onConfirm={() => onSearch(searchValue)} {...rest} />
  );
};
