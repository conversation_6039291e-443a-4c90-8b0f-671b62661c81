import { addDays, formatISO, parseISO } from 'date-fns';

import { MobileDialogDataType } from '@/app/[locale]/(app)/schedule/_components/GridContent/GameEventCell/BaseGameEventCell';
import { GameEventType } from '@/strapi/types/collection/gameEvent';

import { FestivalType } from '../../../../../../strapi/types/collection/festival';
import { JsonFieldType } from '../../../../../../strapi/types/helper';
import { ScheduleFilter } from '../ScheduleGrid';
import { getGridColumnsForEvent, isDateInRange, WeekDates } from '../utils';
import { DateDescriptorCell } from './DateDescriptorCell';
import { FestivalEventCell } from './FestivalEventCell';
import { GameEventCell } from './GameEventCell/GameEventCell';
import { GridColumnSeparatorUnderlay } from './GridColumnSeparatorUnderlay';

interface Props {
  weekDates: WeekDates[];
  filter: ScheduleFilter;
  gameEvents: GameEventType[];
  festivals: FestivalType[];
  translations: JsonFieldType | null;
  openMobileDialog: (mobileDialogData: MobileDialogDataType) => void;
  dayColumnWidth: number;
}

export const GridContent = ({
  weekDates,
  filter,
  gameEvents,
  translations,
  openMobileDialog,
  festivals,
  dayColumnWidth,
}: Props) => {
  const today = new Date();
  const currentWeek =
    weekDates.findIndex((week) => isDateInRange(today, week.dates[0], week.dates[week.dates.length - 1])) + 1;

  const gridTemplateColumns = weekDates
    .flatMap((wd) => wd.dates)
    .map((d) => `[D${d}] ${dayColumnWidth}px`)
    .join(' ');

  return (
    <div className="relative">
      {weekDates.map((wd, index) => {
        if (wd.week !== currentWeek) return null;

        const daysBefore = weekDates.slice(0, index).reduce((acc, w) => acc + w.dates.length, 0);
        const backgroundStyle = {
          gridColumnStart: `D${wd.dates[0]}`,
          gridColumnEnd: `D${formatISO(addDays(parseISO(wd.dates[wd.dates.length - 1]), 1), {
            representation: 'date',
          })}`,
          left: `${daysBefore * dayColumnWidth}px`,
          width: `${wd.dates.length * dayColumnWidth}px`,
          zIndex: 0,
        };

        return <div className="bg-gray absolute h-full" key={`bg-${wd.week}`} style={backgroundStyle} />;
      })}

      <GridColumnSeparatorUnderlay
        columnWidth={dayColumnWidth}
        numberOfCols={weekDates.flatMap((wd) => wd.dates).length}
      />

      <div className="relative z-10 flex flex-col gap-4">
        <div className="grid grid-flow-row-dense" style={{ gridTemplateColumns }}>
          {weekDates.flatMap((wd) =>
            wd.dates.map((d, i) => (
              <div id={i === 0 ? getScheduleGridWeekId(wd.week) : undefined} key={d}>
                <DateDescriptorCell date={d} translations={translations} />
              </div>
            )),
          )}
        </div>

        {/* Game events section */}
        {(filter === 'games' || filter === 'all') && (
          <div className="grid grid-flow-row-dense gap-y-4" style={{ gridTemplateColumns }}>
            {gameEvents.map((e) => {
              if ((!e.startDate && !e.startDateTime) || (!e.endDate && !e.endDateTime)) return null;

              const gridStyle = getGridColumnsForEvent(
                (e.startDateTime ?? e.startDate)!,
                (e.endDateTime ?? e.endDate)!,
              );

              return (
                <div key={e.id} style={gridStyle}>
                  <GameEventCell
                    game={e.game}
                    gameEvent={e}
                    openMobileDialog={() =>
                      openMobileDialog({ game: e.game, translations, festival: null, gameEvent: e })
                    }
                    translations={translations}
                  />
                </div>
              );
            })}
          </div>
        )}

        {/* Festival events section */}
        {(filter === 'festival' || filter === 'all') && (
          <div className="grid grid-flow-row-dense gap-y-4" style={{ gridTemplateColumns }}>
            {festivals.map((festival) => {
              if ((!festival.startDate && !festival.startDateTime) || (!festival.endDate && !festival.endDateTime))
                return null;

              const gridStyle = getGridColumnsForEvent(
                (festival.startDateTime ?? festival.startDate)!,
                (festival.endDateTime ?? festival.endDate)!,
              );

              return (
                <div key={festival.id} style={gridStyle}>
                  <FestivalEventCell
                    festival={festival}
                    openMobileDialog={() => openMobileDialog({ game: null, translations, festival, gameEvent: null })}
                    translations={translations}
                  />
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export const getScheduleGridWeekId = (week: number) => `schedule-grid-week-${week}`;
