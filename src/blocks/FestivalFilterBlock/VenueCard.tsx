'use client';

import clsx from 'clsx';
import { useState } from 'react';

import { VenueType } from '@/strapi/types/collection/festival';
import { JsonFieldType } from '@/strapi/types/helper';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

interface VenueCardProps {
  venue: VenueType;
  translations: JsonFieldType | null | undefined;
  isSelected: boolean;
  onSelect: () => void;
}

export const VenueCard = ({ venue, translations, isSelected, onSelect }: VenueCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const { isSm } = useScreenType();

  const handleMouseEnter = () => {
    if (!isSm) setIsHovered(true);
  };
  const handleMouseLeave = () => {
    if (!isSm) setIsHovered(false);
  };

  return (
    <div
      className={clsx(
        'flex min-w-[150px] cursor-pointer flex-col justify-between gap-12 rounded-lg p-2.5 shadow-sm md:min-w-0',
        isSelected ? 'bg-dark-default text-white' : 'text-dark-default bg-white',
      )}
      onClick={onSelect}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {venue.type && (
        <div
          className={clsx(
            'h-fit max-h-fit w-min rounded-sm px-1 leading-[110%]',
            isSelected ? 'bg-white-dirty text-dark-default' : 'bg-dark-default text-white',
            {
              'bg-white-dirty! text-dark-default!': venue.type === 'festival' && !isSelected,
            },
          )}
        >
          <span className="font-riforma text-[11px] uppercase">{translations?.[venue.type] ?? venue.type}</span>
        </div>
      )}
      <div className="relative flex h-[40px] flex-col justify-end">
        <span
          className={clsx(
            'font-riforma text-sm font-bold transition-transform duration-200',
            'line-clamp-2',
            isSelected || (isHovered && !isSm) ? '-translate-y-5' : 'translate-y-0',
          )}
        >
          {venue.name}
        </span>
        <div
          className={clsx(
            'absolute left-0 flex w-full pb-[10px] transition-opacity duration-200',
            isSelected || (isHovered && !isSm) ? 'opacity-100' : 'pointer-events-none opacity-0',
          )}
          style={{ top: '60%' }}
        >
          <div className="bg-white-dirty text-dark-default h-min max-h-fit w-max rounded-sm px-1 leading-[110%]">
            <span className="font-riforma text-[10px]">
              {isSelected
                ? (translations?.['selected'] ?? 'Selected')
                : (translations?.['filterActivities'] ?? 'Filter Activities')}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
