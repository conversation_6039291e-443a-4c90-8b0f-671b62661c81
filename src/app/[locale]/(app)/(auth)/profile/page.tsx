import clsx from 'clsx';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { SocialLoginListener } from '@/services/auth/components/SocialLoginListener';
import { getClubTitles } from '@/strapi/api/collection/clubs';
import { getGameTitles } from '@/strapi/api/collection/game';
import { getProfilePageData, getProfilePageSeo } from '@/strapi/api/single/profile';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import { WEBVIEW_HIDE_CLASS } from '@/utils/webviewHideScript';

import { SignoutButton } from '../_components/SignoutButton';
import { UserProfileGate } from '../_components/UserProfileGate';
import { ProfileSections } from './_components/ProfileSections';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const { locale } = await params;
  const seo = await getProfilePageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | Profile',
    description: seo?.metaDescription ?? 'This is Esports World Cup | Profile',
  };
}

export default async function ProfilePage({ params }: PageProps) {
  const { locale } = await params;
  const data = await getProfilePageData(locale);
  const siteConfig = await getSiteConfig(locale);
  const games = await getGameTitles(locale);
  const clubs = await getClubTitles(locale);

  if (!data) {
    return notFound();
  }

  const { title, subtitle, signoutButtonText } = data;
  return (
    <div className="min-h-screen pt-5 pb-20 md:pt-10 xl:pt-20 2xl:py-40">
      <SocialLoginListener />
      <div className="mx-auto w-full max-w-[1396px]">
        <UserProfileGate>
          <div className="flex flex-col gap-8">
            <div
              className={clsx(
                'flex justify-between gap-4 px-4 max-md:flex-col-reverse max-md:items-end lg:px-8',
                WEBVIEW_HIDE_CLASS,
              )}
            >
              <div className="text-dark-default flex flex-col gap-1 max-md:w-full">
                <h1 className="text-h1">{title}</h1>
                <p className="text-paragraph">{subtitle}</p>
              </div>
              <SignoutButton text={signoutButtonText} />
            </div>
            <ProfileSections {...data} clubs={clubs} games={games} translations={siteConfig?.translations} />
          </div>
        </UserProfileGate>
      </div>
    </div>
  );
}
