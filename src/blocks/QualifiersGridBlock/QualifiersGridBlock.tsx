import clsx from 'clsx';

import { QualifiersGridBlockType } from '../../strapi/types/block';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { QualifiersCard } from './QualifiersCard';

export const QualifiersGridBlock = ({ section, qualifiers }: QualifiersGridBlockType) => {
  if (qualifiers.length === 0) {
    return null;
  }

  return (
    <BlockSectionWrapper {...section}>
      <div
        className={clsx(
          'flex gap-2 md:grid md:grid-cols-2 md:gap-4 lg:grid-cols-3 lg:gap-8',
          'max-md:hide-scrollbar max-md:-mx-4 max-md:-mt-3 max-md:-mb-9 max-md:overflow-x-auto max-md:px-4 max-md:pt-3 max-md:pb-9',
        )}
      >
        {qualifiers.map((qualifier) => (
          <QualifiersCard key={qualifier.id} {...qualifier} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
