import { mapBase, VenueMapConfig } from '@/components/InteractiveMap/venues';

type Props = {
  venue: VenueMapConfig;
  isClicked: boolean;
  isHovered: boolean;
  isHoverable: boolean;
  setClickedVenue: (id: string) => void;
  handleHoverEnter: (id: string) => void;
  handleHoverLeave: (id: string) => void;
  toPctX: (val: number) => string;
  toPctY: (val: number) => string;
};

export default function VenueOverlay({
  venue,
  isClicked,
  isHovered,
  isHoverable,
  setClickedVenue,
  handleHoverEnter,
  handleHoverLeave,
  toPctX,
  toPctY,
}: Props) {
  const { overlayBox, interactionBox } = venue;
  const rotationDeg = interactionBox?.rotationDeg ?? 0;
  const widthPct = interactionBox?.widthPercent ?? 100;
  const heightPct = interactionBox?.heightPercent ?? 100;
  const offsetTopPct = interactionBox?.offsetTopPercent ?? 0;
  const offsetLeftPct = interactionBox?.offsetLeftPercent ?? 0;

  const isActive = isHovered || isClicked;

  const handleClick = () => {
    if (!isClicked) setClickedVenue(venue.id);
  };

  const handleMouseEnter = () => {
    if (isHoverable) handleHoverEnter(venue.id);
  };

  const handleMouseLeave = () => {
    if (isHoverable) handleHoverLeave(venue.id);
  };

  return (
    <>
      {isActive && (
        <div
          className="absolute"
          style={{
            top: toPctY(overlayBox.top),
            left: toPctX(overlayBox.left),
            width: toPctX(overlayBox.width),
            height: toPctY(overlayBox.height),
            zIndex: 20,
          }}
        >
          <img
            alt="Venue highlight"
            className="absolute inset-0 h-full w-full"
            src={venue.svg}
            style={{
              transformOrigin: 'center',
            }}
          />
        </div>
      )}
      <div
        className="absolute cursor-pointer"
        style={{
          top: `${((overlayBox.top + (overlayBox.height * offsetTopPct) / 100) / mapBase.height) * 100}%`,
          left: `${((overlayBox.left + (overlayBox.width * offsetLeftPct) / 100) / mapBase.width) * 100}%`,
          width: `${((overlayBox.width * widthPct) / 100 / mapBase.width) * 100}%`,
          height: `${((overlayBox.height * heightPct) / 100 / mapBase.height) * 100}%`,
          transform: `rotate(${rotationDeg}deg)`,
          transformOrigin: 'center',
          willChange: 'transform',
          zIndex: 25,
        }}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      />
    </>
  );
}
