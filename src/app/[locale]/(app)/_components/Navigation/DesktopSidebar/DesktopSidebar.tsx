'use client';

import clsx from 'clsx';
import * as MdIcons from 'react-icons/md';
import { useIsClient } from 'usehooks-ts';

import { WEBVIEW_HIDE_CLASS } from '@/utils/webviewHideScript';

import { NavigationItem, ProfileButton } from '../components';
import { NavigationProps } from '../Navigation';
import { ButtonsSection } from './ButtonsSection';
import { useVisibleItems } from './hooks';
import { LanguageSelector } from './LanguageSelector';
import { MoreMenu } from './MoreMenu';
import { TopLogo } from './TopLogo';

export function DesktopSidebar({ data, siteConfig, disabledLocales, isEmbedded }: NavigationProps) {
  const { visibleItems, hiddenItems, containerRef, navigationRef } = useVisibleItems(data.items, 1);
  const isClient = useIsClient();

  return (
    <nav
      className={clsx('fixed z-90 flex h-full max-h-[100dvh] items-center justify-center', WEBVIEW_HIDE_CLASS)}
      ref={navigationRef}
    >
      <div className="shadow-sidebar w-sidebar-width flex h-full flex-col items-center justify-between bg-white">
        <TopLogo isEmbedded={isEmbedded} logo={data.logo} />
        <div className={clsx('flex w-full flex-col px-1.5', !isClient && 'overflow-hidden')} ref={containerRef}>
          {visibleItems.map((item, i) => (
            <div className={clsx(!isClient && 'invisible')} key={i}>
              <NavigationItem
                icon={item.icon as keyof typeof MdIcons}
                isEmbedded={isEmbedded}
                title={item.title}
                url={item.url}
              />
            </div>
          ))}
          {data.previousTournamentNavItems.length !== 0 && (
            <MoreMenu
              icon="history"
              isEmbedded={isEmbedded}
              items={data.previousTournamentNavItems}
              label={siteConfig?.translations?.history ?? 'History'}
            />
          )}
          {hiddenItems.length !== 0 && (
            <MoreMenu
              icon="more_horiz"
              isEmbedded={isEmbedded}
              items={hiddenItems}
              label={siteConfig?.translations?.more ?? 'More'}
            />
          )}
        </div>
        <div className="relative flex w-full flex-col">
          {!isEmbedded && <LanguageSelector disabledLocales={disabledLocales} />}
          <ProfileButton isEmbedded={isEmbedded} />
          <ButtonsSection buttons={data.buttons} isEmbedded={isEmbedded} />
        </div>
      </div>
    </nav>
  );
}
