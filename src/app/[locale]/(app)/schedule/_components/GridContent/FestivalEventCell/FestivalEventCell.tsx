import { clsx } from 'clsx';
import Image from 'next/image';
import { RefObject, useRef } from 'react';
import { MdPinDrop } from 'react-icons/md';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import { StrapiImage } from '../../../../../../../components/StrapiImage';
import { FestivalType } from '../../../../../../../strapi/types/collection/festival';
import { JsonFieldType } from '../../../../../../../strapi/types/helper';
import { useScreenType } from '../../../../../../../ui/providers/ScreenTypeProvider';
import { getEventStatus } from '../../utils';
import { MobileDialogDataType } from '../GameEventCell/BaseGameEventCell';
import { GameEventStatusIndicatorWrapper } from '../shared/GameEventStatusIndicatorWrapper';
import FestivalCardBgImage from './festival-card-bg.jpeg';
import { FestivalEventDialog } from './FestivalEventDialog';

interface Props {
  festival: FestivalType;
  openMobileDialog: (mobileDialogData: MobileDialogDataType) => void;
  translations: JsonFieldType | null;
}

export const FestivalEventCell = ({ festival, openMobileDialog, translations }: Props) => {
  const { startDateTime, startDate, endDate, endDateTime, title, posterImage, venue, venues } = festival;
  // const locale = useDateLocale();
  const { isSm } = useScreenType();
  const [isDialogOpen, toggleIsDialogOpen, setIsDialogOpen] = useToggle();
  const festivalEventStatus = getEventStatus(startDateTime ?? startDate, endDateTime ?? endDate);

  const containerRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(containerRef as RefObject<HTMLDivElement>, () => setIsDialogOpen(false));
  const venueName = venue || venues?.[0]?.name;

  const toggleIsDialogOpenHandler = () => {
    if (isSm) {
      openMobileDialog({ game: null, festival, gameEvent: null, translations });
    } else {
      toggleIsDialogOpen();
    }
  };

  if (!festival.isShownOnSchedule) {
    return null;
  }

  return (
    <div className="relative h-full min-h-[91px] cursor-pointer" ref={containerRef}>
      <div className="ms-[3px] me-1.5 h-full" onClick={toggleIsDialogOpenHandler}>
        <div className="bg-dark-default relative flex h-full flex-col justify-between gap-1 rounded-sm p-[7px] leading-[1]">
          {posterImage ? (
            <StrapiImage alt="" className="absolute inset-0 size-full rounded-sm object-cover" image={posterImage} />
          ) : (
            <Image
              alt=""
              className="absolute inset-0 size-full rounded-sm object-cover"
              height={100}
              quality={50}
              src={FestivalCardBgImage}
              width={100}
            />
          )}
          <div className="bg-dark-default/70 absolute inset-0 size-full rounded-sm" />
          {venueName && (
            <div className="z-[1] flex items-center gap-0.5">
              <MdPinDrop className="align-middle text-xs text-white" />
              <span className="font-base text-[10px] font-bold text-white">{venueName}</span>
            </div>
          )}
          <p className="font-base relative mt-auto mb-1 line-clamp-3 text-[13px] leading-[130%] font-extrabold text-white">
            {title}
          </p>
          {festivalEventStatus && (
            <div className="flex items-center justify-start">
              <GameEventStatusIndicatorWrapper
                cardType="Festival"
                className="z-[1]"
                eventStatus={festivalEventStatus}
                label={translations?.[festivalEventStatus] ? translations?.[festivalEventStatus] : festivalEventStatus}
              />
            </div>
          )}
        </div>
      </div>
      <div
        className={clsx(
          'fixed end-8 bottom-32 z-10 transition-opacity lg:bottom-8',
          isDialogOpen ? 'opacity-100' : 'opacity-0',
        )}
      >
        {isDialogOpen && !isSm && (
          <FestivalEventDialog festival={festival} translations={translations} onClose={() => setIsDialogOpen(false)} />
        )}
      </div>
    </div>
  );
};
