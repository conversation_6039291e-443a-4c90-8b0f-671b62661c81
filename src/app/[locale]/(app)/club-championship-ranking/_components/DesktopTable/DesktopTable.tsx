import { AnimatePresence, motion } from 'framer-motion';

import { JsonFieldType } from '../../../../../../strapi/types/helper';
import { useSimulator } from '../../_context/SimulatorContext';
import { ClubData, SimulatedClubData } from '../../_utils/types';
import { DesktopTableRow } from './DesktopTableRow';

export const DesktopTable = ({
  rankings,
  translations,
}: {
  rankings: ClubData[] | SimulatedClubData[];
  translations?: JsonFieldType | null;
}) => {
  const { isSimulatorMode } = useSimulator();
  return (
    <div>
      <div className="bg-dark-default font-primary flex gap-1.5 py-2 ps-6 pe-16 text-[12px] leading-[100%] font-bold text-white uppercase">
        <span className="w-[44px] text-center">{translations?.rank ?? 'rank'}</span>
        <span className="w-[44px] text-center">{translations?.rankDiff ?? '+/-'}</span>
        <span className="h-0 w-[39px] opacity-0">hidden element</span>
        <span className="min-w-[205px] grow">{translations?.club ?? 'club'}</span>
        <span className="w-[110px] text-center">{translations?.ccPoints ?? 'CC POINTS'}</span>
        {isSimulatorMode ? (
          <span className="w-[166px] text-center">{translations?.simulatedPoints ?? 'simulated points'}</span>
        ) : (
          <span className="w-[166px] text-center">{translations?.medals ?? 'MEDALS'}</span>
        )}
      </div>
      <AnimatePresence>
        <div className="divide-gray-easy divide-y">
          {rankings.map((club) => (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              initial={{ opacity: 0, y: 20 }}
              key={club.club.id}
              layout
              transition={{
                type: 'spring',
                stiffness: 500,
                damping: 50,
                mass: 1,
              }}
            >
              <DesktopTableRow club={club} translations={translations} />
            </motion.div>
          ))}
        </div>
      </AnimatePresence>
    </div>
  );
};
