import { NewsBlockType } from '@/strapi/types/block';

import { NewsThumbnailCard } from '../NewsThumbnailCard';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const NewsCardsBlock = ({ featuredArticles, section }: NewsBlockType) => {
  if (featuredArticles.length === 0) {
    return null;
  }

  const articles = featuredArticles.slice(0, 4);
  return (
    <BlockSectionWrapper {...section}>
      <div className="flex items-start gap-4 overflow-x-auto pb-5 max-lg:-mx-4 max-lg:px-4 xl:gap-5 2xl:gap-[30px]">
        {articles.map((a) => (
          <NewsThumbnailCard key={a.id} {...a} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
