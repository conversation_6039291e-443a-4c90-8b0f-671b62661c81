'use client';

import { IconType } from 'react-icons';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>rd, FaG<PERSON>gle, FaTwitch } from 'react-icons/fa6';

import { SocialProvider } from '@/services/auth/types';
import { logButtonClickedEvent } from '@/services/braze';

interface Props {
  onProviderClick: (provider: SocialProvider) => Promise<void>;
}

export const SocialLoginSection = ({ onProviderClick }: Props) => {
  return (
    <div className="flex flex-wrap justify-center gap-1 md:gap-1.5 xl:gap-[11px]">
      {Object.keys(socialProviderIconMap).map((p) => (
        <SocialLoginButton
          key={p}
          provider={p as SocialProvider}
          onClick={() => onProviderClick(p as SocialProvider)}
        />
      ))}
    </div>
  );
};

export const socialProviderIconMap: Record<SocialProvider, IconType> = {
  [SocialProvider.GOOGLE]: <PERSON><PERSON><PERSON>oo<PERSON>,
  [SocialProvider.APPLE]: FaApple,
  [SocialProvider.TWITCH]: FaTwitch,
  [SocialProvider.DISCORD]: FaDiscord,
};

const SocialLoginButton = ({ provider, onClick }: { provider: SocialProvider; onClick: () => void }) => {
  const Icon = socialProviderIconMap[provider];

  return (
    <button
      className="bg-dark-default flex size-[41px] cursor-pointer items-center justify-center rounded-lg text-white md:size-[54px]"
      onClick={() => {
        onClick();
        logButtonClickedEvent({ location: 'Social Login Section', button_name: `Login With ${provider}` });
      }}
    >
      <Icon className="size-5" />
    </button>
  );
};
