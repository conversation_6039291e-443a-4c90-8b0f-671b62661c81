import { GameType } from '@/strapi/types/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';

import { CompetitionCard } from './CompetitionCard';
import { fetchCompletedGamesData } from './fetchCompletedGamesData';

interface Props {
  games: GameType[];
  isCompletedBadgeVisible?: boolean;
  translations: JsonFieldType;
}

export const CompetitionsGrid = async ({ games, isCompletedBadgeVisible = true, translations }: Props) => {
  const completedGamesDataMap = await fetchCompletedGamesData(games);

  const sortedGames = games.toSorted((a, b) =>
    sortByCompletedAndTournamentDates(
      { ...a, isCompleted: !!completedGamesDataMap[a.documentId]?.isCompleted },
      { ...b, isCompleted: !!completedGamesDataMap[b.documentId]?.isCompleted },
    ),
  );

  return (
    <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 xl:grid-cols-4">
      {sortedGames.map((game) => {
        const completedData = completedGamesDataMap[game.documentId];
        return (
          <CompetitionCard
            game={game}
            key={game.id}
            translations={translations}
            {...completedData}
            isCompletedBadgeVisible={isCompletedBadgeVisible}
          />
        );
      })}
    </div>
  );
};

type GameWithCompletedStatus = GameType & { isCompleted: boolean };

function sortByCompletedAndTournamentDates(a: GameWithCompletedStatus, b: GameWithCompletedStatus) {
  if (!a.isCompleted && b.isCompleted) {
    return -1;
  }
  if (a.isCompleted && !b.isCompleted) {
    return 1;
  }

  const aTournamentStart = a.tournamentStart ?? 0;
  const aTournamentEnd = a.tournamentEnd ?? 0;
  const bTournamentStart = b.tournamentStart ?? 0;
  const bTournamentEnd = b.tournamentEnd ?? 0;

  if (!a.isCompleted && !b.isCompleted) {
    return aTournamentStart < bTournamentStart ? -1 : bTournamentStart < aTournamentStart ? 1 : 0;
  }

  return aTournamentEnd < bTournamentEnd ? -1 : bTournamentEnd < aTournamentEnd ? 1 : 0;
}
