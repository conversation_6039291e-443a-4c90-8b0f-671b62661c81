'use client';

import clsx from 'clsx';
import { useRef, useState } from 'react';

import { ScheduleGridLegend } from '@/app/[locale]/(app)/schedule/_components/GridContent/ScheduleGridLegend/ScheduleGridLegend';
import { TournamentEventDialog } from '@/app/[locale]/(app)/schedule/_components/GridContent/TournamentEventDialog/TournamentEventDialog';
import { useLockBodyScroll } from '@/app/[locale]/(app)/schedule/hooks/useLockBodyScroll';
import { useScrollToToday } from '@/app/[locale]/(app)/schedule/hooks/useScrollToToday';
import { useDragToScroll } from '@/components/ScrollWithFadeWrapper/useDragToScroll';
import { GameEventType } from '@/strapi/types/collection/gameEvent';
import { JsonFieldType } from '@/strapi/types/helper';
import { WeekTicketsLinkType } from '@/strapi/types/helper/weekTickets';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { ToggleGroup } from '../../../../../components/ToggleGroup';
import { FestivalType } from '../../../../../strapi/types/collection/festival';
import { GridContent } from './GridContent';
import { FestivalEventDialog } from './GridContent/FestivalEventCell/FestivalEventDialog';
import { MobileDialogDataType } from './GridContent/GameEventCell/BaseGameEventCell';
import { generateWeekDatesBetween, getScheduleGridColumnWidth } from './utils';
import { WeekLabels } from './WeekLabels';

export type ScheduleFilter = 'all' | 'games' | 'festival';

interface Props {
  tournamentStartDate: string;
  tournamentEndDate: string;
  gameEvents: GameEventType[];
  festivals: FestivalType[];
  weekTickets: WeekTicketsLinkType[];
  translations: JsonFieldType | null;
  title: string | null;
  subtitle: string | null;
  timezoneInfo: string | null;
  gridColumnWidth: number | null;
}

export const ScheduleGrid = ({
  tournamentStartDate,
  tournamentEndDate,
  gameEvents,
  weekTickets,
  translations,
  title,
  subtitle,
  timezoneInfo,
  festivals,
  gridColumnWidth,
}: Props) => {
  const [filter, setFilter] = useState<ScheduleFilter>('all');
  const dayColumnWidth = getScheduleGridColumnWidth(gridColumnWidth);

  const [isMobileDialogOpen, setIsMobileDialogOpen] = useState(false);
  const [selectedMobileData, setSelectedMobileData] = useState<MobileDialogDataType | null>(null);
  const { isSm } = useScreenType();

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  useDragToScroll(scrollContainerRef);

  const weekDates = generateWeekDatesBetween(tournamentStartDate, tournamentEndDate, weekTickets.length);

  const allDates = weekDates.flatMap((w) => w.dates);

  useScrollToToday({
    allDates,
    scrollContainerRef,
    dayWidth: dayColumnWidth,
  });

  const openMobileDialog = (mobileData: MobileDialogDataType) => {
    setSelectedMobileData(mobileData);
    setIsMobileDialogOpen(true);
  };

  const closeMobileDialog = () => {
    setSelectedMobileData(null);
    setIsMobileDialogOpen(false);
  };
  useLockBodyScroll(isMobileDialogOpen && isSm);

  return (
    <>
      <div
        className={clsx(
          'flex grow flex-col gap-4 overflow-hidden py-15 lg:gap-8',
          isMobileDialogOpen && isSm && 'opacity-30',
        )}
      >
        <div className="flex justify-between gap-4 max-lg:flex-col">
          <div className="text-dark-default flex flex-col gap-1 px-4 lg:px-8">
            {title && <p className="text-h1">{title}</p>}
            {subtitle && <p className="text-paragraph">{subtitle}</p>}
            {timezoneInfo && (
              <p className="font-primary text-gray-dark text-[10px] font-bold uppercase">{timezoneInfo}</p>
            )}
            <ScheduleGridLegend translations={translations} />
          </div>

          <div className="mt-auto overflow-x-auto px-4 lg:px-8">
            <ToggleGroup
              filters={[
                { value: 'all', label: translations?.['scheduleFilter.showAll'] ?? 'show all' },
                { value: 'games', label: translations?.['scheduleFilter.games'] ?? 'games' },
                { value: 'festival', label: translations?.['scheduleFilter.festivals'] ?? 'festivals' },
              ]}
              selectedFilter={filter}
              onFilterSelect={(f) => setFilter(f as ScheduleFilter)}
            />
          </div>
        </div>
        <div className="flex grow flex-col">
          <div
            className="hide-scrollbar flex grow flex-col gap-5 overflow-x-auto overflow-y-visible px-4 lg:px-8"
            ref={scrollContainerRef}
          >
            <div className="relative w-max grow">
              <WeekLabels
                dayColumnWidth={dayColumnWidth}
                translations={translations}
                weekDates={weekDates}
                weekTickets={weekTickets}
              />

              <GridContent
                dayColumnWidth={dayColumnWidth}
                festivals={festivals}
                filter={filter}
                gameEvents={gameEvents}
                openMobileDialog={openMobileDialog}
                translations={translations}
                weekDates={weekDates}
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        {selectedMobileData && isMobileDialogOpen && isSm && (
          <div className="fixed bottom-0 z-10 mx-4 flex h-full items-center">
            <div className="relative">
              {selectedMobileData.game && selectedMobileData.gameEvent && (
                <TournamentEventDialog
                  game={selectedMobileData.game}
                  gameEvent={selectedMobileData.gameEvent}
                  translations={selectedMobileData.translations}
                  onCloseAction={closeMobileDialog}
                />
              )}
              {selectedMobileData.festival && (
                <FestivalEventDialog
                  festival={selectedMobileData.festival}
                  translations={selectedMobileData.translations}
                  onClose={closeMobileDialog}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};
