'use client';

import clsx from 'clsx';
import { useState } from 'react';

import { WEBVIEW_HIDE_CLASS } from '@/utils/webviewHideScript';

import { NavigationItem } from '../components';
import { NavigationProps } from '../Navigation';
import { MoreMenu } from './MoreMenu';

export function MobileBottomNav({ data, siteConfig, disabledLocales, isEmbedded }: NavigationProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const visibleItems = data.items?.filter((item) => item.appearsOnMobileNavigation);
  return (
    <>
      <div
        className={clsx(
          'shadow-mobile-navigation fixed bottom-0 z-90 min-h-18 w-full bg-white/80 px-2.5 backdrop-blur-[30px]',
          WEBVIEW_HIDE_CLASS,
        )}
        style={{ paddingBottom: 'env(safe-area-inset-bottom)' }}
      >
        <div className="grid h-full w-full grid-cols-[repeat(auto-fit,minmax(0,1fr))] gap-2.5">
          {visibleItems?.map((item, i) => {
            return (
              <NavigationItem
                icon={item.icon}
                isEmbedded={isEmbedded}
                key={i}
                title={item.title}
                url={item.url}
                onClick={() => setIsMenuOpen(false)}
              />
            );
          })}
          <MoreMenu
            data={data}
            disabledLocales={disabledLocales}
            isEmbedded={isEmbedded}
            isOpen={isMenuOpen}
            translations={siteConfig?.translations ?? {}}
            onClose={() => setIsMenuOpen(false)}
            onOpen={() => setIsMenuOpen(true)}
          />
        </div>
      </div>
    </>
  );
}
