'use client';

import { useEffect, useRef, useState } from 'react';

export function useVenueState() {
  const [clickedVenue, setClickedVenue] = useState<string | null>(null);
  const [hoveredVenue, setHoveredVenue] = useState<string | null>(null);
  const [isHoverable, setIsHoverable] = useState(false);

  const hoverTimeout = useRef<NodeJS.Timeout | null>(null);
  const activeHovers = useRef(new Set<string>());

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsHoverable(window.matchMedia('(hover: hover)').matches);
    }
  }, []);

  const handleHoverEnter = (venueId: string) => {
    if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
    activeHovers.current.add(venueId);
    setHoveredVenue(venueId);
  };

  const handleHoverLeave = (venueId: string) => {
    activeHovers.current.delete(venueId);
    if (hoverTimeout.current) clearTimeout(hoverTimeout.current);
    hoverTimeout.current = setTimeout(() => {
      if (activeHovers.current.size === 0) {
        setHoveredVenue(null);
      }
    }, 100);
  };

  return {
    clickedVenue,
    setClickedVenue,
    hoveredVenue,
    setHoveredVenue,
    handleHoverEnter,
    handleHoverLeave,
    isHoverable,
    setIsHoverable,
  };
}
