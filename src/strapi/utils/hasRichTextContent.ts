import { RichTextContentType } from '../types/helper';

/**
 * Checks if a RichText content has any non-empty content
 * @param content The RichText content to check
 * @returns true if the content has any non-empty blocks, false otherwise
 */
export const hasRichTextContent = (content: RichTextContentType | null): boolean => {
  if (!content) return false;

  // Check if it's an empty array
  if (Array.isArray(content) && content.length === 0) return false;

  // Check if any blocks have content
  return content.some((block) => {
    // Check for text blocks
    if (block.type === 'paragraph' || block.type === 'heading') {
      return (
        block.children?.some(
          (child) =>
            // Check if the child has a text property that's not empty
            typeof (child as any).text === 'string' && (child as any).text.trim().length > 0,
        ) ?? false
      );
    }

    // For other block types (lists, quotes, etc.), consider them non-empty
    return true;
  });
};
