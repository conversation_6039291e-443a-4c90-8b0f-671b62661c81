import { CompetitionsGridBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { CompetitionsGrid } from './CompetitionsGrid';

export const CompetitionsGridBlock = ({ section, games, translations }: CompetitionsGridBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <CompetitionsGrid games={games} isCompletedBadgeVisible={false} translations={translations ?? {}} />
    </BlockSectionWrapper>
  );
};
