import { StreamType } from '@/strapi/types/collection/stream';
import { JsonFieldType } from '@/strapi/types/helper';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { GameStream } from '../types/GameStream';
import { StreamHeroMatch } from '../types/StreamHeroMatch';
import { TimerIntermission } from './TimerIntermission';
import { VodIntermission } from './VodIntermission';

export interface IntermissionProps {
  stream: GameStream | StreamType;
  isLoading?: boolean;
  matches: StreamHeroMatch[];
  translations: JsonFieldType;
  apiTranslations: JsonFieldType;
}

export const Intermission = ({ stream, matches, ...rest }: IntermissionProps) => {
  if (stream.intermissionType === null || stream.intermissionType === 'timer') {
    return <TimerIntermission stream={stream} {...rest} />;
  }

  const upcomingMatches = matches.filter((m) => m.status === LocalMatchStatus.UPCOMING).slice(0, 4);
  return <VodIntermission matches={upcomingMatches} stream={stream} {...rest} />;
};
