import { Metadata } from 'next';
import Image from 'next/image';
import { notFound } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getChangePasswordPageData } from '@/strapi/api/single/authPages';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import BackgroundImage from '@/ui/assets/images/abstract-background-3.png';

import { AuthModal } from '../_components/AuthModal';
import { UserProfileGate } from '../_components/UserProfileGate';
import { UpdatePasswordSection } from './_components';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const { locale } = await params;
  const data = await getChangePasswordPageData(locale);

  return {
    title: data?.seo?.metaTitle ?? 'Esports World Cup | Change Password',
    description: data?.seo?.metaDescription ?? 'This is Esports World Cup | Change Password',
  };
}

export default async function ChangePasswordPage({ params }: PageProps) {
  const { locale } = await params;
  const data = await getChangePasswordPageData(locale);
  const siteConfig = await getSiteConfig(locale);

  if (!data) {
    return notFound();
  }

  const { title, subtitle, footerText } = data;
  return (
    <div className="relative flex min-h-screen w-full items-center justify-center">
      <Image alt="" className="absolute inset-0 h-full w-full object-cover" quality={100} src={BackgroundImage} />
      <UserProfileGate>
        <AuthModal footer={footerText} subtitle={subtitle} title={title}>
          <UpdatePasswordSection {...data} translations={siteConfig?.translations} />
        </AuthModal>
      </UserProfileGate>
    </div>
  );
}
