import { CompetitionCard } from '@/components/CompetitionCard';
import { SimpleRichTextContent } from '@/components/SimpleRichTextContent';
import { constructImageUrl } from '@/services/graphql/utils';
import { ClubChampionOverviewBlockType } from '@/strapi/types/block';
import { Button } from '@/ui/components/Button';

import { fetchClubChampionshipWinner } from '../shared/api/fetchClubChampionshipWinner';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { ClubChampionshipTable } from './ClubChampionshipTable';
import { HeroSection } from './HeroSection';
import { TitleWithStatsSection } from './TitleWithStatsSection';

export async function ClubChampionOverviewBlock(props: ClubChampionOverviewBlockType) {
  if (!props.clubId) {
    return null;
  }

  const { winner, competitions } = await fetchClubChampionshipWinner(props.games, props.clubId);
  if (!winner) {
    return null;
  }

  if (props.isTabularView) {
    return <ClubChampionshipTable {...props} competitions={competitions} winner={winner} />;
  }

  const { heroText, heroImage, moneyWon, pointsWon, clubDescription, button, section, translations } = props;
  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-col items-center gap-4 md:gap-8">
        <HeroSection
          heroImage={heroImage}
          logoUrl={constructImageUrl(winner?.images, 'logo_transparent')}
          text={heroText}
        />
        <TitleWithStatsSection
          medals={winner.medals}
          points={pointsWon ?? winner.prizes.XTS ?? 0}
          translations={translations ?? {}}
          usd={moneyWon ?? winner.prizes.USD ?? 0}
          winnerName={winner.club.name ?? translations?.tbd?.toUpperCase() ?? 'TBD'}
        />
        {clubDescription && (
          <SimpleRichTextContent
            content={clubDescription}
            paragraphClassname="text-paragraph text-dark-default text-center"
          />
        )}
        <div className="flex w-full flex-wrap justify-center gap-[13px]">
          {competitions.map((c) => (
            <CompetitionCard
              {...c}
              arePointsVisible={false}
              clubId={winner.club.id}
              isSimulation={false}
              key={c.competitionName}
              simulatedPoints={null}
            />
          ))}
        </div>
        {button && (
          <div className="max-md:w-full">
            <Button
              brazeEventProperties={{
                button_name: 'View All Rankings link',
                location: 'Club Champion Overview Block',
              }}
              isFullWidth
              {...button}
            />
          </div>
        )}
      </div>
    </BlockSectionWrapper>
  );
}
