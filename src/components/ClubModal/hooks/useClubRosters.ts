import uniqBy from 'lodash/uniqBy';
import { useMemo } from 'react';

import { Roster } from '@/components/RosterCard/types';
import { Tournament, TournamentStatus } from '@/services/graphql/types/tournament';
import { GameWithCompetitions } from '@/strapi/api/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';

/**
 * Custom React hook to generate club rosters for a given club across multiple tournaments and games.
 *
 * @param clubId - The ID of the club for which to generate rosters. If null, returns an empty array.
 * @param tournaments - Array of Tournament objects where club is playing.
 * @param games - Array of GameType objects representing the games to process.
 * @returns An array of Roster objects, each containing the club's team and associated game information.
 *
 * @remarks
 * - Relies on getting exact tournaments and games where club is participating.
 * - Uses `useMemo` to optimize performance and avoid unnecessary recalculations.
 * - Each roster includes team details, player list, and game metadata.
 * - If the club does not participate in a game, a placeholder team is returned.
 */
export function useClubRosters(
  clubId: string | null,
  tournaments: Tournament[],
  games: GameWithCompetitions[],
  translations: JsonFieldType,
) {
  return useMemo(() => {
    const rosters: Roster[] = [];

    if (!clubId) {
      return rosters;
    }

    for (const game of games) {
      const gameTournaments = tournaments.filter((t) => game.competitionSlugs.some((s) => s.competitionId === t.id));
      const isGameFinished = gameTournaments.every((t) => t.status === TournamentStatus.FINISHED);

      const teamsDataList = getTeamsData(gameTournaments, clubId, translations);
      for (const { currentClubTeam, teamsCount } of teamsDataList) {
        const roster: Roster = {
          team: currentClubTeam,
          game: {
            id: game.documentId,
            name: game.title,
            slug: game.slug,
            logoLight: game.schedulePopupLogo,
            logoDark: game.logoDark,
            teamsCount,
            startDate: game.tournamentStart,
            endDate: game.tournamentEnd,
            isFinished: isGameFinished,
          },
        };
        rosters.push(roster);
      }
    }

    rosters.sort((a) => (a.game.isFinished ? -1 : 1));
    return rosters;
  }, [clubId, games, tournaments, translations]);
}

function getTeamsData(tournaments: Tournament[], clubId: string, translations: JsonFieldType) {
  const tournamentContestants = tournaments.flatMap((t) => t.contestants ?? []);
  const uniqueContestants = uniqBy(tournamentContestants, (c) => c.team?.id);

  const currentClubContestants = uniqueContestants.filter((c) => c.team?.club.id === clubId);
  const teamsDataList = [];
  for (const contestant of currentClubContestants) {
    // rank for team is present only in one tournament (or null) - filter again to find rank if possible
    const currentClubContestantWithRank = tournamentContestants.find(
      (c) => c.team?.id === contestant.team?.id && c.rank,
    );
    const players =
      contestant?.members?.map((m) => ({
        name: m.player.name ?? 'tbd',
        isCoach: m.role?.name === 'Coach',
      })) ?? [];

    const teamsData = {
      teamsCount: uniqueContestants.length,
      currentClubTeam: {
        id: contestant?.team?.id ?? '',
        name: contestant?.team?.name ?? translations.tbd?.toUpperCase() ?? 'TBD',
        rank: currentClubContestantWithRank?.rank ?? null,
        players: players.length !== 0 ? players : getPlaceholderPlayers(translations),
      },
    };
    teamsDataList.push(teamsData);
  }

  return teamsDataList;
}

function getPlaceholderPlayers(translations: JsonFieldType) {
  return [...Array.from({ length: 4 })].map((_, i) => ({
    name: translations.tbd?.toUpperCase() ?? 'TBD',
    isCoach: i === 3,
  }));
}
