import clsx from 'clsx';
import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';
import { TicketCardType } from '@/strapi/types/helper/ticketCard';
import { MediaType } from '@/strapi/types/media';
import GameIconPlaceholderImage from '@/ui/assets/images/game-icon-placeholder.png';
import { Button } from '@/ui/components/Button';

export const TicketCard = (props: TicketCardType) => {
  return <WeeklyTicketCard {...props} />;
};

export const WeeklyTicketCard = ({ title, dateRange, description, image, button, games }: TicketCardType) => {
  return (
    <div className="font-primary flex w-[249px] flex-none flex-col justify-between rounded-2xl bg-white p-4 shadow-md select-none lg:rounded-3xl">
      <div>
        {image && (
          <StrapiImage
            className="pointer-events-none mb-3 w-full rounded-lg object-cover lg:rounded-2xl"
            image={image}
          />
        )}
        {dateRange && <p className="mb-1 h-[11px] w-full text-center text-[11px] font-bold">{dateRange}</p>}
        {title && (
          <p className="text-h5 mb-3 line-clamp-2 h-[50px] w-[216px] py-[2px] text-center text-ellipsis">{title}</p>
        )}
        {description && <p className="text-gray-dark w-full text-center text-[9px] font-bold">{description}</p>}
        {games && games.length > 0 && (
          <div className="mt-2 flex flex-col items-center justify-center gap-1">
            {games.map(({ id, logoDark }) => (
              <GameLogo key={id} logo={logoDark} />
            ))}
          </div>
        )}
      </div>

      {button && (
        <div className="border-gray mt-3 flex items-center justify-center border-t-1 pt-3">
          <Button
            {...button}
            brazeEventProperties={{
              button_name: `Primary Button (${button.text})`,
              location: `Ticket Sales Block - Weekly Ticket Card (${title})`,
            }}
            isFullWidth
          />
        </div>
      )}
    </div>
  );
};

const GameLogo = ({ logo }: { logo?: MediaType | null }) => (
  <div className={clsx('h-[32px]', logo ? 'w-[107px]' : 'w-[32px]')}>
    {logo ? (
      <StrapiImage className="h-full w-full object-contain" image={logo} />
    ) : (
      <Image alt="Game logo placeholder" height={32} src={GameIconPlaceholderImage} width={32} />
    )}
  </div>
);
