import { useSubscription } from '@apollo/client';

import { TOURNAMENTS_QUERY, TOURNAMENTS_SUBSCRIPTION_QUERY } from '../../queries';
import { SubscriptionQueryData, TournamentContestantSubscriptionData } from '../../types/subscription';
import { TournamentsQueryResponse } from '../../types/tournament';
import { isTournamentContestantSubscriptionData } from '../utils';
import { SubscriptionParams } from './matchSeries';

export function useTournamentsSubscription({ enabled, tournamentIds }: SubscriptionParams) {
  useSubscription<SubscriptionQueryData>(TOURNAMENTS_SUBSCRIPTION_QUERY, {
    skip: !enabled,
    variables: { tournamentIds },
    onData: ({ data, client }) => {
      const subData = data.data?.fixtureDataEvents.data;
      if (!subData) {
        return;
      }

      const prev = client.readQuery<TournamentsQueryResponse>({
        query: TOURNAMENTS_QUERY,
        variables: { tournamentIds },
      });

      if (!prev) {
        return;
      }

      const next = structuredClone(prev);
      switch (true) {
        case isTournamentContestantSubscriptionData(subData):
          patchTournamentContestantData(next, subData);
          break;
        default:
          return;
      }

      client.writeQuery({ query: TOURNAMENTS_QUERY, variables: { tournamentIds }, data: next });
    },
  });
}

function patchTournamentContestantData(prev: TournamentsQueryResponse, subData: TournamentContestantSubscriptionData) {
  for (const t of prev.tournaments.result ?? []) {
    const contestant = t.contestants?.find((c) => c.id === subData.id);
    if (contestant) {
      contestant.rank = subData.rank;
      return;
    }
  }
}
