interface Props {
  url: string | null;
  autoplay?: boolean | null;
  loop?: boolean | null;
  muted?: boolean | null;
  controls?: boolean | null;
  fallback?: React.ReactNode;
}

export const VideoEmbed = (props: Props) => {
  const { fallback, url, ...rest } = props;
  if (!url) {
    return fallback ?? null;
  }

  const embedUrl = constructEmbedUrl({ url, ...rest });
  return (
    <iframe
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
      allowFullScreen
      className="size-full"
      frameBorder="0"
      referrerPolicy="strict-origin-when-cross-origin"
      src={embedUrl}
      title="YouTube video player"
    />
  );
};

const EMBED_PATH = 'embed';

function constructEmbedUrl({ url, autoplay, controls = true, loop, muted }: Props) {
  if (!url) {
    return '';
  }
  // faceit embed url
  if (url.includes('faceit.com')) {
    return url;
  }

  const isEmbeddedVideo = url.includes(EMBED_PATH);

  let embedUrl = url;
  if (!isEmbeddedVideo) {
    embedUrl = embedUrl.replace('live', EMBED_PATH);
  }

  if (autoplay) {
    embedUrl += `&autoplay=${autoplay ? 1 : 0}&mute=1`;
  } else {
    embedUrl += `&mute=${muted ? 1 : 0}`;
  }

  if (loop) {
    const videoId = embedUrl.match(/embed\/([^?]+)/)?.[1];
    if (videoId) {
      embedUrl += `&loop=1&playlist=${videoId}`;
    }
  }

  embedUrl += `&controls=${controls ? 1 : 0}`;

  return embedUrl;
}
