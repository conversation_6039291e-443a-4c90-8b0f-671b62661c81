import { Locale } from '@/hooks/i18n/const';
import { PageType } from '@/strapi/types/collection/page';
import { PreviousTournamentPageType } from '@/strapi/types/collection/previousTournament';
import { HERO_CLUB_CHAMPION_OVERVIEW_BLOCK_KEY, HeroClubChampionOverviewType } from '@/strapi/types/hero';
import { SeoType } from '@/strapi/types/shared/seo';

import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils/block';
import { assembleHeroWithAdditionalData } from '../utils/hero';

export async function getPreviousTournamentPageData(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('previous-tournaments').find({
      filters: { slug: { $eq: slug } },
      populate: {
        backgroundMedia: { populate: '*' },
        hero: { on: { [HERO_CLUB_CHAMPION_OVERVIEW_BLOCK_KEY]: { populate: { background: { populate: '*' } } } } },
        blocks: getDynamicZoneBlocks(),
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as PreviousTournamentPageType;
    if (!data) {
      return null;
    }

    await assembleBlocksWithAdditionalData(data.blocks, locale);

    data.hero = pageData.data[0].hero[0] as HeroClubChampionOverviewType | null;
    if (data.hero) {
      await assembleHeroWithAdditionalData(data.hero, locale);
    }

    return data;
  } catch (error) {
    console.error('Previous Tournament Page data not fetched!', error);
    throw new Error('Previous Tournament Page data not fetched!');
  }
}

export async function getPreviousTournamentPageSeo(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('previous-tournaments').find({
      filters: { slug: { $eq: slug } },
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as PageType;
    if (!data) {
      return null;
    }
    return data.seo as SeoType;
  } catch (error) {
    console.error('Previous Tournament Page SEO not fetched!', error);
    return null;
  }
}
