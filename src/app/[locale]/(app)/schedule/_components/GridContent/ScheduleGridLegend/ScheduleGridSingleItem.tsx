import clsx from 'clsx';
import { ReactNode } from 'react';

interface Props {
  label: string;
  color?: string;
  icon?: ReactNode;
}

export const ScheduleGridSingleItem = ({ label, color, icon }: Props) => {
  return (
    <div className="bg-gray-easy font-base bg-gray-easy inline-flex items-center gap-1 rounded-sm px-2 text-xs font-bold text-black">
      {icon ? (
        <span className="text-base">{icon}</span>
      ) : (
        <span className={clsx('size-3 shrink-0 rounded-full', color)} />
      )}
      {label}
    </div>
  );
};
