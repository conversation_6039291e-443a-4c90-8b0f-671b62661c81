import { MatchSeriesQueryResponse } from '../types/matchSeries';
import {
  MatchContestantSubscriptionData,
  MatchSeriesContestantSubscriptionData,
  MatchSeriesSubscriptionData,
  MatchSubscriptionData,
  SubscriptionData,
  TournamentContestantSubscriptionData,
} from '../types/subscription';
import { TournamentsQueryResponse } from '../types/tournament';

export const isMatchSeriesQueryData = (
  data: MatchSeriesQueryResponse | TournamentsQueryResponse,
): data is MatchSeriesQueryResponse => !!(data as any).matchSeries;

export const isMatchSeriesSubscriptionData = (data: SubscriptionData): data is MatchSeriesSubscriptionData =>
  data.__typename === 'MatchSeries';

export const isMatchSubscriptionData = (data: SubscriptionData): data is MatchSubscriptionData =>
  data.__typename === 'Match';

export const isMatchSeriesContestantSubscriptionData = (
  data: SubscriptionData,
): data is MatchSeriesContestantSubscriptionData => data.__typename === 'MatchSeriesContestant';

export const isMatchContestantSubscriptionData = (data: SubscriptionData): data is MatchContestantSubscriptionData =>
  data.__typename === 'MatchContestant';

export const isTournamentContestantSubscriptionData = (
  data: SubscriptionData,
): data is TournamentContestantSubscriptionData => data.__typename === 'TournamentContestant';
