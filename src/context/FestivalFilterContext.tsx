'use client';

import React, { createContext, useContext, useState } from 'react';

type FestivalFilterContextType = {
  selectedVenueId: number | null;
  setSelectedVenueId: (id: number | null) => void;
  selectedDate: string | null;
  setSelectedDate: (date: string | null) => void;
};

const FestivalFilterContext = createContext<FestivalFilterContextType | undefined>(undefined);

export const useFestivalFilter = () => {
  const ctx = useContext(FestivalFilterContext);
  if (!ctx) throw new Error('useFestivalFilter must be used within FestivalFilterProvider');
  return ctx;
};

export const FestivalFilterProvider: React.FC<{ children: React.ReactNode; initialDate?: string | null }> = ({
  children,
  initialDate,
}) => {
  const [selectedVenueId, setSelectedVenueId] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<string | null>(initialDate ?? null);

  return (
    <FestivalFilterContext.Provider value={{ selectedVenueId, setSelectedVenueId, selectedDate, setSelectedDate }}>
      {children}
    </FestivalFilterContext.Provider>
  );
};
