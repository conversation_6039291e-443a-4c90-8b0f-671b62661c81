import { JsonFieldType } from '@/strapi/types/helper';

import { <PERSON>Winner } from '../../../fetchCompletedGamesData';
import { WinnerRow } from './WinnerRow';

interface WinnersTableProps {
  winners: Record<number, GameWinner[]>;
  translations: JsonFieldType;
}

export const WinnersTable = ({ winners, translations }: WinnersTableProps) => {
  return (
    <div className="flex w-full flex-col items-center gap-[9px] px-2 md:px-6">
      <p className="font-base text-xs leading-none font-extrabold text-white capitalize md:text-lg">
        {translations.winners ?? 'winners'}
      </p>
      <div className="bg-white-dirty w-full rounded-sm md:rounded-lg md:px-2 md:py-1">
        <table className="w-full">
          <thead className="max-md:hidden">
            <tr className="text-gold-primary font-primary text-[8px] leading-tight font-bold">
              <th className="w-2.5 ps-1 pt-px pb-0.5 text-start">#</th>
              <th className="w-full ps-1 pt-px pb-0.5 text-start uppercase">{translations.team ?? 'team'}</th>
              <th className="min-w-[55px] ps-1 pt-px pb-0.5 text-center uppercase">
                {translations.clubPoints ?? 'cc points'}
              </th>
              <th className="min-w-[80px] ps-1 pe-1 pt-px pb-0.5 text-center uppercase">
                {translations.prizeMoney ?? 'prize money'}
              </th>
            </tr>
          </thead>
          <tbody>
            <WinnerRow placement="first" translations={translations} winners={winners[1]} />
            <WinnerRow placement="second" translations={translations} winners={winners[2]} />
            <WinnerRow placement="third" translations={translations} winners={winners[3]} />
          </tbody>
        </table>
      </div>
    </div>
  );
};
