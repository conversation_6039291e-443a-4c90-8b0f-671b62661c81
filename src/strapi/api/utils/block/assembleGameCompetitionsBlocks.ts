import { Locale } from '@/hooks/i18n/const';
import {
  CLUB_CHAMPION_OVERVIEW_BLOCK_KEY,
  ClubChampionOverviewBlockType,
  COMPETITION_WINNERS_GRID_BLOCK_KEY,
  CompetitionWinnersGridBlockType,
  DynamicZoneBlock,
  PARTICIPATING_TEAMS_BLOCK_KEY,
  ParticipatingTeamsBlockType,
} from '@/strapi/types/block';

import { getGameCompetitions } from '../../collection/game';

const GAME_COMPETITION_BLOCK_TYPES = [
  PARTICIPATING_TEAMS_BLOCK_KEY,
  COMPETITION_WINNERS_GRID_BLOCK_KEY,
  CLUB_CHAMPION_OVERVIEW_BLOCK_KEY,
];

type GameCompetitionsBlockType =
  | ParticipatingTeamsBlockType
  | CompetitionWinnersGridBlockType
  | ClubChampionOverviewBlockType;

export async function assembleGameCompetitionsBlocks(blocks: DynamicZoneBlock[], locale: Locale) {
  const filteredBlocks = blocks.filter((b) =>
    GAME_COMPETITION_BLOCK_TYPES.includes(b.__component),
  ) as GameCompetitionsBlockType[];

  for (const block of filteredBlocks) {
    const games = await getGameCompetitions(locale, block.year);
    block.games = games;
  }
}
