import { VodEnrichedItem } from '@/blocks/VodBlock/VodGrid';
import { StreamIntermissionType, StreamType } from '@/strapi/types/collection/stream';

import { GameStream } from '../types/GameStream';

export const generateGameStream = (stream: StreamType): GameStream => {
  const isCustomStream = !stream.game;

  return {
    id: stream.id,
    title: isCustomStream ? stream.internalName : stream.game?.title,
    darkLogo: isCustomStream ? stream.logoDark : stream.game?.logoDark,
    lightLogo: isCustomStream ? stream.logoLight : stream.game?.schedulePopupLogo,
    backgroundImage: stream.backgroundImage,
    isGameStream: !!stream.game,
    isCustomStream: isCustomStream,
    tournamentIds: stream.game?.competitionSlugs?.map((slug) => slug.competitionId) ?? [],
    startTime: stream.startTime,
    endTime: stream.endTime,
    tag: stream.tag,
    customSchedule: stream.customSchedule,
    streamUrl: stream.streamUrl,
    slug: stream.game?.slug ?? null,
    intermissionText: stream.intermissionText,
    buttonUrl: stream.buttonUrl,
    buttonText: stream.buttonText,
    intermissionType: stream.intermissionType ?? StreamIntermissionType.TIMER,
    vods: stream.vods as VodEnrichedItem[],
  };
};
