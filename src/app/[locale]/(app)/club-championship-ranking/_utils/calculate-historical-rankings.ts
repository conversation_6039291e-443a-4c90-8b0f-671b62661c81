import { Tournament } from '@/services/graphql/types/tournament';
import { GameType } from '@/strapi/types/collection/game';

import { calculateRankings } from './calculate-rankings-new';
import { ClubData, HistoricalRankings } from './types';

/**
 * Creates a mapping of competition slugs to tournament IDs
 */
const createCompetitionSlugsMapping = (
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo'>[],
): Record<string, string[]> => {
  return gameCompetitions.reduce<Record<string, string[]>>((acc, game) => {
    if (!game?.slug) return acc;
    acc[game.slug] = game.competitionSlugs.map((competition) => competition.competitionId);
    return acc;
  }, {});
};

/**
 * Calculates rankings for a specific day, including rank difference from the previous day
 */
export const calculateRankingsForDay = (
  tournaments: Tournament[],
  targetDate: string,
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo' | 'title'>[],
): ClubData[] => {
  // For a given day, we want to include all matches from that day
  // So we need to look at data up until the start of the next day
  const nextDay = new Date(targetDate);
  nextDay.setDate(nextDay.getDate() + 1);
  const maxTimestamp = new Date(`${nextDay.toISOString().split('T')[0]}T00:00:00Z`);

  // Calculate rankings for the previous day to get rank differences
  const previousDate = new Date(targetDate);
  previousDate.setDate(previousDate.getDate() - 1);
  const previousNextDay = new Date(previousDate);
  previousNextDay.setDate(previousNextDay.getDate() + 1);
  const previousMaxTimestamp = new Date(`${previousNextDay.toISOString().split('T')[0]}T00:00:00Z`);

  // Create competition slugs mapping
  const competitionsSlugs = createCompetitionSlugsMapping(gameCompetitions);

  // Calculate rankings for both days
  const previousRankings = calculateRankings(competitionsSlugs, tournaments, previousMaxTimestamp, gameCompetitions);
  const currentRankings = calculateRankings(competitionsSlugs, tournaments, maxTimestamp, gameCompetitions);

  // Create a map of previous ranks for easy lookup
  const previousRanksMap = new Map(previousRankings.map((club) => [club.club.id, club.rank || 0]));

  // Add rank difference to current rankings
  return currentRankings.map((club) => ({
    ...club,
    rankDiff: previousRanksMap.has(club.club.id) ? previousRanksMap.get(club.club.id)! - (club.rank || 0) : 0, // If club wasn't ranked yesterday, no difference
  }));
};

/**
 * Calculates historical rankings for each day between tournament start and end dates
 * @deprecated Use calculateRankingsForDay instead for better performance
 */
export function calculateHistoricalRankings(
  tournaments: Tournament[],
  tournamentStartDate: string,
  tournamentEndDate: string,
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo' | 'title'>[],
): HistoricalRankings {
  const competitionsSlugs = createCompetitionSlugsMapping(gameCompetitions);
  const historicalRankings: HistoricalRankings = {};
  let previousDayRankings: ClubData[] | null = null;

  // Convert start and end dates to Date objects
  const startDate = new Date(tournamentStartDate);
  const endDate = new Date(tournamentEndDate);

  // Loop through each day
  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    // Create the maxTimestamp for the next day at midnight UTC
    // For example: if currentDate is 2025-07-09, maxTimestamp will be 2025-07-10T00:00:00Z
    // This means we'll calculate rankings for July 9th using data up until the start of July 10th
    const nextDay = new Date(currentDate);
    nextDay.setDate(nextDay.getDate() + 1);
    const maxTimestamp = new Date(`${nextDay.toISOString().split('T')[0]}T00:00:00Z`);

    // Calculate rankings for this day
    const dateKey = currentDate.toISOString().split('T')[0]; // Format: YYYY-MM-DD
    const currentDayRankings = calculateRankings(competitionsSlugs, tournaments, maxTimestamp, gameCompetitions);

    // Calculate rank differences between days for each club
    if (previousDayRankings) {
      currentDayRankings.forEach((club) => {
        // Find the same club in previous day's rankings
        const previousClubRanking = previousDayRankings?.find((prevClub) => prevClub.club.id === club.club.id);

        // Calculate rank difference between days
        if (club.rank && previousClubRanking?.rank) {
          club.rankDiff = previousClubRanking.rank - club.rank; // Positive means improvement (moved up), negative means decline
        } else {
          club.rankDiff = 0; // If club wasn't ranked yesterday or today, no difference
        }
      });
    }

    historicalRankings[dateKey] = currentDayRankings;

    // Store current day's rankings for next iteration
    previousDayRankings = currentDayRankings;

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return historicalRankings;
}
