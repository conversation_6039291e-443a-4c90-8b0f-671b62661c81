import { JsonFieldType } from '@/strapi/types/helper';
import { Only } from '@/ui/components/Only';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { MatchDetails } from '../types';
import { PlayersList } from './PlayersList';
import { ResultsView } from './ResultsView';
import { StatusView } from './StatusView';

type Props = MatchDetails & { translations: JsonFieldType };

export const Body = ({ teams, results, status, ...rest }: Props) => {
  const [teamA, teamB] = teams;
  results.forEach((r) => r.teams.sort((a) => (a.id === teamA.id ? -1 : 1)));

  const areResultsVisible = status !== LocalMatchStatus.UPCOMING && results.length !== 0;
  return (
    <div className="flex flex-col gap-1">
      <div className="flex gap-1 md:gap-2">
        <PlayersList players={teamA.players} />
        <Only for="mdAndAbove">
          <div className="flex w-[173px] flex-col items-center justify-center gap-1.5">
            <StatusView {...rest} status={status} />
            {areResultsVisible && <ResultsView results={results} />}
          </div>
        </Only>
        <PlayersList isReversed players={teamB.players} />
      </div>
      <Only for="sm">{areResultsVisible && <ResultsView results={results} />}</Only>
    </div>
  );
};
