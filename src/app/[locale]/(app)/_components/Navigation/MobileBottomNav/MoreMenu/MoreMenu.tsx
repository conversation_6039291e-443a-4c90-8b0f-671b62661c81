'use client';

import clsx from 'clsx';
import { AnimatePresence, motion } from 'motion/react';
import { MdClose, MdMoreHoriz } from 'react-icons/md';

import { Locale } from '@/hooks/i18n/const';
import { NavigationData } from '@/strapi/api/single/navigation';
import { JsonFieldType } from '@/strapi/types/helper';

import { MenuComponent } from './MenuComponent';

interface Props {
  data: NavigationData;
  disabledLocales?: Locale[];
  isEmbedded?: boolean;
  translations: JsonFieldType;
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
}

export const MoreMenu = ({ data, disabledLocales, isEmbedded, translations, isOpen, onOpen, onClose }: Props) => {
  return (
    <>
      <motion.div
        animate={{ backgroundColor: isOpen ? 'rgba(21, 21, 21, 1)' : 'rgba(21, 21, 21, 0)' }}
        className="my-1.5 grid cursor-pointer items-start justify-center justify-items-center gap-1 rounded-lg p-1.5"
        onClick={onOpen}
      >
        <div className="h-6 w-6">
          {isOpen ? (
            <MdClose className="text-2xl" color="#fff" />
          ) : (
            <MdMoreHoriz className="text-2xl" color="#151515" />
          )}
        </div>
        <span className={clsx('text-dark-default text-[10px] leading-none capitalize', isOpen && 'text-white')}>
          {isOpen ? (translations.close ?? 'Close') : (translations.more ?? 'More')}
        </span>
      </motion.div>
      <AnimatePresence>
        {isOpen && (
          <MenuComponent
            data={data}
            disabledLocales={disabledLocales}
            isEmbedded={isEmbedded}
            translations={translations}
            onClick={onClose}
          />
        )}
      </AnimatePresence>
    </>
  );
};
