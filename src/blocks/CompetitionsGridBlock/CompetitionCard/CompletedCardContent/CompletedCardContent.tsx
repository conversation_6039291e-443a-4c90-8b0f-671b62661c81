import { Only } from '@/ui/components/Only';

import { GameWinner } from '../../fetchCompletedGamesData';
import { CardContentProps } from '../ActiveCardContent';
import { CardLogo } from '../CardLogo';
import { TextSection } from '../TextSection';
import { WinnersTable } from './WinnersTable';

type Props = CardContentProps & { isCompletedBadgeVisible?: boolean; winners: Record<number, GameWinner[]> };

export function CompletedCardContent({ game, isCompletedBadgeVisible, winners, translations }: Props) {
  return (
    <div className="flex w-full flex-col items-center md:gap-[27px]">
      <div className="flex w-full flex-col items-center px-2 pt-2 md:px-6 md:pt-6">
        {isCompletedBadgeVisible && (
          <p className="bg-gold-primary font-primary rounded-[2px] px-0.5 py-0.5 text-[10px] leading-none font-bold text-white uppercase md:px-1 md:text-[11px]">
            {translations['completed'] ?? 'completed'}
          </p>
        )}
        <CardLogo logo={game.schedulePopupLogo} />
      </div>
      <WinnersTable translations={translations} winners={winners} />
      <div className="flex w-full flex-col border-[#313131] px-6 text-center text-white max-md:pt-1">
        {game.prizePool && <TextSection title={translations['prizePool'] ?? 'Prize Pool'} value={game.prizePool} />}
        <Only for="mdAndAbove">
          {game.competingTeams && (
            <TextSection
              title={translations['participatingClubs'] ?? 'Participating Clubs'}
              value={game.competingTeams}
            />
          )}
          {game.competingPlayers && (
            <TextSection
              title={translations['participatingPlayers'] ?? 'Participating Players'}
              value={game.competingPlayers}
            />
          )}
        </Only>
      </div>
    </div>
  );
}
