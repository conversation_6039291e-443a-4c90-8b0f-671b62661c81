import { Locale } from '@/hooks/i18n/const';
import { DynamicZoneBlock } from '@/strapi/types/block';
import { WHERE_TO_WATCH_BLOCK_KEY, WhereToWatchBlockType } from '@/strapi/types/gamePageBlock';

import { getStreamProviders } from '../../collection/streamProvider';

export async function assembleWhereToWatchBlocks(blocks: DynamicZoneBlock[], locale: Locale) {
  const isWhereToWatchBlockPresent = blocks.some((b) => b.__component === WHERE_TO_WATCH_BLOCK_KEY);
  if (!isWhereToWatchBlockPresent) {
    return;
  }

  const providers = await getStreamProviders(locale);
  const whereToWatchBlocks = blocks.filter(
    (b) => b.__component === WHERE_TO_WATCH_BLOCK_KEY,
  ) as WhereToWatchBlockType[];
  whereToWatchBlocks.forEach((b) => (b.providers = providers));
}
