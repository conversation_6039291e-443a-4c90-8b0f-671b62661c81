'use client';

import clsx from 'clsx';
import { useState } from 'react';
import { MdInfoOutline } from 'react-icons/md';

import { useDateLocaleFormatter } from '@/hooks/i18n/useDateLocale';
import { JsonFieldType } from '@/strapi/types/helper';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { BracketMatch, BracketMatchType } from '../../../../utils/brackets/types';
import { ContestantBox } from '../ContestantBox';
import { MatchDetailsModal } from './MatchDetailsModal';

type BracketBoxProps = BracketMatch & {
  translations: JsonFieldType;
};

export const BracketBox = ({
  id,
  type,
  startTime,
  contestants,
  matches,
  status,
  descriptor,
  translations,
  topText,
}: BracketBoxProps) => {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const format = useDateLocaleFormatter();

  const isLiveMatch = status === LocalMatchStatus.LIVE;
  const isRegularMatch = [BracketMatchType.REGULAR, BracketMatchType.QUALIFIER, BracketMatchType.THIRD_PLACE].includes(
    type,
  );
  const isWinnerBox = [BracketMatchType.QUALIFIED, BracketMatchType.WINNER].includes(type);
  return (
    <>
      <div
        className={clsx(
          'flex w-[220px] shrink-0 flex-col gap-1 rounded-sm p-1 md:w-[264px] md:gap-1.5 md:rounded-lg md:p-2',
          !isWinnerBox && 'cursor-pointer',
          isDetailsModalOpen && 'opacity-50',
          isLiveMatch
            ? 'bg-[#F40F30] shadow-[0px_8px_24px_0px_#F40F301A]'
            : {
                'bg-white-dirty shadow-[0px_8px_24px_0px_#FFFFFF4D]': isRegularMatch,
                'bg-dark-default shadow-[0px_8px_24px_0px_#FFFFFF4D]': type === BracketMatchType.QUALIFIED,
                'bg-radial-gold shadow-[0px_8px_24px_0px_#FFFFFF4D]': [
                  BracketMatchType.FINALS,
                  BracketMatchType.WINNER,
                ].includes(type),
              },
        )}
        id={id}
        onClick={isWinnerBox ? undefined : () => setIsDetailsModalOpen(true)}
      >
        <div className="font-primary flex h-3 items-center justify-between gap-2.5 text-[8px] leading-none font-bold uppercase md:h-4 md:text-[10px]">
          <p
            className={clsx(
              'ps-1.5',
              isLiveMatch
                ? 'flex-1 rounded-sm bg-white px-1 py-0.5 text-[#F10F30] md:px-[5px] md:py-[3px]'
                : isRegularMatch
                  ? 'text-dark-default'
                  : 'text-white',
            )}
          >
            {isRegularMatch || type === BracketMatchType.FINALS ? format(startTime, 'eee, MMM d - h:mm a') : topText}
          </p>
          <p
            className={clsx('rounded-xs px-1 py-0.5 uppercase md:rounded-sm md:px-[5px] md:py-[3px]', {
              'text-dark-default bg-white': status === LocalMatchStatus.COMPLETED,
              'bg-dark-default text-white': status === LocalMatchStatus.UPCOMING,
              'bg-white text-[#F10F30]': isLiveMatch,
              invisible: isWinnerBox,
            })}
          >
            {translations[status] ?? status}
          </p>
        </div>
        {contestants.map((c) => (
          <ContestantBox key={c.id} {...c} isWinnerBracketBox={isWinnerBox} status={status} />
        ))}
        {!isWinnerBox && (
          <div
            className={clsx(
              'flex h-3 items-center justify-between md:h-4',
              !isLiveMatch && isRegularMatch ? 'text-dark-default' : 'text-white',
            )}
          >
            <p className="font-base ps-1 text-[8px] leading-none font-extrabold uppercase">{descriptor}</p>
            <MdInfoOutline className="size-4" />
          </div>
        )}
      </div>
      <MatchDetailsModal
        details={{ id, teams: contestants, results: matches, startTime, status: status as LocalMatchStatus }}
        isOpen={isDetailsModalOpen}
        translations={translations}
        onClose={() => setIsDetailsModalOpen(false)}
      />
    </>
  );
};
