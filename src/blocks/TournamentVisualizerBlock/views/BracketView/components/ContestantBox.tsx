import clsx from 'clsx';
import Image from 'next/image';

import { BracketContestant } from '@/blocks/TournamentVisualizerBlock/utils/brackets/types';
import PlaceholderLogo from '@/ui/assets/images/ewc-placeholder.png';
import { isWinningResult, LocalMatchStatus } from '@/utils/matchSeries';

type ContestantBoxProps = BracketContestant & { status: LocalMatchStatus | string; isWinnerBracketBox: boolean };

export const ContestantBox = ({ name, score, result, logoUrl, status, isWinnerBracketBox }: ContestantBoxProps) => {
  const isWinner = isWinningResult(result);
  return (
    <div className="flex h-8 items-center justify-between rounded-xs bg-white p-[3px] ps-[9px] md:h-[55px] md:rounded-md">
      <div
        className={clsx(
          'flex min-w-0 items-center gap-1',
          status === LocalMatchStatus.COMPLETED && !isWinner && 'opacity-50',
        )}
      >
        <Image
          alt={`${name} logo`}
          className="size-5 md:size-10"
          height={40}
          src={logoUrl ?? PlaceholderLogo}
          width={40}
        />
        <p className="font-primary text-dark-default max-w-full truncate text-[10px] leading-none font-bold uppercase md:text-xs">
          {name}
        </p>
      </div>
      {!isWinnerBracketBox && (
        <div
          className={clsx(
            'flex size-[26px] items-center justify-center rounded-[2px] px-[7.5px] py-[5px] md:size-[49px] md:rounded-[3px] md:p-2',
            ((status === LocalMatchStatus.COMPLETED && isWinner) || status === LocalMatchStatus.LIVE) &&
              'bg-white-dirty',
          )}
        >
          <p
            className={clsx('font-primary text-dark-default text-base leading-none font-bold md:text-2xl', {
              'opacity-30': status === LocalMatchStatus.UPCOMING,
              'opacity-50': status === LocalMatchStatus.COMPLETED && !isWinner,
            })}
          >
            {score ?? '-'}
          </p>
        </div>
      )}
    </div>
  );
};
