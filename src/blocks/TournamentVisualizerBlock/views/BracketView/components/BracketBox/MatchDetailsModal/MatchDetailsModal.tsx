import { RefObject, useRef } from 'react';
import { useOnClickOutside } from 'usehooks-ts';

import { useSelectedMatch } from '@/app/[locale]/(app)/competitions/[slug]/_components/SelectedMatchProvider';
import { useLockBodyScroll } from '@/app/[locale]/(app)/schedule/hooks/useLockBodyScroll';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { Body } from './Body';
import { StatusView } from './Body/StatusView';
import { Header } from './Header';
import { MatchDetails } from './types';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  details: MatchDetails;
  translations: JsonFieldType;
}

export const MatchDetailsModal = ({ isOpen, onClose, details, translations }: Props) => {
  const modalRef = useRef<HTMLDivElement>(null);
  useOnClickOutside(modalRef as RefObject<HTMLDivElement>, onClose);
  useLockBodyScroll(isOpen, { shouldPreserveGutter: true });

  const { setSelectedMatchId } = useSelectedMatch();

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center">
      <div
        className="bg-white-dirty w-full max-w-[300px] rounded-lg p-2 shadow-[0px_12px_24px_0px_#15151547] md:max-w-[485px] md:p-3"
        ref={modalRef}
      >
        <div className="flex flex-col gap-1 md:gap-1.5">
          <Only for="sm">
            <StatusView {...details} translations={translations} />
          </Only>
          <Header status={details.status} teams={details.teams} />
          <Body {...details} translations={translations} />
          {details.status !== LocalMatchStatus.UPCOMING && (
            <WatchVodButton
              isLiveMatch={details.status === LocalMatchStatus.LIVE}
              translations={translations}
              onClick={() => {
                onClose();
                setSelectedMatchId(details.id);
                window.scrollTo({ top: 0, behavior: 'smooth' });
              }}
            />
          )}
          <Button
            brazeEventProperties={{
              button_name: 'Close Modal button',
              location: 'Tournament Visualizer Block - Brackets View - Match Details Modal',
            }}
            isFullWidth
            size="small"
            text={translations['close'] ?? 'close'}
            variant="secondary"
            onClick={onClose}
          />
        </div>
      </div>
    </div>
  );
};

interface WatchVodButtonProps {
  isLiveMatch: boolean;
  translations: JsonFieldType;
  onClick: () => void;
}

export const WatchVodButton = ({ isLiveMatch, onClick, translations }: WatchVodButtonProps) => {
  return (
    <Button
      brazeEventProperties={{
        button_name: 'Watch VOD button',
        location: 'Tournament Visualizer Block - Brackets View - Match Details Modal',
      }}
      isFullWidth
      size="small"
      text={isLiveMatch ? (translations['watchNow'] ?? 'watch now') : (translations['watchVod'] ?? 'watch vod')}
      variant={isLiveMatch ? 'red' : 'secondary'}
      onClick={onClick}
    />
  );
};
