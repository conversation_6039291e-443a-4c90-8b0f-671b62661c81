import { fetchTournamentsData } from '@/services/graphql/api';
import { Prize, PrizeCurrency, TournamentContestant, TournamentStatus } from '@/services/graphql/types/tournament';
import { constructImageUrl } from '@/services/graphql/utils';
import { GameType } from '@/strapi/types/collection/game';

export interface GameCompletedData {
  isCompleted: boolean;
  winners: Record<number, GameWinner[]>;
}

export interface GameWinner {
  name: string | null;
  logoUrl: string | null;
  points: number | null;
  prize: number | null;
}

export async function fetchCompletedGamesData(games: GameType[]) {
  const tournamentIds = games.flatMap((g) => g.competitionSlugs.map((cs) => cs.competitionId));
  const data = await fetchTournamentsData(tournamentIds);

  const completedGamesDataMap: Record<string, GameCompletedData> = {};
  if (!data?.tournaments.result) {
    return completedGamesDataMap;
  }

  for (const game of games) {
    const tournamentIds = game.competitionSlugs.map((cs) => cs.competitionId);
    const tournaments = data.tournaments.result.filter((t) => tournamentIds.includes(t.id));
    const isCompleted = tournaments.every((t) => t.status === TournamentStatus.FINISHED);

    const completedGameData: GameCompletedData = { isCompleted, winners: {} };
    completedGamesDataMap[game.documentId] = completedGameData;

    if (!isCompleted) {
      continue;
    }

    const prizePool = tournaments.flatMap((t) => t.prizePool ?? []);
    const contestants = tournaments.flatMap((t) => t.contestants ?? []);
    for (let i = 1; i < 4; i++) {
      completedGameData.winners[i] = getWinnerDataForPlacement(prizePool, contestants, i);
    }
  }

  return completedGamesDataMap;
}

function getWinnerDataForPlacement(
  prizePool: Prize[],
  contestants: TournamentContestant[],
  placement: number,
): GameWinner[] {
  const prize = prizePool.find((p) => p.currency === PrizeCurrency.USD && p.rank === placement)?.amount ?? null;
  const points = prizePool.find((p) => p.currency === PrizeCurrency.XTS && p.rank === placement)?.amount ?? null;
  const winningContestants = contestants.filter((c) => c.rank === placement);

  return winningContestants.map((wc) => ({
    prize,
    points,
    name: wc.team?.name ?? null,
    logoUrl: constructImageUrl(wc.team?.images, 'logo_transparent_whitebg'),
  }));
}
