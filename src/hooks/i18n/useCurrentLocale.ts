'use client';

import { useCurrentLocale as useI18nCurrentLocale } from 'next-i18n-router/client';
import { Config } from 'next-i18n-router/dist/types';

import { IS_SERVER } from '@/config/env';

import i18nConfig from '../../../i18nConfig';
import { Locale } from './const';

export function useCurrentLocale() {
  const cookieLocale = useI18nCurrentLocale(i18nConfig as Config) as Locale;

  if (IS_SERVER) {
    return cookieLocale;
  }
  return getBrowserCacheSafeLocale(cookieLocale);
}

/**
 * Determines the most reliable locale to use based on the current browser context,
 * accounting for browser back/forward cache (bfcache) behavior.
 *
 * When navigating using the browser's forward or backward buttons, the HTML content
 * may be restored from the browser's bfcache, which can cause the `cookieLocale` to
 * become unreliable if the navigation is to a different locale.
 *
 * eg. on `en` page, changing language to `ar` (persists locale to cookie),
 * navigating via browser history back to `en` (cached response, cookie locale is old)
 * -> `cookieLocale` is still set to `ar`
 *
 * In such cases, this function returns the locale inferred from the current URL path instead.
 *
 * @param cookieLocale - The locale value obtained from cookies, representing
 *   the user's last selected or persisted locale.
 * @returns The most reliable locale to use, preferring the locale from the URL path
 *   if a bfcache navigation is detected, otherwise returning the `cookieLocale`.
 */
function getBrowserCacheSafeLocale(cookieLocale: Locale) {
  const clientLocale = window.location.pathname.split('/')[1] as Locale;

  const isStaleCookieLocale = clientLocale !== cookieLocale;
  if (isStaleCookieLocale) {
    return clientLocale;
  }

  return cookieLocale;
}
