export type IndicatorStatusType = 'upcoming' | 'live' | 'completed';

type GameEventStatusIndicatorProps = {
  status?: IndicatorStatusType;
  label: string;
  className?: string;
} & React.HTMLAttributes<HTMLDivElement>;

export const GameEventStatusIndicator = ({
  status = 'completed',
  className = '',
  label,
  ...rest
}: GameEventStatusIndicatorProps) => {
  const isLive = status === 'live';

  return (
    <div
      className={`font-primary inline-flex items-center gap-1 rounded-sm px-2 py-1 text-[9px] font-bold uppercase ${className} `}
      {...rest}
    >
      {isLive && <span className="block size-[6px] rounded-full bg-white" />}
      {label}
    </div>
  );
};
