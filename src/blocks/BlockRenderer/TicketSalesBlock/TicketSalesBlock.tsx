'use client';

import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { ScrollWithFadeWrapper } from '@/components/ScrollWithFadeWrapper';
import { TicketSalesBlockType } from '@/strapi/types/block';

import { TicketCard } from './TicketCard';

export const TicketSalesBlock = ({ tickets, section }: TicketSalesBlockType) => {
  return (
    <BlockSectionWrapper {...section} isCTAButton>
      <ScrollWithFadeWrapper>
        <div className="flex gap-4 pt-3 pb-9">
          {tickets.map((ticket) => (
            <TicketCard {...ticket} key={ticket.id} />
          ))}
        </div>
      </ScrollWithFadeWrapper>
    </BlockSectionWrapper>
  );
};
