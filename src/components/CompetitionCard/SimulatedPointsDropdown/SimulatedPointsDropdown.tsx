import { useEffect, useState } from 'react';
import Select from 'react-select';

import { useSimulator } from '@/app/[locale]/(app)/club-championship-ranking/_context/SimulatorContext';
import { JsonFieldType } from '@/strapi/types/helper';

import { customStyles } from './styles';

export type OptionType = {
  label: string;
  value: number | null;
  isDisabled?: boolean;
};

export const SIMULATED_POINTS_OPTIONS = [
  { canBeDisabled: true, label: '1st Place', key: '1stPlace', value: 1000 },
  { canBeDisabled: true, label: '2nd Place', key: '2ndPlace', value: 750 },
  { canBeDisabled: true, label: '3rd Place', key: '3rdPlace', value: 500 },
  { canBeDisabled: true, label: '4th Place', key: '4thPlace', value: 300 },
  { label: 'No Points', key: 'noPoints', value: 0 },
];

export const SimulatedPointsDropdown = ({
  competitionSlug,
  initialValue,
  onPointsChange,
  initialPoints,
  translations,
  clubId,
}: {
  initialPoints: number;
  competitionSlug: string;
  initialValue?: number | null;
  onPointsChange: (competitionSlug: string, points: number | null) => void;
  translations?: JsonFieldType | null;
  clubId: string;
}) => {
  const [menuPortalTarget, setMenuPortalTarget] = useState<HTMLElement | null>(null);
  const { simulatedCompetitionsMap } = useSimulator();

  useEffect(() => {
    setMenuPortalTarget(document.body);
  }, []);

  const options: OptionType[] = SIMULATED_POINTS_OPTIONS.map((option) => {
    // Only check for other clubs using this option if it can be disabled
    const value = simulatedCompetitionsMap?.[competitionSlug]?.[option.key];
    const isUsedByOtherClub = option.canBeDisabled && !!value && value !== clubId;

    return {
      label: translations?.[option.key] ?? option.label,
      value: option.value,
      isDisabled: simulatedCompetitionsMap
        ? initialPoints
          ? option.value !== null && initialPoints >= option.value
          : isUsedByOtherClub
        : false,
    };
  });

  const selectedOption = options.find((opt) => opt.value === initialValue) ?? null;

  return (
    <div className="relative">
      <Select<OptionType>
        isSearchable={false}
        menuPortalTarget={menuPortalTarget}
        options={options}
        placeholder={translations?.selectPoints ?? 'select points'}
        styles={customStyles}
        value={selectedOption}
        onChange={(newValue) => onPointsChange(competitionSlug, newValue?.value ?? null)}
      />
    </div>
  );
};
