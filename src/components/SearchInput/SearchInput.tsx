'use client';

import clsx from 'clsx';
import { MdSearch } from 'react-icons/md';

import { logButtonClickedEvent } from '@/services/braze';

export interface SearchInputProps {
  value: string;
  placeholder?: string;
  analyticsLocation?: string;
  analyticsButtonName?: string;
  variant?: 'flat' | 'elevated';
  onChange: (value: string) => void;
  onConfirm?: () => void;
}

export const SearchInput = ({
  value,
  onChange,
  onConfirm,
  placeholder = 'Search...',
  variant = 'elevated',
  analyticsLocation = 'Search Input',
  analyticsButtonName = 'Trigger Search Button',
}: SearchInputProps) => {
  return (
    <div
      className={clsx(
        'flex w-full items-center justify-between gap-2 py-[15px] ps-6 pe-4',
        'rounded-xl lg:rounded-[15px]',
        'bg-white',
        variant === 'flat' ? 'border-gray border' : 'shadow-[4px_8px_16px_0px_#00000014]',
      )}
    >
      <input
        className={clsx(
          'text-dark-default grow outline-none',
          'placeholder:font-base placeholder:text-base placeholder:leading-normal placeholder:font-bold placeholder:text-[#D1D1D1]',
        )}
        placeholder={placeholder}
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            onConfirm?.();
          }
        }}
      />
      <button
        onClick={() => {
          onConfirm?.();
          logButtonClickedEvent({
            location: analyticsLocation,
            button_name: analyticsButtonName,
          });
        }}
      >
        <MdSearch className="text-[#272727]" size={21} />
      </button>
    </div>
  );
};
