import { VodBlock } from '@/blocks/VodBlock/VodBlock';
import {
  ANNOUNCED_GAMES_GRID_BLOCK_KEY,
  AUTOSCROLLING_SHOWCASE_KEY,
  BlockBase,
  CLUB_CHAMPION_OVERVIEW_BLOCK_KEY,
  COMPETITION_WINNERS_GRID_BLOCK_KEY,
  COMPETITIONS_GRID_BLOCK_KEY,
  CONTENT_NAVIGATION_GRID_BLOCK_KEY,
  CTA_BLOCK_KEY,
  FAQ_BLOCK_KEY,
  GREENFLY_MEDIA_BLOCK,
  KEY_STATISTICS_BLOCK_KEY,
  MEDIA_BLOCK_KEY,
  NEWS_BLOCK_KEY,
  PARTICIPATING_TEAMS_BLOCK_KEY,
  PARTNER_RIBBON_BLOCK_KEY,
  PARTNERS_BLOCK_KEY,
  PROMO_BLOCK_KEY,
  PULL_QUOTE_BLOCK_KEY,
  QUALIFIERS_GRID_BLOCK_KEY,
  QUALIFIERS_LIST_BLOCK_KEY,
  R<PERSON><PERSON><PERSON>_BLOCK_KEY,
  RICH_TEXT_BLOCK_KEY,
  SHOWCASE_GRID_BLOCK_KEY,
  STACKABLE_INFO_BLOCK_KEY,
  TICKET_SALES_BLOCK_KEY,
  TIMELINE_BLOCK_KEY,
  VISUAL_INFO_BLOCK_KEY,
  VOD_BLOCK_KEY,
} from '@/strapi/types/block';
import { FESTIVAL_FILTER_BLOCK_KEY, FESTIVAL_LIST_BLOCK_KEY } from '@/strapi/types/festivalBlock';
import {
  GAME_SCHEDULE_BLOCK_KEY,
  STANDINGS_TABLE_BLOCK_KEY,
  TOURNAMENT_VISUALIZER_BLOCK_KEY,
  WHERE_TO_WATCH_BLOCK_KEY,
} from '@/strapi/types/gamePageBlock';

import { AnnouncedGamesGridBlock } from '../AnnouncedGamesGridBlock';
import { AutoscrollingShowcaseBlock } from '../AutscrollingShowcaseBlock';
import { ClubChampionOverviewBlock } from '../ClubChampionOverviewBlock';
import { CompetitionsGridBlock } from '../CompetitionsGridBlock';
import { CompetitionWinnersGridBlock } from '../CompetitionWinnersGridBlock';
import { ContentNavigationGridBlock } from '../ContentNavigationGridBlock';
import { CtaBlock } from '../CtaBlock';
import { FaqBlock } from '../FaqBlock';
import { FestivalFilterBlock } from '../FestivalFilterBlock';
import { FestivalListBlock } from '../FestivalListBlock';
import { GameScheduleBlock } from '../GameScheduleBlock';
import { GreenflyMediaGallery } from '../GreenflyMediaGallery';
import { KeyStatistics } from '../KeyStatistics';
import { MediaBlock } from '../MediaBlock';
import { NewsCardsBlock } from '../NewsCardsBlock';
import { ParticipatingTeamsBlock } from '../ParticipatingTeamsBlock';
import { PartnerRibbonBlock } from '../PartnerRibbonBlock';
import { PartnersBlock } from '../PartnersBlock';
import { PromoBlock } from '../PromoBlock';
import { PullQuoteBlock } from '../PullQuoteBlock';
import { QualifiersGridBlock } from '../QualifiersGridBlock';
import { QualifiersListBlock } from '../QualifiersListBlock';
import { RibbonBlock } from '../RibbonBlock';
import { RichTextBlock } from '../RichTextBlock';
import { ShowcaseGridBlock } from '../ShowcaseGridBlock';
import { StackableInfoBlock } from '../StackableInfoBlock';
import { StandingsTableBlock } from '../StandingsTableBlock';
import { TimelineBlock } from '../TimelineBlock';
import { TournamentVisualizerBlock } from '../TournamentVisualizerBlock';
import { VisualInfoBlock } from '../VisualInfoBlock';
import { WhereToWatchBlock } from '../WhereToWatchBlock';
import { TicketSalesBlock } from './TicketSalesBlock/TicketSalesBlock';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const rendererMap: Record<string, React.FunctionComponent<any>> = {
  [SHOWCASE_GRID_BLOCK_KEY]: ShowcaseGridBlock,
  [NEWS_BLOCK_KEY]: NewsCardsBlock,
  [PULL_QUOTE_BLOCK_KEY]: PullQuoteBlock,
  [FAQ_BLOCK_KEY]: FaqBlock,
  [RICH_TEXT_BLOCK_KEY]: RichTextBlock,
  [MEDIA_BLOCK_KEY]: MediaBlock,
  [RIBBON_BLOCK_KEY]: RibbonBlock,
  [AUTOSCROLLING_SHOWCASE_KEY]: AutoscrollingShowcaseBlock,
  [PROMO_BLOCK_KEY]: PromoBlock,
  [CTA_BLOCK_KEY]: CtaBlock,
  [ANNOUNCED_GAMES_GRID_BLOCK_KEY]: AnnouncedGamesGridBlock,
  [VISUAL_INFO_BLOCK_KEY]: VisualInfoBlock,
  [STACKABLE_INFO_BLOCK_KEY]: StackableInfoBlock,
  [TICKET_SALES_BLOCK_KEY]: TicketSalesBlock,
  [PARTNERS_BLOCK_KEY]: PartnersBlock,
  [KEY_STATISTICS_BLOCK_KEY]: KeyStatistics,
  [TIMELINE_BLOCK_KEY]: TimelineBlock,
  [QUALIFIERS_GRID_BLOCK_KEY]: QualifiersGridBlock,
  [QUALIFIERS_LIST_BLOCK_KEY]: QualifiersListBlock,
  [CONTENT_NAVIGATION_GRID_BLOCK_KEY]: ContentNavigationGridBlock,
  [PARTICIPATING_TEAMS_BLOCK_KEY]: ParticipatingTeamsBlock,
  [PARTNER_RIBBON_BLOCK_KEY]: PartnerRibbonBlock,
  [STANDINGS_TABLE_BLOCK_KEY]: StandingsTableBlock,
  [GAME_SCHEDULE_BLOCK_KEY]: GameScheduleBlock,
  [TOURNAMENT_VISUALIZER_BLOCK_KEY]: TournamentVisualizerBlock,
  [FESTIVAL_LIST_BLOCK_KEY]: FestivalListBlock,
  [FESTIVAL_FILTER_BLOCK_KEY]: FestivalFilterBlock,
  [WHERE_TO_WATCH_BLOCK_KEY]: WhereToWatchBlock,
  [GREENFLY_MEDIA_BLOCK]: GreenflyMediaGallery,
  [VOD_BLOCK_KEY]: VodBlock,
  [COMPETITION_WINNERS_GRID_BLOCK_KEY]: CompetitionWinnersGridBlock,
  [CLUB_CHAMPION_OVERVIEW_BLOCK_KEY]: ClubChampionOverviewBlock,
  [COMPETITIONS_GRID_BLOCK_KEY]: CompetitionsGridBlock,
};

const isFullWidthBlock = (block: string) =>
  [RIBBON_BLOCK_KEY, PROMO_BLOCK_KEY, PARTNER_RIBBON_BLOCK_KEY, GREENFLY_MEDIA_BLOCK, MEDIA_BLOCK_KEY].includes(block);
const isRichTextBlock = (block: string) => block === RICH_TEXT_BLOCK_KEY;

export const BlockRenderer = ({ __component, section, ...rest }: BlockBase) => {
  const Block = rendererMap[__component];

  switch (true) {
    case Block === undefined:
      return null;
    case isFullWidthBlock(__component):
      return <Block section={section} {...rest} />;
    default:
      return (
        <Block section={{ ...section, className: 'mx-auto py-[50px] lg:py-[90px] px-4 lg:px-8 max-w-6xl' }} {...rest} />
      );
  }
};

export const PostBlockRenderer = ({ __component, section, ...rest }: BlockBase) => {
  const Block = rendererMap[__component];

  switch (true) {
    case Block === undefined:
      return null;
    case isFullWidthBlock(__component):
      return <Block section={section} {...rest} />;
    case isRichTextBlock(__component):
      return (
        <div className="flex px-4 lg:px-8 xl:px-35 2xl:justify-center">
          <div className="flex w-full justify-center xl:max-w-[1158px]">
            <Block section={{ ...section, className: 'max-w-[920px]' }} {...rest} />
          </div>
        </div>
      );
    default:
      return (
        <div className="max-w-full px-4 lg:px-8 xl:px-35 2xl:mx-auto">
          <Block section={{ ...section, className: 'w-full xl:max-w-[1158px]' }} {...rest} />
        </div>
      );
  }
};
