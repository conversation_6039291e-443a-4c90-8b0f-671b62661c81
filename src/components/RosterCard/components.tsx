'use client';

import clsx from 'clsx';
import { MdOutlineCalendarMonth, MdPeople } from 'react-icons/md';

import { useDateLocaleFormatter } from '@/hooks/i18n/useDateLocale';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';

import { StrapiImage } from '../StrapiImage';
import { Roster } from './types';

interface HeaderProps {
  teamName: string;
  game: Roster['game'];
  theme: 'dark' | 'light';
}

export const Header = ({ teamName, game, theme }: HeaderProps) => {
  return (
    <div className="flex justify-between gap-4">
      <div className="font-base flex flex-col leading-none">
        <p
          className={clsx('text-base font-extrabold capitalize', theme === 'dark' ? 'text-white' : 'text-dark-default')}
        >
          {teamName}
        </p>
        <p className={clsx('text-xs font-semibold', theme === 'dark' ? 'text-white-dirty' : 'text-gray-dark')}>
          {game.name}
        </p>
      </div>
      {theme === 'dark' && game.logoLight && (
        <StrapiImage className="h-9 w-[120px] object-contain" height={36} image={game.logoLight} width={120} />
      )}
      {theme === 'light' && game.logoDark && (
        <StrapiImage className="h-9 w-[120px] object-contain" height={36} image={game.logoDark} width={120} />
      )}
    </div>
  );
};

interface PositionedSectionProps {
  position: number | null;
  gameSlug: string | null;
  translations: JsonFieldType;
}

export const PositionedSection = ({ position, gameSlug, translations }: PositionedSectionProps) => {
  return (
    <div className="flex items-end justify-between">
      <div className="font-base flex flex-col gap-2">
        <p className="text-xs leading-none font-normal capitalize">{translations.positioned ?? 'Positioned'}</p>
        <p className="text-2xl leading-none font-extrabold">{position ?? '-'}</p>
      </div>
      {gameSlug && (
        <Button
          brazeEventProperties={{
            button_name: `Results for game link (${gameSlug})`,
            location: `Club Modal - Roster Card`,
          }}
          link={`competitions/${gameSlug}`}
          size="small"
          text={translations.results ?? 'RESULTS'}
          variant="secondary"
        />
      )}
    </div>
  );
};

interface ViewSectionProps {
  dateFrom: string | null;
  dateTo: string | null;
  teamsCount: number;
  gameSlug: string | null;
  translations: JsonFieldType;
}

export const ViewSection = ({ dateFrom, dateTo, teamsCount, gameSlug, translations }: ViewSectionProps) => {
  const format = useDateLocaleFormatter();

  return (
    <div className="text-dark-default flex items-end justify-between gap-4">
      <div className="flex flex-col gap-1">
        {dateFrom && dateTo && (
          <div className="flex items-center gap-1">
            <MdOutlineCalendarMonth className="size-[14px]" />
            <p className="font-base mt-0.5 text-xs leading-none font-normal">
              {format(dateFrom, 'MMM do')} - {format(dateTo, 'MMM do')}
            </p>
          </div>
        )}
        <div className="flex items-center gap-1">
          <MdPeople className="size-[14px]" />
          <p className="font-base mt-0.5 text-xs leading-none font-extrabold">
            {teamsCount} {translations.teams ?? 'teams'}
          </p>
        </div>
      </div>
      {gameSlug && (
        <Button
          brazeEventProperties={{ button_name: `View Game link (${gameSlug})`, location: 'Club Modal - Roster Card' }}
          link={`competitions/${gameSlug}`}
          size="small"
          text={translations.view ?? 'view'}
        />
      )}
    </div>
  );
};
