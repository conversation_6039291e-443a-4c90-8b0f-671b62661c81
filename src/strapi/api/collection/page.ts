import { Locale } from '@/hooks/i18n/const';
import { PageType } from '@/strapi/types/collection/page';
import { CUSTOM_STREAM_HERO_BLOCK_KEY } from '@/strapi/types/hero';
import { SeoType } from '@/strapi/types/shared/seo';

import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils/block';
import { assembleHeroWithAdditionalData } from '../utils/hero';
import { populateVods } from './vods';

export async function getPageSlugs(locale: Locale) {
  try {
    const pageData = await strapiClient.collection('pages').find({
      fields: ['slug'],
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data as PageType[];
    if (!data) {
      return [];
    }

    const slugs = data.filter((d) => d.slug).map((d) => d.slug);
    return slugs;
  } catch (error) {
    console.error('Page slugs not fetched!', error);
    return [];
  }
}

export async function getPageData(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('pages').find({
      filters: { slug: { $eq: slug } },
      populate: {
        background: { populate: '*' },
        header: { populate: { logo: { populate: '*' }, cover: { populate: '*' }, buttons: { populate: '*' } } },
        hero: {
          populate: {
            weekStream: { populate: { streams: { populate: { customSchedule: true, vods: populateVods } } } },
          },
        },
        blocks: getDynamicZoneBlocks(),
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as PageType;
    if (!data) {
      return null;
    }

    await assembleBlocksWithAdditionalData(data.blocks, locale);
    if (data.hero) {
      //! add block key manually, missing due to specific block configuration for this component
      data.hero.__component = CUSTOM_STREAM_HERO_BLOCK_KEY;
      await assembleHeroWithAdditionalData(data.hero, locale);
    }

    return data;
  } catch (error) {
    console.error('Page data not fetched!', error);
    throw new Error('Page data not fetched!');
  }
}

export async function getPageSeo(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('pages').find({
      filters: { slug: { $eq: slug } },
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as PageType;
    if (!data) {
      return null;
    }
    return data.seo as SeoType;
  } catch (error) {
    console.error('Page SEO not fetched!', error);
    return null;
  }
}
