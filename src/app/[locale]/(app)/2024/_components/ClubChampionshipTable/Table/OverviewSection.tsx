import Image from 'next/image';

import { Only } from '@/ui/components/Only';

import Falcons<PERSON>ogo from '../../assets/clubs/falcons.png';
import { TableProps } from './Table';

export const OverviewSection = ({ translations }: TableProps) => (
  <Only fallback={<DesktopOverviewSection translations={translations} />} for="sm">
    <MobileOverviewSection translations={translations} />
  </Only>
);

const DesktopOverviewSection = ({ translations }: TableProps) => (
  <div className="flex items-center gap-6 px-4 py-11 lg:py-[27.5px]">
    <div className="flex flex-1 flex-col items-center gap-2">
      <Image alt="" className="size-[100px]" quality={25} src={FalconsLogo} />
      <p className="text-subtitle text-dark-default">team falcons</p>
    </div>
    <div className="-ms-5 w-px self-stretch bg-[#D9D9D9] lg:ms-5" />
    <div className="flex flex-1 flex-col gap-4">
      <div>
        <div className="flex flex-col gap-1">
          <p className="text-button-default text-radial-gold">{translations?.['firstPlace'] ?? '1st place'}</p>
          <p className="font-primary text-radial-gold text-[64px] leading-[1] font-bold">5,665</p>
        </div>
        <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
          {translations?.['ccPoints'] ?? 'cc points'}
        </p>
      </div>
      <div className="flex flex-col gap-2">
        <p className="text-button-big text-radial-gold">$7,000,00</p>
        <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
          {translations?.['won'] ?? 'won'}
        </p>
      </div>
    </div>
  </div>
);

const MobileOverviewSection = ({ translations }: TableProps) => (
  <div className="flex flex-col gap-4 px-6 pt-6 pb-4">
    <div className="flex flex-col items-center gap-1">
      <Image alt="" className="size-[100px]" quality={25} src={FalconsLogo} />
      <p className="text-subtitle text-dark-default">team falcons</p>
      <p className="text-button-default text-radial-gold">{translations?.['firstPlace'] ?? '1st place'}</p>
    </div>
    <div className="flex justify-around gap-4">
      <div className="flex flex-col items-center">
        <p className="font-primary text-radial-gold text-2xl leading-[1] font-bold">5,665</p>
        <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
          {translations?.['ccPoints'] ?? 'cc points'}
        </p>
      </div>
      <div className="flex flex-col items-center">
        <p className="font-primary text-radial-gold text-2xl leading-[1] font-bold">$7,000,00</p>
        <p className="font-primary text-gray-dark text-[10px] leading-[1] font-bold uppercase">
          {translations?.['won'] ?? 'won'}
        </p>
      </div>
    </div>
  </div>
);
