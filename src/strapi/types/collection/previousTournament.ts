import { StrapiBase } from '../base';
import { DynamicZoneBlock } from '../block';
import { HeroClubChampionOverviewType } from '../hero';
import { MediaType } from '../media';
import { SeoType } from '../shared/seo';

export interface PreviousTournamentPageType extends StrapiBase {
  slug: string | null;
  backgroundMedia: MediaType | null;
  navigationLabel: string | null;
  hero: HeroClubChampionOverviewType | null;
  blocks: DynamicZoneBlock[];
  seo: SeoType | null;
}
