import Image from 'next/image';

import { MediaType } from '@/strapi/types/media';
import { getMediaUrl } from '@/strapi/utils/getMediaUrl';

type ImageProps = React.ComponentProps<typeof Image>;

type Props = Omit<ImageProps, 'src' | 'alt'> & {
  alt?: string;
  image: MediaType;
  isFullUrl?: boolean;
};

export const StrapiImage = ({ image, alt, quality, isFullUrl, width, height, ...rest }: Props) => {
  return (
    <Image
      alt={alt ?? image.alternativeText ?? ''}
      height={height ?? image.height ?? 500}
      quality={quality ?? 100}
      src={getMediaUrl(image.url, isFullUrl)}
      width={width ?? image.width ?? 500}
      {...rest}
    />
  );
};
