'use client';

import { useToggle } from 'usehooks-ts';

import { fetchAuthSession } from '@/services/auth/cognito';
import { useInvalidateCurrentUserQuery } from '@/services/auth/hooks';
import { SocialProvider, UserIdentity } from '@/services/auth/types';
import { showErrorToast, showSuccessToast } from '@/ui/components/Toast';

import { socialProviderIconMap } from '../../../../_components/SocialLoginSection';
import { AccountButtonBase } from './AccountButtonBase';
import { AccountsListBase, AccountsListProps } from './AccountsListBase';

export const LinkedAccountsList = ({ user, title, buttonsCopy, translations }: AccountsListProps) => {
  const [isLoading, toggleIsLoading] = useToggle(false);
  const invalidateCurrentUser = useInvalidateCurrentUserQuery();

  const linkedAccounts = Object.values(SocialProvider).filter((p) =>
    user.identities?.some((id) => id.providerName.includes(p)),
  );

  if (linkedAccounts.length === 0) {
    return null;
  }

  const buttons = linkedAccounts.map((provider) => {
    const identity = user.identities?.find((id) => id.providerName.includes(provider)) as UserIdentity;
    const buttonCopy = buttonsCopy.find(({ type }) => type === provider);

    async function unlinkAccount() {
      const { tokens } = await fetchAuthSession({ forceRefresh: true });
      const unlinkData = { providerName: identity.providerName, token: tokens?.idToken?.toString() };

      toggleIsLoading();

      try {
        const res = await fetch('/api/accounts/unlink', { method: 'POST', body: JSON.stringify(unlinkData) });

        if (res.status !== 200) {
          const body = await res.json();
          throw new Error(body.message);
        }

        showSuccessToast({ title: translations?.['accountUnlinkedToastMessage'] ?? 'Account unlinked successfully!' });
        await invalidateCurrentUser();
      } catch (error: any) {
        showErrorToast({ title: 'Unlink account error!', description: error.message });
      } finally {
        toggleIsLoading();
      }
    }

    return (
      <AccountButtonBase
        icon={socialProviderIconMap[provider]}
        isDisabled={isLoading}
        isLinkedAccount
        key={provider}
        primaryText={identity?.userId as string}
        secondaryText={buttonCopy?.linkedAccountSubtitle}
        onClick={unlinkAccount}
      />
    );
  });

  return <AccountsListBase title={title}>{buttons}</AccountsListBase>;
};
