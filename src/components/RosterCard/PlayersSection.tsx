import { JsonFieldType } from '@/strapi/types/helper';

import { RosterPlayer } from './types';

interface Props {
  players: RosterPlayer[];
  translations: JsonFieldType;
}

export const PlayerSection = ({ players, translations }: Props) => {
  const regularPlayers = players.filter((p) => !p.isCoach);
  const coaches = players.filter((p) => p.isCoach);

  return (
    <div className="flex flex-col gap-4 rounded-lg bg-white p-4">
      {regularPlayers.length !== 0 && <PlayersList players={regularPlayers} text={translations.players ?? 'players'} />}
      {coaches.length !== 0 && <PlayersList players={coaches} text={translations.coach ?? 'coach'} />}
    </div>
  );
};

const PlayersList = ({ players, text }: { players: RosterPlayer[]; text: string }) => {
  return (
    <div className="flex flex-col gap-1">
      <p className="font-primary bg-dark-default w-fit rounded-[3px] px-1 py-0.5 text-[9px] leading-[1.1] font-bold text-white uppercase">
        {text}
      </p>
      <div className="divide flex flex-col gap-1">
        {players.map((p) => (
          <>
            <p className="font-base text-dark-default py-px text-[13px] leading-none font-semibold" key={p.name}>
              {p.name}
            </p>
            <div className="bg-gray-easy h-px w-full last:hidden" />
          </>
        ))}
      </div>
    </div>
  );
};
