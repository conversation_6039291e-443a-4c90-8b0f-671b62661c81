import { StrapiImage } from '@/components/StrapiImage';
import { MediaType } from '@/strapi/types/media';

export const CardLogo = ({ logo }: { logo: MediaType | null }) => {
  if (!logo) {
    return null;
  }
  return (
    <div className="box-content w-full max-w-[94px] px-2 py-[7px] md:max-w-[259px] md:px-5 md:py-[18px]">
      <StrapiImage className="aspect-[4.8] w-full object-contain" image={logo} />
    </div>
  );
};
