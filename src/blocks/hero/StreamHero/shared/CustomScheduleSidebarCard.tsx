import clsx from 'clsx';
import { format } from 'date-fns';

import { cn } from '@/utils/cn';

import { useDateLocale } from '../../../../hooks/i18n/useDateLocale';
import { CustomStreamSchedule } from '../../../../strapi/types/collection/stream';
import { useCountdown } from './hooks/useCountdown';

interface CustomScheduleCardProps {
  schedule: CustomStreamSchedule;
  customStreamTitle: string | null;
}

export const CustomScheduleCard = ({ schedule, customStreamTitle }: CustomScheduleCardProps) => {
  const locale = useDateLocale();
  const { isLive, isCompleted } = useCountdown(schedule.startTime, schedule.endTime);

  return (
    <div
      className={clsx('relative flex min-w-[200px] flex-col gap-[5px] rounded-sm px-[14px] py-2.5', {
        'bg-white-dirty': isLive,
        'opacity-40': isCompleted,
      })}
    >
      {isLive && <div className="bg-red-accent absolute inset-x-0 top-0 h-[3px] rounded-t-full lg:hidden" />}
      {isLive && (
        <div className="absolute inset-y-0 start-0 max-lg:hidden">
          <div
            className="bg-red-accent absolute inset-y-0 end-full my-auto h-3 w-1.5"
            style={{ clipPath: 'polygon(100% 0, 0 50%, 100% 100%)' }}
          />
          <div className="bg-red-accent h-full w-[3px] rounded-l-full" />
        </div>
      )}
      <div className="flex items-center gap-2">
        {schedule.startTime && (
          <Badge
            className="bg-dark-default whitespace-nowrap"
            text={schedule.startTime ? format(new Date(schedule.startTime), 'hh:mm a', { locale }) : ''}
          />
        )}
        {customStreamTitle && (
          <span className="font-base h-fit self-center text-[10px] font-extrabold">{customStreamTitle}</span>
        )}
        {schedule.language && <Badge className="bg-gray-dark" text={schedule.language} />}
      </div>
      {schedule.title && <div className="font-primary text-[13px] leading-[110%] font-bold">{schedule.title}</div>}
      {schedule.tag && <div className="font-base text-gray-dark text-[10px] font-extrabold">@{schedule.tag}</div>}
    </div>
  );
};

const Badge = ({ text, className }: { text?: string | null; className?: string }) => {
  return (
    <div
      className={cn(
        'font-primary flex items-center justify-center rounded-[1px] p-0.5 text-[8px] font-bold text-white uppercase',
        className,
      )}
    >
      <p className="leading-none">{text}</p>
    </div>
  );
};
