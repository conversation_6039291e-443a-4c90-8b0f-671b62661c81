import { useEffect } from 'react';

interface Options {
  shouldPreserveGutter?: boolean;
}

export function useLockBodyScroll(shouldLock: boolean, options?: Options) {
  const { shouldPreserveGutter = false } = options ?? {};

  useEffect(() => {
    const html = document.documentElement;
    const disableScrollStyles = shouldPreserveGutter
      ? ['overflow-hidden', 'preserve-scrollbar-gutter']
      : ['overflow-hidden'];

    if (shouldLock) {
      html.classList.add(...disableScrollStyles);
    } else {
      html.classList.remove(...disableScrollStyles);
    }

    return () => {
      html.classList.remove(...disableScrollStyles);
    };
  }, [shouldLock, shouldPreserveGutter]);
}
