import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { isDateInRange } from '@/app/[locale]/(app)/schedule/_components/utils';
import { BlockRenderer } from '@/blocks/BlockRenderer';
import InteractiveMap from '@/components/InteractiveMap/InteractiveMap';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { FestivalFilterProvider } from '@/context/FestivalFilterContext';
import { getVenueListData } from '@/strapi/api/collection/festival';
import { getFestivalPageData, getFestivalsPageSeo } from '@/strapi/api/single/festival';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import { FestivalFilterBlockType } from '@/strapi/types/festivalBlock';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const seo = await getFestivalsPageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | Festivals',
    description: seo?.metaDescription ?? 'Esports World Cup | Festivals',
  };
}

export default async function FestivalPage({ params }: PageProps) {
  const locale = (await params).locale;
  const siteConfig = await getSiteConfig(locale);
  const festivalPageData = await getFestivalPageData(locale);
  const venueData = await getVenueListData(locale);

  if (!festivalPageData) {
    return notFound();
  }

  const { pageData } = festivalPageData;
  const { blocks } = pageData;

  // Find the filter block to get tournamentStart and tournamentEnd
  const filterBlock = blocks.find((b: any) => b.__component?.includes('festival-filter-block')) as
    | FestivalFilterBlockType
    | undefined;
  const tournamentStart = filterBlock?.tournamentStart;
  const tournamentEnd = filterBlock?.tournamentEnd;

  // Compute preselectedDate
  let preselectedDate: string | null = null;
  if (tournamentStart && tournamentEnd && isDateInRange(new Date(), tournamentStart, tournamentEnd)) {
    preselectedDate = new Date().toISOString().slice(0, 10);
  }

  return (
    <>
      <FestivalFilterProvider initialDate={preselectedDate}>
        <div className="mb-32 h-[75vh] md:mb-0 md:h-full">
          {festivalPageData?.pageData?.showInteractiveMap && venueData && (
            <InteractiveMap
              showRibbon={festivalPageData.pageData.interactiveMapShowRibbon}
              translations={siteConfig?.translations}
              venueData={venueData}
            />
          )}
        </div>
        <div className="h-full px-4 py-15 lg:px-8">
          <div className="relative mx-auto flex max-w-[1400px] flex-col gap-5 md:gap-8">
            {blocks.map((b) => (
              <BlockRenderer {...b} {...siteConfig} key={`${b.__component}-${b.id}`} />
            ))}
          </div>
        </div>
      </FestivalFilterProvider>
    </>
  );
}
