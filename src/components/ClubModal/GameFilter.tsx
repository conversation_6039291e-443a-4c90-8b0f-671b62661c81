'use client';

import clsx from 'clsx';

import { GameWithCompetitions } from '@/strapi/api/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';
import { Only } from '@/ui/components/Only';
import { SingleSelect } from '@/ui/components/SelectInput/SingleSelect';

import { StrapiImage } from '../StrapiImage';

interface Props {
  selectedGameId: string;
  games: GameWithCompetitions[];
  onSelect: (id: string) => void;
  translations: JsonFieldType;
}

export const GameFilter = (props: Props) => {
  const { games, onSelect, translations, selectedGameId } = props;
  return (
    <Only fallback={<GameFilterDropdown {...props} />} for="mdAndAbove">
      <div className="flex flex-col gap-4">
        <h5 className="text-h5 capitalize">{translations.gameFilter ?? 'Game Filter'}</h5>
        <div className="flex flex-wrap gap-4">
          {games.map((g) => (
            <GridItem
              {...g}
              isSelected={g.documentId === selectedGameId}
              key={g.slug}
              onClick={() => onSelect(g.documentId === selectedGameId ? '' : g.documentId)}
            />
          ))}
        </div>
      </div>
    </Only>
  );
};

type GridItemProps = GameWithCompetitions & { onClick: () => void; isSelected: boolean };

const GridItem = ({ logoDark, schedulePopupLogo, isSelected, onClick }: GridItemProps) => {
  return (
    <div
      className={clsx(
        'group flex justify-center rounded-lg px-2 py-1 shadow-[0px_12px_16px_0px_#0000000F] transition-colors',
        !isSelected ? 'cursor-pointer bg-white' : 'bg-dark-default',
      )}
      onClick={onClick}
    >
      {logoDark && !isSelected && (
        <StrapiImage
          alt=""
          className="aspect-[3] w-[99px] object-contain transition-transform group-hover:scale-110"
          image={logoDark}
        />
      )}
      {schedulePopupLogo && isSelected ? (
        <StrapiImage
          alt=""
          className="aspect-[3] w-[99px] object-contain transition-transform group-hover:scale-110"
          image={schedulePopupLogo}
        />
      ) : null}
    </div>
  );
};

export const GameFilterDropdown = ({ selectedGameId, games, onSelect, translations }: Props) => {
  const items = games.map((g) => ({ label: g.title ?? '', value: g.documentId }));

  return (
    <SingleSelect
      options={items}
      placeholder={translations['clubModalFilter.filterTournaments'] ?? 'Filter Tournaments'}
      value={selectedGameId}
      onSelect={onSelect}
    />
  );
};
