import Image from 'next/image';
import { FaEyeSlash } from 'react-icons/fa6';
import { LiaCrownSolid } from 'react-icons/lia';

import { RankDiffPill } from '@/app/[locale]/(app)/club-championship-ranking/_components/HelperComponents/RankDiffPill';
import { ClubData } from '@/app/[locale]/(app)/club-championship-ranking/_utils/types';
import { LocalizedLink } from '@/components/LocalizedLink';
import { constructImageUrl } from '@/services/graphql/utils';
import { JsonFieldType } from '@/strapi/types/helper';

export const CCRankingsBar = ({
  onClose,
  todaysCCRankings,
  translations,
}: {
  onClose: () => void;
  translations?: JsonFieldType;
  todaysCCRankings: ClubData[] | null | undefined;
}) => {
  return (
    <div className="absolute end-6 z-100 flex h-full w-[calc(100vw-170px)] items-center rounded-xl bg-[#2E2E2EE5] shadow-[4px_8px_16px_0px_#00000014] backdrop-blur-lg">
      <div className="flex h-full items-center gap-1.5 rounded-tl-xl rounded-bl-xl bg-gradient-to-r from-[#987C4B] to-transparent px-4 text-white">
        <LiaCrownSolid className="size-6" />
        <span className="font-primary max-w-[113px] min-w-[113px] text-[10px] leading-none font-bold uppercase">
          {translations?.['ccRanking'] ?? 'club championship ranking'}
        </span>
      </div>
      {todaysCCRankings && (
        <div className="gap flex h-full grow gap-[5px] overflow-hidden py-[5px]">
          {todaysCCRankings.slice(0, 20).map((club) => (
            <div
              className="box-content flex min-w-[191px] items-center justify-between gap-3 rounded-sm bg-white/[8%] px-3"
              key={club.club.id}
            >
              <div className="flex items-center gap-1">
                <div className="font-primary flex size-[17px] shrink-0 items-center justify-center rounded-xs bg-white/15 text-[12px] font-bold text-white">
                  {club.rank}
                </div>
                <div className="flex grow items-center gap-0.5">
                  <Image
                    alt={club.club.name ?? ''}
                    className="size-6"
                    height={24}
                    src={constructImageUrl(club.images, 'logo_transparent_blackbg') ?? ''}
                    width={24}
                  />

                  <div className="font-base flex max-w-[109px] grow flex-col gap-px truncate font-extrabold uppercase">
                    <span className="truncate text-[12px] leading-[100%] text-white">{club.club.name}</span>
                    <span className="text-gray-dark text-[9px] leading-[100%]">
                      {club.prizes.XTS} {translations?.points ?? 'points'}
                    </span>
                  </div>
                </div>
              </div>
              <RankDiffPill diff={club.rankDiff} />
            </div>
          ))}
        </div>
      )}
      <div className="absolute inset-y-[5px] end-[5px] flex gap-[5px]">
        <LocalizedLink
          brazeEventProperties={{
            button_name: 'view ranking',
            location: 'club championship rankings bar',
          }}
          className="text-button-default rounded-lg bg-[radial-gradient(122.78%_179%_at_50.21%_0%,_#F2C575_0%,_#987C4B_40.5%,_#4E442D_92.88%)] px-6 py-4 text-white uppercase"
          href="/club-championship-ranking"
        >
          {translations?.['viewRanking'] ?? 'view ranking'}
        </LocalizedLink>
        <button
          className="flex aspect-square h-full cursor-pointer items-center justify-center rounded-lg bg-white"
          onClick={onClose}
        >
          <FaEyeSlash className="text-dark-default size-5" />
        </button>
      </div>
    </div>
  );
};
