'use client';

import { useRouter } from 'next/navigation';
import { FormProvider } from 'react-hook-form';

import { FormPasswordInput } from '@/components/form/FormPasswordInput';
import { FormTextInput } from '@/components/form/FormTextInput';
import { confirmResetPassword } from '@/services/auth/cognito';
import { ResetPasswordPageData } from '@/strapi/api/single/authPages/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { showErrorToast, showSuccessToast } from '@/ui/components/Toast';

import { ResetPasswordFormValues, useResetPasswordForm } from './hooks';

type Props = ResetPasswordPageData & {
  translations?: JsonFieldType | null;
};

export const ResetPasswordSection = ({
  emailFieldPlaceholder,
  confirmationCodeFieldPlaceholder,
  passwordFieldPlaceholder,
  passwordConfirmFieldPlaceholder,
  resetButtonText,
  translations,
}: Props) => {
  const router = useRouter();

  const formMethods = useResetPasswordForm(translations);
  const { handleSubmit, formState } = formMethods;

  async function sendResetEmail({ password, ...rest }: ResetPasswordFormValues) {
    try {
      await confirmResetPassword({ newPassword: password, ...rest });
      showSuccessToast({ title: translations?.['passwordUpdatedToastMessage'] ?? 'Password updated successfully!' });
      router.push('login');
    } catch (error: any) {
      showErrorToast({ title: 'Confirm reset password error!', description: error.message });
    }
  }

  const { isValid, isSubmitting } = formState;
  return (
    <FormProvider {...formMethods}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(sendResetEmail)}>
        <FormTextInput name="username" placeholder={emailFieldPlaceholder ?? 'Your email address'} />
        <FormTextInput
          name="confirmationCode"
          placeholder={confirmationCodeFieldPlaceholder ?? 'Confirmation code sent to your email'}
        />
        <FormPasswordInput name="password" placeholder={passwordFieldPlaceholder ?? 'Your new password'} />
        <FormPasswordInput name="passwordConfirm" placeholder={passwordConfirmFieldPlaceholder ?? 'Confirm password'} />
        <Button
          brazeEventProperties={{
            button_name: `Reset Password Button`,
            location: `Reset Password Modal`,
          }}
          isDisabled={!isValid || isSubmitting}
          isFullWidth
          text={resetButtonText ?? 'reset password'}
          type="submit"
        />
      </form>
    </FormProvider>
  );
};
