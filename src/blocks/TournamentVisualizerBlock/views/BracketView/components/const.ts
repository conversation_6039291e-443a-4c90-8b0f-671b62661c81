/*
| Element                       | Mobile (px) | Desktop (px) |
|-------------------------------|-------------|--------------|
| Round Column Header height    | 17          | 17           |
| Bracket height                | 108         | 176          |
| Bracket spacing               | 16          | 16           |
*/

export const ROUND_HEADER_HEIGHT_STYLE = 'h-[17px]';
export const BRACKET_HEIGHT = 'md:h-[176px] h-[108px]';

/**
 * Returns the height classes for the bracket wrapper based on the given tier.
 * Bracket wrappers are used to centrally align brackets in each successive round, to show a "funnelling" type of flow.
 *
 * The calculation for each tier is:
 * - Tier 1: height for 2 brackets + 1 spacing
 * - Tier 2: height for 4 brackets + 3 spacings
 * - Tier 3: height for 8 brackets + 7 spacings
 *
 * Tiers beyond 3 (Ro16) are not expected.
 *
 * @param tier - The tier of the bracket (1, 2, or 3).
 * @returns A string with Tailwind CSS classes for the calculated height, or an empty string for unsupported tiers.
 */
export function getBracketWrapperHeightForTier(tier: number) {
  switch (tier) {
    case 1:
      return 'h-[calc(108px*2+16px)] md:h-[calc(176px*2+16px)]';
    case 2:
      return 'h-[calc(108px*4+16px*3)] md:h-[calc(176px*4+16px*3)]';
    case 3:
      return 'h-[calc(108px*8+16px*7)] md:h-[calc(176px*8+16px*7)]';
    default:
      return '';
  }
}

/**
 * Returns the appropriate height value for the connector wrapper based on the given tournament tier.
 * The connector should connect two leading brackets from previous round, and end in the middle of target bracket in next round.
 
* The calculation for each tier is:
 * - Tier 1: height for 1 brackets + 1 spacing
 * - Tier 2: two times height for 1 brackets + 1 spacing
 * - Tier 3: four times height for 1 brackets + 1 spacing
 *
 * Tiers beyond 3 (Ro16) are not expected.
 * 
 * @param tier - The tournament tier (1, 2, or 3) for which to get the connector wrapper height.
 * @returns A string with Tailwind CSS classes for the calculated height, or an empty string for unsupported tiers.
 */
export function getConnectorWrapperHeightForTier(tier: number) {
  switch (tier) {
    case 1:
      return 'md:h-[calc(176px+16px)] h-[calc(108px+16px)]';
    case 2:
      return 'md:h-[calc((176px+16px)*2)] h-[calc((108px+16px)*2)]';
    case 3:
      return 'md:h-[calc((176px+16px)*4)] h-[calc((108px+16px)*4)]';
    default:
      return '';
  }
}
