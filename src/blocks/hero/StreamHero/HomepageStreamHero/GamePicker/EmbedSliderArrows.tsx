'use client';

import { ReactNode, RefObject } from 'react';

import { cn } from '@/utils/cn';

import { useScrollArrowVisibility } from './hooks';

interface EmbedSliderArrowsProps {
  children: ReactNode;
  scrollRef: RefObject<HTMLElement | null>;
  className?: string;
  scrollAmount?: number;
}

export function EmbedSliderArrows({ children, scrollRef, className, scrollAmount = 200 }: EmbedSliderArrowsProps) {
  const { showLeft, showRight } = useScrollArrowVisibility(scrollRef);

  const handleScroll = (direction: 'left' | 'right') => {
    if (!scrollRef.current) return;
    scrollRef.current.scrollBy({
      left: direction === 'left' ? -scrollAmount : scrollAmount,
      behavior: 'smooth',
    });
  };

  return (
    <div className={cn('relative', className)}>
      {showLeft && (
        <div className="pointer-events-none absolute top-0 left-0 z-10 h-full w-10 bg-[linear-gradient(270deg,rgba(21,21,21,0)_0%,#151515_70%)]" />
      )}
      {showRight && (
        <div className="pointer-events-none absolute top-0 right-0 z-10 h-full w-10 bg-[linear-gradient(90deg,rgba(21,21,21,0)_0%,#151515_70%)]" />
      )}

      <button
        className={cn(
          'absolute top-1/2 left-2 z-20 hidden -translate-y-1/2 cursor-pointer rounded-full bg-black p-2 transition-all duration-300 ease-out md:block',
          showLeft ? 'pointer-events-auto scale-100 opacity-100' : 'pointer-events-none scale-95 opacity-0',
          'hover:scale-110',
        )}
        type="button"
        onClick={() => handleScroll('left')}
      >
        <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" strokeWidth={3} viewBox="0 0 24 24">
          <path d="M15 18l-6-6 6-6" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </button>

      <button
        className={cn(
          'absolute top-1/2 right-2 z-20 hidden -translate-y-1/2 cursor-pointer rounded-full bg-black p-2 transition-all duration-300 ease-out md:block',
          showRight ? 'pointer-events-auto scale-100 opacity-100' : 'pointer-events-none scale-95 opacity-0',
          'hover:scale-110',
        )}
        type="button"
        onClick={() => handleScroll('right')}
      >
        <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" strokeWidth={3} viewBox="0 0 24 24">
          <path d="M9 6l6 6-6 6" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      </button>

      {children}
    </div>
  );
}
