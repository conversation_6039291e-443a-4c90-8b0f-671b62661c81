import { LocalMatchStatus } from '@/utils/matchSeries';

import { Contestant, Group, Phase } from '../types';

export interface FfaPhase extends Phase {
  groups: FfaGroup[];
}

export interface FfaGroup extends Group {
  matches: FfaMatch[];
}

export interface FfaMatch {
  id: string;
  title: string;
  startTime?: string;
  status?: LocalMatchStatus;
  contestants: FfaContestant[];
}

export type FfaContestant = Omit<Contestant, 'score'> & {
  rank: number | null;
  kills: number;
  placement: number;
  points: number;
};
