import { useMemo } from 'react';

import { Tournament } from '@/services/graphql/types/tournament';
import { GameWithCompetitions } from '@/strapi/api/collection/game';

/**
 * Custom React hook to retrieve tournaments and games associated with a specific club.
 *
 * Given a club ID, a list of all tournaments, and a list of all games, this hook computes:
 * - The tournaments in which the club is participating (based on contestants).
 * - The games in which the club is playing (based on tournament IDs).
 * - The tournaments associated with those games, ensuring all relevant tournaments are included,
 *   even if the club has been eliminated from some.
 *
 * @param clubId - The ID of the club to filter tournaments and games for. If null, returns empty arrays.
 * @param allTournaments - Array of all available tournaments.
 * @param allGames - Array of all available games.
 * @returns An object containing:
 *   - `tournaments`: Array of tournaments associated with the club's games.
 *   - `games`: Array of games in which the club is participating.
 */
export function useClubTournamentsAndGames(
  clubId: string | null,
  allTournaments: Tournament[],
  allGames: GameWithCompetitions[],
) {
  const tournamentsAndGames = useMemo(() => {
    // get tournaments the club is playing in
    const clubTournaments = clubId
      ? (allTournaments.filter((t) => t.contestants?.find((c) => c.team?.club.id === clubId)) ?? [])
      : [];

    // filter games that the club is playing based on tournament ids
    const clubGames =
      clubTournaments.length !== 0
        ? allGames.filter((g) => {
            return g.competitionSlugs.some((cs) => clubTournaments.some((t) => t.id === cs.competitionId));
          })
        : [];

    // if club is eliminated it will not be present in all game tournaments
    // filter again to get every tournament for games
    const allGameTournaments = allTournaments.filter((t) =>
      clubGames.some((g) => g.competitionSlugs.some((cs) => cs.competitionId === t.id)),
    );

    return { tournaments: allGameTournaments, games: clubGames };
  }, [allGames, allTournaments, clubId]);

  return tournamentsAndGames;
}
