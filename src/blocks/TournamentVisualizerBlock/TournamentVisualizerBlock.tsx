'use client';

import { useMemo } from 'react';

import { useCompetitionSlugs } from '@/app/[locale]/(app)/competitions/[slug]/_components/CompetitionSlugsProvider';
import { useGameMatchSeriesData } from '@/services/graphql/hooks';
import { TournamentVisualizerBlockType } from '@/strapi/types/gamePageBlock';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { Navigation } from './NavigationComponent';
import { useSelectedPhase } from './NavigationComponent/hooks';
import { getGroupedMatches } from './utils';
import { ViewRenderer } from './views';
import { Loader } from './views/Loader';

export const TournamentVisualizerBlock = ({
  section,
  apiTranslations,
  translations,
}: TournamentVisualizerBlockType) => {
  const competitionSlugs = useCompetitionSlugs();
  const { data, error, loading } = useGameMatchSeriesData();

  const initialPhases = useMemo(
    () =>
      competitionSlugs.map((s) => ({
        id: s.competitionId,
        name: apiTranslations?.[s.competitionId] ?? s.competitionName,
      })),
    [apiTranslations, competitionSlugs],
  );

  const phases = useMemo(
    () =>
      getGroupedMatches({
        matchSeries: data?.matchSeries.items,
        initialPhases,
        config: { apiTranslations: apiTranslations ?? {}, translations: translations ?? {} },
      }),
    [apiTranslations, data?.matchSeries.items, initialPhases, translations],
  );

  const { selectedPhaseId, setSelectedPhaseId } = useSelectedPhase(phases);
  const selectedPhase = phases?.find((p) => p.id === selectedPhaseId);

  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-col gap-0.5">
        <Navigation
          items={phases}
          selectedItemId={selectedPhaseId as string}
          type="main"
          onSelect={setSelectedPhaseId}
        />
        {data && selectedPhase && (
          <ViewRenderer key={selectedPhase.id} phase={selectedPhase} translations={translations ?? {}} />
        )}
        {loading && <Loader />}
        {error && <div>error: {error.message}</div>}
      </div>
    </BlockSectionWrapper>
  );
};
