import { Metadata } from 'next';
import Image from 'next/image';
import { notFound, redirect } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { UserLoggedInRedirect } from '@/services/auth/components/UserLoggedInRedirect';
import { getConfirmEmailPageData } from '@/strapi/api/single/authPages';
import BackgroundImage from '@/ui/assets/images/abstract-background-3.png';

import { AuthModal } from '../_components/AuthModal';
import { ResendCodeSection, VerifyEmailSection } from './_components';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const { locale } = await params;
  const data = await getConfirmEmailPageData(locale);

  return {
    title: data?.seo?.metaTitle ?? 'Esports World Cup | Confirm Email',
    description: data?.seo?.metaDescription ?? 'This is Esports World Cup | Confirm Email',
  };
}

export default async function ConfirmEmailPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const { username } = await searchParams;
  const data = await getConfirmEmailPageData(locale);

  if (!data) {
    return notFound();
  }

  if (typeof username !== 'string' || username.length === 0) {
    redirect(`/${locale}/login`);
  }

  const { title, subtitle, footerText } = data;
  const decodedUsername = decodeURIComponent(username);
  return (
    <div className="relative flex min-h-screen w-full items-center justify-center">
      <UserLoggedInRedirect />
      <Image alt="" className="absolute inset-0 h-full w-full object-cover" quality={100} src={BackgroundImage} />
      <AuthModal footer={footerText} subtitle={subtitle} title={title}>
        <VerifyEmailSection username={decodedUsername} {...data} />
        <ResendCodeSection username={decodedUsername} {...data} />
      </AuthModal>
    </div>
  );
}
