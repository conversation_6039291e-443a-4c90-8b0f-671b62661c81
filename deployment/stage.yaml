applicationName: "ewc-frontend"

deployment:
  terminationGracePeriodSeconds: 60
  containerSecurityContext:
    readOnlyRootFilesystem: false
    runAsNonRoot: false
  env:
    ENV:
      value: "${ENV}"
    NEXT_PUBLIC_STRAPI_URL:
      value: "${NEXT_PUBLIC_STRAPI_URL}"
    STRAPI_API_TOKEN:
      value: "${STRAPI_API_TOKEN}"
    NEXT_PUBLIC_CDN_BASE_URL:
      value: "${NEXT_PUBLIC_CDN_BASE_URL}"
    REVALIDATE_TIME_IN_SECONDS:
      value: "60"
    BASIC_AUTH_CREDENTIALS:
      value: "${BASIC_AUTH_CREDENTIALS}"
    NEXT_PUBLIC_APP_BASE_URL:
      value: "${NEXT_PUBLIC_APP_BASE_URL}"
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN:
      value: "${NEXT_PUBLIC_AWS_COGNITO_DOMAIN}"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_ID:
      value: "${NEXT_PUBLIC_AWS_COGNITO_USER_POOL_ID}"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_CLIENT_ID:
      value: "${NEXT_PUBLIC_AWS_COGNITO_USER_POOL_CLIENT_ID}"
    AWS_REGION: 
      value: "${AWS_REGION}"
    AWS_ACCESS_KEY_ID: 
      value: "${AWS_ACCESS_KEY_ID}"
    AWS_SECRET_ACCESS_KEY: 
      value: "${AWS_SECRET_ACCESS_KEY}"
    NEXT_PUBLIC_BRAZE_API_KEY: 
      value: "${NEXT_PUBLIC_BRAZE_API_KEY}"
    NEXT_PUBLIC_BRAZE_BASE_URL: 
      value: "${NEXT_PUBLIC_BRAZE_BASE_URL}"
    NEXT_PUBLIC_GRAPH_API_URL: 
      value: "${NEXT_PUBLIC_GRAPH_API_URL}"
    NEXT_PUBLIC_FANTASY_BASE_URL: 
      value: "${NEXT_PUBLIC_FANTASY_BASE_URL}"
  image:
    repository: "${CI_REGISTRY_IMAGE}"
    tag: "${CI_COMMIT_SHA}"
  ports:
    - containerPort: 3000
      name: http
  readinessProbe:
    enabled: true
    httpGet:
      path: /api/health
      port: http
  livenessProbe:
    enabled: true
    httpGet:
      path: /api/health
      port: http
  resources:
    limits:
      cpu: null
      memory: 1024Mi
    requests:
      cpu: 0.5
      memory: 512Mi
  topologySpreadConstraints:
    - labelSelector:
        matchLabels:
          app.kubernetes.io/name: ewc-frontend
      maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: DoNotSchedule
    - labelSelector:
        matchLabels:
          app.kubernetes.io/name: ewc-frontend
      maxSkew: 1
      topologyKey: kubernetes.io/hostname
      whenUnsatisfiable: ScheduleAnyway

autoscaling:
  enabled: true
  minReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75

pdb:
  enabled: true

service:
  ports:
    - port: 3000
      name: http
      protocol: TCP
      targetPort: 3000

ingress:
  enabled: true
  ingressClassName: "alb"
  annotations:
    "alb.ingress.kubernetes.io/load-balancer-name": "${CI_ENVIRONMENT_NAME}-ewc-frontend"
    "alb.ingress.kubernetes.io/scheme": "internet-facing"
    "alb.ingress.kubernetes.io/target-type": "ip"
    "alb.ingress.kubernetes.io/listen-ports": '[{"HTTPS":443}]'
    "alb.ingress.kubernetes.io/healthcheck-path": "/api/health"
    "alb.ingress.kubernetes.io/security-groups": "sg-00f79d00c6dfc25db"
    "alb.ingress.kubernetes.io/manage-backend-security-group-rules": "true"
  hosts:
    - host: "ewc-frontend.${CI_ENVIRONMENT_NAME}.ewcf.cw.esl.systems"
      paths:
        - path: /
          pathType: Prefix
