'use client';

import { useEffect, useRef } from 'react';

export function useScrollIntoViewOnSelect<K>(selectedId: K | null) {
  const itemRefs = useRef<Map<K, HTMLElement>>(new Map());
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (selectedId != null && scrollContainerRef.current) {
      const selectedEl = itemRefs.current.get(selectedId);
      const scrollContainer = scrollContainerRef.current;

      if (selectedEl && scrollContainer) {
        const elLeft = selectedEl.offsetLeft;
        const elRight = elLeft + selectedEl.offsetWidth;
        const containerLeft = scrollContainer.scrollLeft;
        const containerRight = containerLeft + scrollContainer.offsetWidth;

        const isOutOfView = elLeft < containerLeft || elRight > containerRight;

        if (isOutOfView) {
          const targetScrollLeft = elLeft - scrollContainer.offsetWidth / 2 + selectedEl.offsetWidth / 2;
          scrollContainer.scrollTo({ left: targetScrollLeft, behavior: 'smooth' });
        }
      }
    }
  }, [selectedId]);

  useEffect(() => {
    return () => {
      itemRefs.current.clear();
    };
  }, []);

  return {
    scrollContainerRef,
    itemRefs,
  };
}
