import { useCurrentLocale } from '.';

const enPluralRules = new Intl.PluralRules('en', { type: 'ordinal' });
const enOrdinalSuffixes: Partial<Record<Intl.LDMLPluralRule, string>> = {
  zero: 'th',
  one: 'st',
  two: 'nd',
  few: 'rd',
  other: 'th',
};

interface Options {
  arSimplified?: boolean;
}

export function useOrdinalFormatter(options?: Options) {
  const { arSimplified = false } = options ?? {};
  const locale = useCurrentLocale();

  return (number: number) => {
    if (locale === 'en') {
      const suffix = enOrdinalSuffixes[enPluralRules.select(number)];
      return `${number}${suffix}`;
    }

    if (locale === 'zh') {
      return `第${number}`;
    }

    if (arSimplified) {
      return number;
    }

    return toArabicOrdinal(number);
  };
}

const arabicUnitOrdinals = [
  '', // 0
  'الأوّل', // 1st
  'الثاني', // 2nd
  'الثالث', // 3rd
  'الرابع', // 4th
  'الخامس', // 5th
  'السادس', // 6th
  'السابع', // 7th
  'الثامن', // 8th
  'التاسع', // 9th
];

const arabicTensOrdinals = [
  '', // 0
  'العاشر', // 10th
  'العشرون', // 20th
  'الثلاثون', // 30th
  'الأربعون', // 40th
  'الخمسون', // 50th
  'الستون', // 60th
  'السبعون', // 70th
  'الثمانون', // 80th
  'التسعون', // 90th
  'المئة', // 100th
];

function toArabicOrdinal(n: number): string {
  if (n < 1 || n > 100) return String(n);
  if (n <= 9) return arabicUnitOrdinals[n];
  if (n === 100) return arabicTensOrdinals[10];

  // 11–19
  if (n < 20) {
    const unit = n - 10;
    return `ال${arabicUnitOrdinals[unit].replace('الأ', '')} عشر`;
  }

  // 20, 30, ..., 90
  if (n % 10 === 0) {
    return arabicTensOrdinals[n / 10];
  }

  // 21–99
  const unit = n % 10;
  const tens = Math.floor(n / 10);
  return `${arabicUnitOrdinals[unit]} و${arabicTensOrdinals[tens]}`;
}
