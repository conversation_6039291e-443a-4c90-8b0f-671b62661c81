export function getInitialTransform() {
  if (typeof window === 'undefined') return { scale: 1.2, positionX: -200, positionY: -150 };

  const width = window.innerWidth;
  const height = window.innerHeight;

  let positionY;

  if (height < 300) {
    positionY = -50;
  } else if (height < 400) {
    positionY = -80;
  } else if (height < 500) {
    positionY = -90;
  } else if (height < 600) {
    positionY = -90;
  } else if (height < 700) {
    positionY = -100;
  } else if (height < 800) {
    positionY = -110;
  } else if (height < 900) {
    positionY = -120;
  } else if (height < 1000) {
    positionY = -130;
  } else {
    positionY = -220;
  }

  let scale, positionX;

  if (width < 500) {
    scale = 1.45;
    positionX = -270;
  } else if (width < 1000) {
    scale = 1.25;
    positionX = -180;
  } else {
    scale = 1.2;
    positionX = -210;
  }

  return { scale, positionX, positionY };
}
