import { useQuery } from '@apollo/client';

import { GAME_DATA_QUERY } from '../queries';
import { GameDataQueryResponse } from '../types/gameData';
import { QueryOptions } from './matchSeries';
import { useGameDataSubscription } from './subscribe/gameData';

export function useGameStatsData(matchId: string | null, tournamentId: string, options?: QueryOptions) {
  const { isDisabled = false, isSubscriptionDisabled = false } = options ?? {};
  const isMatchIdEmpty = !matchId;

  const query = useQuery<GameDataQueryResponse>(GAME_DATA_QUERY, {
    variables: { matchId },
    skip: isDisabled || isMatchIdEmpty,
    errorPolicy: 'ignore',
  });

  useGameDataSubscription({
    enabled: !!query.data && !isMatchIdEmpty && !isDisabled && !isSubscriptionDisabled,
    tournamentIds: [tournamentId],
    matchId: matchId as string,
  });

  return query;
}
