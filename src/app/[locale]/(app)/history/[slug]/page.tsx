import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { BlockRenderer } from '@/blocks/BlockRenderer';
import { ClubChampionOverviewHero } from '@/blocks/hero/ClubChampionOverviewHero';
import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import {
  getPreviousTournamentPageData,
  getPreviousTournamentPageSeo,
} from '@/strapi/api/collection/previousTournament';
import { redirectAccordingToConfig } from '@/strapi/api/collection/redirect';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import { MediaType } from '@/strapi/types/media';
import { isVideoMedia } from '@/utils/media';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale<{ slug: string }>): Promise<Metadata> {
  const { locale, slug } = await params;
  const seo = await getPreviousTournamentPageSeo(locale, slug);
  return {
    title: seo?.metaTitle ?? 'Esports World Cup',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function Page({ params }: PageProps<{ slug: string }>) {
  const { locale, slug } = await params;
  await redirectAccordingToConfig(`/history/${slug}`, locale);

  const pageData = await getPreviousTournamentPageData(locale, slug);
  if (!pageData) {
    return notFound();
  }

  const siteConfig = await getSiteConfig(locale);
  const { blocks, backgroundMedia, hero } = pageData;
  return (
    <div className="relative">
      <Background background={backgroundMedia} />
      <div className="relative mt-[174px]">
        {hero && <ClubChampionOverviewHero {...hero} translations={siteConfig?.translations ?? {}} />}
        {blocks.map((b) => (
          <BlockRenderer {...b} {...siteConfig} key={`${b.__component}-${b.id}`} />
        ))}
      </div>
    </div>
  );
}

const Background = ({ background }: { background: MediaType | null }) => {
  return (
    <div className="bg-dark-default text-white-dirty absolute inset-0 h-[400px]">
      {background === null ? null : isVideoMedia(background.url) ? (
        <StrapiVideo autoPlay className="size-full object-cover" loop muted video={background} />
      ) : (
        <StrapiImage className="size-full object-cover" image={background} />
      )}
      <div className="pointer-events-none absolute inset-0 bg-gradient-to-b from-[#15151500] to-[#151515]" />
    </div>
  );
};
