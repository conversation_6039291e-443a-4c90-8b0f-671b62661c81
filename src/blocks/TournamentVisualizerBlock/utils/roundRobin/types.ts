import { LocalMatchStatus } from '@/utils/matchSeries';

import { Contestant, Group, Phase } from '../types';

export interface RoundRobinPhase extends Phase {
  groups: RoundRobinGroup[];
}

export interface RoundRobinGroup extends Group {
  contestants: RoundRobinContestant[];
  matches: RoundRobinMatch[];
}

export interface RoundRobinMatch {
  id: string;
  contestants: Contestant[];
  startTime: string;
  status: LocalMatchStatus;
}

export interface RoundRobinContestant extends Contestant {
  wins: number;
  draws: number;
  losses: number;
  pointsScored: number;
  pointsLost: number;
  diff: number;
}
