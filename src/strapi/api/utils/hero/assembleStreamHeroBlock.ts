import { VodEnrichedItem } from '@/blocks/VodBlock/VodGrid';
import { Locale } from '@/hooks/i18n/const';
import { VodItemType } from '@/strapi/types/block';
import { StreamIntermissionType } from '@/strapi/types/collection/stream';
import { CustomStreamHeroBlockType, HomepageStreamHeroType } from '@/strapi/types/hero';

import { getVodData } from '../../collection/vods';
import { enrichVodItems } from '../vod';

export async function assembleStreamHeroBlock(
  heroBlock: HomepageStreamHeroType | CustomStreamHeroBlockType,
  locale: Locale,
) {
  const streams = heroBlock.weekStream?.streams;
  if (!streams) {
    return;
  }

  const gameVodsMap = new Map<string, VodEnrichedItem[]>();
  for (const stream of streams) {
    if (stream.intermissionType === StreamIntermissionType.TIMER) {
      continue;
    }

    if (stream.game?.slug) {
      const existingVods = gameVodsMap.get(stream.game.slug);
      if (existingVods) {
        stream.vods = existingVods;
        continue;
      }

      const vods = (await getVodData(locale, stream.game.slug)) as VodItemType[];
      const enrichedVods = await enrichVodItems(vods);

      gameVodsMap.set(stream.game.slug, enrichedVods);
      stream.vods = enrichedVods;
      continue;
    }

    const enrichedVods = await enrichVodItems(stream.vods);
    stream.vods = enrichedVods;
  }
}
