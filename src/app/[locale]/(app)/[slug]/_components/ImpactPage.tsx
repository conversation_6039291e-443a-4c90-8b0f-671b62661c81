import clsx from 'clsx';

import { BlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import TopNavigation from '@/components/TopNavigation';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { PageType } from '@/strapi/types/collection/page';
import { TopNavigationType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import { isVideoMedia } from '@/utils/media';

import { Locale } from '../../../../../hooks/i18n/const';
import { RenderHero } from './RenderHero';

export const ImpactPage = ({
  background,
  header,
  hero,
  blocks,
  disableBgGradient,
  locale,
  ...rest
}: PageType & Partial<SiteConfigData> & { locale: Locale }) => {
  const topNavigationItems = blocks
    .map((b) => b.section?.navigation)
    .filter((item): item is TopNavigationType => item !== null && item !== undefined);

  const shouldRenderHeader = !hero && header;
  return (
    <div className="relative">
      {background && <Background background={background} disableBgGradient={disableBgGradient} />}
      <TopNavigation navigationItems={topNavigationItems} />
      <div className={clsx('relative flex flex-col', { 'pt-[140px] lg:pt-[174px]': shouldRenderHeader })}>
        <RenderHero
          header={header}
          hero={hero}
          locale={locale}
          translations={rest.translations ?? {}}
          type={rest.type}
        />
        {blocks.map((b) => (
          <BlockRenderer {...b} key={`${b.__component}-${b.id}`} {...rest} />
        ))}
      </div>
    </div>
  );
};

const Background = ({ background, disableBgGradient }: { background: MediaType; disableBgGradient: boolean }) => {
  return (
    <div className="bg-dark-default absolute top-0 h-100 w-full">
      {isVideoMedia(background.url) ? (
        <StrapiVideo autoPlay className="absolute inset-0 h-full w-full object-cover" loop muted video={background} />
      ) : (
        <StrapiImage className="absolute inset-0 h-full w-full object-cover" image={background} />
      )}
      {!disableBgGradient && (
        <div className="from-dark-default/0 to-dark-default absolute inset-0 bg-gradient-to-b to-55%" />
      )}
    </div>
  );
};
