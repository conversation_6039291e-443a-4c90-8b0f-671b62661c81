interface TextSectionProps {
  title: string;
  value: string | number;
}

export const TextSection = ({ title, value }: TextSectionProps) => {
  return (
    <div className="flex flex-col items-center justify-between gap-1 border-inherit py-1 text-[10px] leading-none md:flex-row md:justify-between md:py-2 md:text-sm md:not-last:border-b">
      <span className="font-base font-medium">{title}</span>
      <span className="font-primary font-bold">{value}</span>
    </div>
  );
};
