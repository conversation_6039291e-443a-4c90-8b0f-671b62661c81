'use client';

import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef } from 'react';

import { APP_BASE_URL, COGNITO_CLIENT_ID, COGNITO_DOMAIN } from '@/config/env/client';
import { useCurrentLocale } from '@/hooks/i18n';
import { fetchAuthSession } from '@/services/auth/cognito';
import { useInvalidateCurrentUserQuery } from '@/services/auth/hooks';
import { JsonFieldType } from '@/strapi/types/helper';
import { showErrorToast, showSuccessToast } from '@/ui/components/Toast';

export const LinkAccountListener = ({ translations }: { translations?: JsonFieldType | null }) => {
  const params = useSearchParams();
  const locale = useCurrentLocale();

  const isLinkAccountActivatedRef = useRef(false);
  const invalidateCurrentUser = useInvalidateCurrentUserQuery();

  const { replace } = useRouter();
  const redirectToProfile = useCallback(() => replace(`/${locale}/profile`), [locale, replace]);

  const code = params.get('code');
  useEffect(() => {
    if (isLinkAccountActivatedRef.current) {
      return;
    }
    isLinkAccountActivatedRef.current = true;

    if (!code) {
      showErrorToast({
        title: translations?.['missingCodeParameterErrorToastMessage'] ?? 'Missing code parameter!',
        description:
          translations?.['missingCodeParameterErrorToastMessageDescription'] ??
          'Unable to link accounts because the code query param is missing.',
      });
      redirectToProfile();
      return;
    }

    linkProfileWithNewAccount(code, translations).then(() => {
      invalidateCurrentUser();
      redirectToProfile();
    });
  }, [code, invalidateCurrentUser, redirectToProfile, translations]);

  return null;
};

async function linkProfileWithNewAccount(code: string, translations?: JsonFieldType | null) {
  try {
    const newAccountToken = await fetchIdToken(code);

    const { tokens } = await fetchAuthSession();
    const currentUserToken = tokens?.idToken?.toString() as string;

    const data = { currentUserToken, newAccountToken };
    const response = await fetch('/api/accounts/link', { method: 'POST', body: JSON.stringify(data) });
    if (response.status === 200) {
      showSuccessToast({ title: translations?.['accountLinkedToastMessage'] ?? 'Account successfully linked!' });
    }
  } catch (error: any) {
    showErrorToast({ title: 'Error while linking accounts!', description: error.message });
  }
}

async function fetchIdToken(code: string) {
  const body = new URLSearchParams({
    client_id: COGNITO_CLIENT_ID,
    redirect_uri: `${APP_BASE_URL}/profile/link`,
    grant_type: 'authorization_code',
    code,
  });
  const response = await fetch(`https://${COGNITO_DOMAIN}/oauth2/token`, {
    method: 'POST',
    body,
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  });

  const data = await response.json();
  if (response.status === 200) {
    return data.id_token as string;
  }

  throw new Error(data.error);
}
