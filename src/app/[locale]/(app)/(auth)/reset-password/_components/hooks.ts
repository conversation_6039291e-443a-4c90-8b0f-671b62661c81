import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { getEmailSchema, getPasswordConfirmSchema, getPasswordSchema } from '@/config/schema/auth';
import { JsonFieldType } from '@/strapi/types/helper';

function getResetPasswordSchema(translations?: JsonFieldType | null) {
  return yup.object({
    confirmationCode: yup
      .string()
      .required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.'),
    username: getEmailSchema(translations),
    password: getPasswordSchema(translations),
    passwordConfirm: getPasswordConfirmSchema(translations),
  });
}

export interface ResetPasswordFormValues {
  confirmationCode: string;
  username: string;
  password: string;
  passwordConfirm: string;
}

export function useResetPasswordForm(translations?: JsonFieldType | null) {
  return useForm<ResetPasswordFormValues>({
    defaultValues: { confirmationCode: '', username: '', password: '', passwordConfirm: '' },
    resolver: yup<PERSON><PERSON><PERSON><PERSON>(getResetPasswordSchema(translations)),
    mode: 'onTouched',
  });
}
