import { Button } from '@/ui/components/Button';

interface Props {
  text: string;
  onClose: () => void;
}

export const CloseButton = ({ text, onClose }: Props) => {
  return (
    <div className="absolute end-0 top-0 max-md:p-1 md:-top-8 lg:-end-4 lg:-top-12">
      <Button
        brazeEventProperties={{ button_name: `Close Modal button`, location: `Club Modal` }}
        text={text}
        variant="secondary"
        onClick={onClose}
      />
    </div>
  );
};
