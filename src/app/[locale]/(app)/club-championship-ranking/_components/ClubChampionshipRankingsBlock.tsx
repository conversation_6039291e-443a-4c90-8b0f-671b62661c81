'use client';

import clsx from 'clsx';
import { useMemo } from 'react';
import { MdInfoOutline } from 'react-icons/md';
import { MdOutlineWarningAmber } from 'react-icons/md';

import { useTodaysCcRankings } from '@/hooks/tournaments/useTodaysCcRankings';
import { useTournamentsData } from '@/services/graphql/hooks/tournaments';
import { Tournament } from '@/services/graphql/types/tournament';
import { GameWithCompetitions } from '@/strapi/api/collection/game';

import { SiteConfigData } from '../../../../../strapi/api/single/siteConfig';
import { RichTextContentType } from '../../../../../strapi/types/helper';
import { hasRichTextContent } from '../../../../../strapi/utils/hasRichTextContent';
import { Button } from '../../../../../ui/components/Button';
import { SimulatorProvider, useSimulator } from '../_context/SimulatorContext';
import { ClubData } from '../_utils/types';
import { ClubCard } from './ClubCard';
import { ClubChampionshipEligibleIcon } from './HelperComponents/ClubChampionshipEligibleIcon';
import { ExplainerText } from './HelperComponents/ExplainerText';
import { LeaderboardSection } from './LeaderboardSection';
import { MissingDataPlaceholder } from './MissingDataPlaceholder';

interface ClubChampionshipRankingsBlockProps {
  leaderboardTitle: string | null;
  leaderboardSubtitle: string | null;
  tournamentEnd: string;
  tournamentStart: string;
  topExplainerText: RichTextContentType | null;
  middleExplainerText: RichTextContentType | null;
  bottomExplainerText: RichTextContentType | null;
  grayedOutText: RichTextContentType | null;
  siteConfig: SiteConfigData | null;
  gameCompetitions: GameWithCompetitions[];
  isTop3Hidden?: boolean;
  leaderboardButtonText: string | null;
  enableSimulator: boolean;
  simulatorTranslationProps: {
    title: string | null;
    subtitle: string | null;
    buttonResetText: string | null;
    buttonExitText: string | null;
    warningText: RichTextContentType | null;
  };
}

interface ClubChampionshipRankingsContentProps extends ClubChampionshipRankingsBlockProps {
  initialRankings: ClubData[];
  tournaments: Tournament[];
}

const ClubChampionshipRankingsContent = ({
  topExplainerText,
  leaderboardTitle,
  leaderboardSubtitle,
  siteConfig,
  middleExplainerText,
  grayedOutText,
  tournamentEnd = '2025-08-24',
  tournamentStart = '2025-07-07',
  bottomExplainerText,
  isTop3Hidden = false,
  initialRankings,
  simulatorTranslationProps,
  leaderboardButtonText,
  enableSimulator,
  gameCompetitions,
  tournaments,
}: ClubChampionshipRankingsContentProps) => {
  const { isSimulatorMode, toggleSimulatorMode, resetSimulation } = useSimulator();

  // Get today's rankings for display
  const highlightedClubs = initialRankings?.slice(0, 3) ?? [];
  const areHighlightedClubsVisible = !isTop3Hidden && highlightedClubs.length > 0;

  const title = isSimulatorMode ? simulatorTranslationProps.title : leaderboardTitle;
  const subtitle = isSimulatorMode ? simulatorTranslationProps.subtitle : leaderboardSubtitle;

  return (
    <div
      className={clsx('relative z-10 mx-auto flex max-w-[1396px] flex-col items-center gap-20 px-4 lg:px-8', {
        'mt-[-118px] md:mt-[-137.5px] lg:mt-[-118px]': areHighlightedClubsVisible,
      })}
    >
      <div className="flex w-full flex-col gap-4 md:gap-8">
        {areHighlightedClubsVisible && (
          <div className="flex w-full flex-col gap-2 md:flex-row md:gap-4 2xl:gap-8">
            {highlightedClubs.map((club) => (
              <ClubCard club={club} key={club.club.id} translations={siteConfig?.translations} />
            ))}
          </div>
        )}
        {topExplainerText && hasRichTextContent(topExplainerText) && (
          <ExplainerText
            content={topExplainerText}
            icon={<MdOutlineWarningAmber className="text-gold-primary size-[22px]" />}
            paragraphClassName="text-gold-primary"
          />
        )}
      </div>

      <div className="flex w-full flex-col gap-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-end [&>button]:max-md:w-full">
          <div className="flex grow flex-col gap-1">
            {title && <h3 className="text-h3 text-dark-default font-primary">{title}</h3>}
            {subtitle && <p className="text-paragraph text-dark-default font-base">{subtitle}</p>}
          </div>
          {isSimulatorMode && simulatorTranslationProps.buttonResetText && (
            <Button
              brazeEventProperties={{ button_name: 'reset selection', location: 'club championship ranking' }}
              text={simulatorTranslationProps.buttonResetText}
              variant="secondary"
              onClick={resetSimulation}
            />
          )}
          {enableSimulator && (
            <Button
              brazeEventProperties={{
                button_name: isSimulatorMode ? 'exit simulator mode' : 'enter endgame simulator',
                location: 'club championship ranking',
              }}
              text={isSimulatorMode ? simulatorTranslationProps.buttonExitText : leaderboardButtonText}
              variant={isSimulatorMode ? 'gold' : 'primary'}
              onClick={toggleSimulatorMode}
            />
          )}
        </div>
        <div className="flex flex-col">
          <LeaderboardSection
            gameCompetitions={gameCompetitions}
            middleExplainerText={middleExplainerText}
            tournamentEnd={tournamentEnd}
            tournamentStart={tournamentStart}
            tournaments={tournaments}
            translations={siteConfig?.translations}
          />
          {!isSimulatorMode && bottomExplainerText && hasRichTextContent(bottomExplainerText) && (
            <ExplainerText
              content={bottomExplainerText}
              icon={<ClubChampionshipEligibleIcon />}
              paragraphClassName="text-gold-primary"
            />
          )}
        </div>
        {!isSimulatorMode && grayedOutText && hasRichTextContent(grayedOutText) && (
          <ExplainerText
            className="text-dark-default border-dark-default/2 bg-[#1515150D]"
            content={grayedOutText}
            icon={<MdInfoOutline className="text-dark-default size-[26px] p-0.5" />}
            paragraphClassName="text-dark-default"
          />
        )}
        {isSimulatorMode &&
          simulatorTranslationProps.warningText &&
          hasRichTextContent(simulatorTranslationProps.warningText) && (
            <ExplainerText
              className="border-[#AD3838]/10 bg-[#AD3838]/5"
              content={simulatorTranslationProps.warningText}
              icon={<MdOutlineWarningAmber className="size-[22px] text-[#AD3838]" />}
              paragraphClassName="text-[#AD3838]"
            />
          )}
      </div>
    </div>
  );
};

export const ClubChampionshipRankingsBlock = (props: ClubChampionshipRankingsBlockProps) => {
  const flatCompetitionSlugs = useMemo(
    () =>
      props.gameCompetitions.flatMap((game) => game.competitionSlugs.map((competition) => competition.competitionId)),
    [props.gameCompetitions],
  );

  const { data, loading, error } = useTournamentsData(flatCompetitionSlugs, { isSubscriptionDisabled: true });
  const { todaysCCRankings } = useTodaysCcRankings(props.gameCompetitions);

  if (loading) return <div>loading...</div>;
  if (error) return <MissingDataPlaceholder>Something went wrong</MissingDataPlaceholder>;
  if (!data || !data.tournaments.result) return <MissingDataPlaceholder>No data found</MissingDataPlaceholder>;
  if (!todaysCCRankings) return <MissingDataPlaceholder>No rankings data found</MissingDataPlaceholder>;

  return (
    <SimulatorProvider gameCompetitions={props.gameCompetitions} tournaments={data.tournaments.result}>
      <ClubChampionshipRankingsContent
        {...props}
        initialRankings={todaysCCRankings}
        tournaments={data.tournaments.result}
      />
    </SimulatorProvider>
  );
};
