import { useMemo } from 'react';

import { useCurrentLocale } from '@/hooks/i18n';
import { useMatchSeriesData } from '@/services/graphql/hooks';

import { convertMatchSeriesToHeroData } from '../../shared/utils/convertMatchSeriesToHeroData';

export function useMatchesData(tournamentIds: string[]) {
  const locale = useCurrentLocale();
  const { data, loading, error } = useMatchSeriesData(tournamentIds);

  const matches = useMemo(
    () => (data ? convertMatchSeriesToHeroData(data.matchSeries.items, locale) : []),
    [data, locale],
  );

  return { matches, loading, errorMessage: error?.message };
}
