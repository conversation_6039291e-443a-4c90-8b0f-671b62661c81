import clsx from 'clsx';
import { memo } from 'react';

import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';

import { GameStreamDetails, SimpleStreamDetails } from '../shared/StreamDetails';
import { StreamHeroMatch } from '../shared/types/StreamHeroMatch';

interface Props {
  gameName: string | null;
  ticketsUrl: string | null;
  intermissionText: string | null;
  selectedMatch?: StreamHeroMatch;
  apiTranslations: JsonFieldType;
  translations: JsonFieldType;
}

export const StreamDetails = memo(
  ({ gameName, ticketsUrl, intermissionText, selectedMatch, apiTranslations, translations }: Props) => {
    const ButtonComponent = (
      <StreamDetailsButton text={translations?.['buyTickets'] ?? 'buy tickets'} url={ticketsUrl} />
    );

    if (!!selectedMatch) {
      return (
        <GameStreamDetails
          apiTranslations={apiTranslations}
          button={ButtonComponent}
          gameName={gameName}
          selectedMatchSeries={selectedMatch}
          translations={translations}
        />
      );
    }
    return <SimpleStreamDetails button={ButtonComponent} text={intermissionText} />;
  },
);

StreamDetails.displayName = 'StreamDetails';

interface StreamDetailsProps {
  text: string;
  url?: string | null;
}

const StreamDetailsButton = ({ text, url }: StreamDetailsProps) => {
  return (
    <div className={clsx('md:ms-auto md:w-fit', !url && 'invisible')}>
      <Button
        brazeEventProperties={{ button_name: `Buy Tickets Link ${url}`, location: 'Game Stream Hero - Stream Details' }}
        isFullWidth
        link={url}
        text={text}
        variant="white"
      />
    </div>
  );
};
