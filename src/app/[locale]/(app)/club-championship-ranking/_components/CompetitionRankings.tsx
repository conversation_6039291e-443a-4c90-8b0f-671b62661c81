import { AnimatePresence, motion } from 'framer-motion';

import { CompetitionCard } from '@/components/CompetitionCard';
import AbstractBgImage from '@/ui/assets/images/abstract-background.webp';

import { JsonFieldType } from '../../../../../strapi/types/helper';
import { useSimulator } from '../_context/SimulatorContext';
import { ClubCompetition } from '../_utils/types';

const sortCompetitions = (entries: [string, ClubCompetition][]) => {
  return entries.sort((a, b) => {
    const [, compA] = a;
    const [, compB] = b;

    // Handle cases where timestamp might be null
    if (!compA.timestamp && !compB.timestamp) return 0;
    if (!compA.timestamp) return 1;
    if (!compB.timestamp) return -1;

    return new Date(compA.timestamp).getTime() - new Date(compB.timestamp).getTime();
  });
};

export const CompetitionRankings = ({
  competitions,
  translations,
  clubId,
}: {
  competitions: Record<string, ClubCompetition>;
  translations?: JsonFieldType | null;
  clubId: string;
}) => {
  const { isSimulatorMode, handleSimulatePoints } = useSimulator();

  const handlePointsChange = (competitionSlug: string, points: number | null) => {
    handleSimulatePoints(clubId, competitionSlug, points);
  };

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="relative flex gap-2 overflow-x-auto p-2 md:p-4"
      initial={{ opacity: 0 }}
      style={{
        backgroundImage: `url(${AbstractBgImage.src})`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
      }}
      transition={{ duration: 0.3 }}
    >
      <AnimatePresence>
        {sortCompetitions(Object.entries(competitions)).map(([competitionName, value], index) => {
          return (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              initial={{ opacity: 0, y: 20 }}
              key={`${competitionName}-${value.rank}-${index}`}
              transition={{ duration: 0.2, delay: 0.1 + index * 0.05 }}
            >
              <CompetitionCard
                clubId={clubId}
                competitionName={competitionName}
                isFinished={value.isFinished}
                isSimulation={isSimulatorMode}
                logoDark={value.logoDark}
                logoLight={value.logoLight}
                rank={value.rank}
                simulatedPoints={value.isSimulated ? value.simulatedPoints : null}
                slug={value.slug}
                translations={translations}
                onSimulatePoints={handlePointsChange}
              />
            </motion.div>
          );
        })}
      </AnimatePresence>
    </motion.div>
  );
};
