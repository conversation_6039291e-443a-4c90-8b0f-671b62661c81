import clsx from 'clsx';

import { JsonFieldType } from '@/strapi/types/helper';

interface Props {
  winnerName: string;
  usd: number;
  points: number;
  medals: {
    gold: number;
    silver: number;
    bronze: number;
  };
  translations: JsonFieldType;
}

export const TitleWithStatsSection = ({ winnerName, usd, points, medals, translations }: Props) => {
  return (
    <div className="flex flex-col items-center gap-4">
      <p className="text-h1 text-gold-primary text-center">
        {winnerName} {translations.won ?? 'won'} ${usd.toLocaleString()}
      </p>
      <div className="flex flex-wrap justify-center gap-2">
        {medals.gold > 0 && <CountBadge bgClassName="bg-radial-gold-matte" text={`${medals.gold} x Gold`} />}
        {medals.silver > 0 && <CountBadge bgClassName="bg-radial-silver" text={`${medals.silver} x Silver`} />}
        {medals.bronze > 0 && <CountBadge bgClassName="bg-radial-bronze" text={`${medals.bronze} x Bronze`} />}
        <CountBadge
          bgClassName="bg-gold-primary"
          text={`${points.toLocaleString()} ${translations.ccPointsAchieved ?? 'CC Points Achieved'}`}
        />
      </div>
    </div>
  );
};

interface CountBadgeProps {
  bgClassName: string;
  text: string;
}

const CountBadge = ({ text, bgClassName }: CountBadgeProps) => {
  return (
    <div className={clsx('bg-gold-primary rounded-xs p-1.5 md:rounded-sm md:p-2', bgClassName)}>
      <p className="font-base text-sm leading-none font-extrabold text-white md:text-lg lg:text-[21px]">{text}</p>
    </div>
  );
};
