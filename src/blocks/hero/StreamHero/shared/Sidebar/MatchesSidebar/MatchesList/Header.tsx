import { LocalMatchStatus } from '@/utils/matchSeries';

import { JsonFieldType } from '../../../../../../../strapi/types/helper';

export const Header = ({
  startTime,
  status,
  translations,
}: {
  startTime?: string;
  status: LocalMatchStatus;
  translations?: JsonFieldType;
}) => {
  if (status === LocalMatchStatus.COMPLETED) {
    return (
      <div className="bg-gray-easy flex h-[20px] items-center rounded-xs px-2 py-1 text-white">
        <div className="font-primary flex items-center justify-between text-[10px] leading-tight font-bold text-black uppercase">
          <p>{translations?.['completed'] ?? 'completed'}</p>
        </div>
      </div>
    );
  } else if (status === LocalMatchStatus.LIVE) {
    return (
      <div className="flex h-[20px] items-center gap-1 rounded-xs bg-[#F40F30] px-2 py-1">
        <div className="size-1.5 rounded-full bg-white" />
        <p className="font-primary text-[10px] leading-none font-bold text-white uppercase">
          {translations?.['live'] ?? 'live'}
        </p>
      </div>
    );
  }
  return (
    <div className="bg-dark-default flex h-[20px] w-full items-center rounded-xs px-2 py-1 text-white">
      <div className="font-primary flex w-full items-center justify-between text-[10px] leading-none font-bold text-white uppercase">
        <p>{translations?.['upcoming'] ?? 'upcoming'}</p>
        <p>{startTime}</p>
      </div>
    </div>
  );
};
