'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import { useCurrentUser } from '@/services/auth/hooks';

export const UserProfileGate = ({ children }: React.PropsWithChildren) => {
  const router = useRouter();
  const { user, isLoading } = useCurrentUser();

  const searchParams = useSearchParams();
  const params = searchParams.toString();

  useEffect(() => {
    if (!isLoading && !user) {
      router.replace(params ? `/login?${params}` : '/login');
    }
  }, [isLoading, user, router, params]);

  //todo show loader?
  if (isLoading) {
    return null;
  }

  if (!user) {
    return null;
  }

  return children;
};
