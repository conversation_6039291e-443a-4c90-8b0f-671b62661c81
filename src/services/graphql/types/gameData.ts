export interface GameDataQueryResponse {
  gameData: {
    data: Match[];
  };
}

export interface GameDataSubscriptionResponse {
  gameDataUpdated: Match;
}

export interface Match {
  state: {
    contestants: Team[];
  };
}

export interface Team {
  score: number;
  players: Player[];
}

export interface Player {
  id: string;
  stats: PlayerStats;
  meta: {
    character: {
      name: string;
    };
  };
  attributes: {
    gold: {
      current: number;
    };
    xp: {
      current: number;
    };
  };
}

export interface PlayerStats {
  deaths: number;
  assists: number;
  damage: number;
  kills: number;
  headshots: number;
}
