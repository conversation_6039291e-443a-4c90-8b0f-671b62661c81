import { useEffect, useState } from 'react';

import { LocalMatchStatus } from '@/utils/matchSeries';

import { Group, Phase } from '../utils/types';

type PartialPhase = {
  id: string;
  name: string;
  groups?: Group[];
};

export function useSelectedPhase(phases: PartialPhase[]) {
  const [selectedPhaseId, setSelectedPhaseId] = useState('');

  useEffect(() => {
    const phasesInitialized = !!phases?.some((p) => p.groups);
    if (!phasesInitialized) {
      return;
    }

    const phaseSelected = !!selectedPhaseId;
    if (phaseSelected) {
      return;
    }

    const firstLivePhaseId = findPhaseIncludingGroupWithStatus(phases as Phase[], LocalMatchStatus.LIVE);
    if (firstLivePhaseId) {
      setSelectedPhaseId(firstLivePhaseId);
      return;
    }

    const firstOpenPhaseId = findPhaseIncludingGroupWithStatus(phases as Phase[], LocalMatchStatus.UPCOMING);
    if (firstOpenPhaseId) {
      setSelectedPhaseId(firstOpenPhaseId);
      return;
    }

    const lastPhaseId = phases.at(-1)?.id as string;
    if (lastPhaseId) {
      setSelectedPhaseId(lastPhaseId);
    }
  }, [phases, selectedPhaseId]);

  return { selectedPhaseId, setSelectedPhaseId };
}

function findPhaseIncludingGroupWithStatus(phases: Phase[], status: LocalMatchStatus) {
  return phases.find((p) => p.groups.some((g) => g.status === status))?.id;
}
