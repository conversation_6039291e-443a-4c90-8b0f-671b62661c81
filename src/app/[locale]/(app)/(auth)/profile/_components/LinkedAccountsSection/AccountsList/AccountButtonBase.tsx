'use client';

import { IconType } from 'react-icons';

import { Button } from '@/ui/components/Button';

interface Props {
  primaryText?: string | null;
  secondaryText?: string | null;
  icon: IconType;
  isLinkedAccount?: boolean;
  onClick: () => Promise<void>;
  isDisabled?: boolean;
}

export const AccountButtonBase = ({
  primaryText,
  secondaryText,
  icon,
  isLinkedAccount = true,
  onClick,
  isDisabled,
}: Props) => {
  const Icon = icon;
  return (
    <div className="bg-white-dirty flex items-center gap-2 rounded-lg p-2 md:gap-3 md:rounded-2xl md:pe-4">
      <div className="bg-dark-default flex size-10 shrink-0 items-center justify-center rounded-[14px] md:size-[50px]">
        <Icon className="size-5 text-white" />
      </div>
      <div className="font-base flex min-w-0 shrink grow flex-col gap-0.5 leading-tight font-bold">
        <p className="text-dark-default truncate text-xs md:text-[13px]">{primaryText}</p>
        <p className="text-gray-dark text-[10px] md:text-[11px]">{secondaryText}</p>
      </div>
      <Button
        brazeEventProperties={{
          button_name: `${isLinkedAccount ? 'Unlink' : 'Link'} Account Button (${primaryText})`,
          location: `${isLinkedAccount ? 'Linked' : 'Available'} Accounts Section`,
        }}
        isDisabled={isDisabled}
        text={isLinkedAccount ? 'unlink' : 'link'}
        variant={isLinkedAccount ? 'secondary' : 'primary'}
        onClick={onClick}
      />
    </div>
  );
};
