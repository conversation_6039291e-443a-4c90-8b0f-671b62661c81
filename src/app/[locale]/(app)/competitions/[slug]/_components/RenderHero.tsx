import { GameHeader } from '@/blocks/game/GameHeader';
import { GameStreamHero } from '@/blocks/hero/StreamHero/GameStreamHero';
import { WinnerHighlightBanner } from '@/blocks/hero/WinnerHighlightBanner/WinnerHighlightBanner';
import { Locale } from '@/hooks/i18n/const';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { GameType } from '@/strapi/types/collection/game';
import { GAME_STREAM_HERO_BLOCK_KEY, GameStreamHeroType, HERO_WINNER_HIGHLIGHT_BANNER } from '@/strapi/types/hero';

interface RenderHeroProps {
  hero: GameType['hero'];
  pageData: GameType;
  slug: string;
  locale: Locale;
  siteConfig: SiteConfigData | null;
  isTopNavigationVisible: boolean;
}

export const RenderHero = async ({ hero, pageData, slug, siteConfig, isTopNavigationVisible }: RenderHeroProps) => {
  if (!hero?.__component) {
    return <GameHeader {...pageData} translations={siteConfig?.translations} />;
  }

  if (hero.__component === GAME_STREAM_HERO_BLOCK_KEY) {
    return (
      <GameStreamHero
        {...(hero as GameStreamHeroType)}
        apiTranslations={siteConfig?.apiTranslations ?? {}}
        gameTitle={pageData.title}
        intermissionText={pageData.intermissionText}
        isTopNavigationVisible={isTopNavigationVisible}
        slug={slug}
        ticketsUrl={pageData.ticketsUrl}
        translations={siteConfig?.translations ?? {}}
      />
    );
  }

  if (hero.__component === HERO_WINNER_HIGHLIGHT_BANNER) {
    return <WinnerHighlightBanner {...pageData} translations={siteConfig?.translations ?? null} />;
  }

  return <GameHeader {...pageData} translations={siteConfig?.translations} />;
};
