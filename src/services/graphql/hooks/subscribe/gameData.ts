import { useSubscription } from '@apollo/client';

import { GAME_DATA_QUERY, GAME_DATA_SUBSCRIPTION_QUERY } from '../../queries';
import { GameDataQueryResponse, GameDataSubscriptionResponse } from '../../types/gameData';
import { useDelay } from './hooks';
import { SubscriptionParams } from './matchSeries';

interface GameDataSubscriptionParams extends SubscriptionParams {
  matchId: string;
}

export function useGameDataSubscription({ enabled, tournamentIds, matchId }: GameDataSubscriptionParams) {
  const delayTime = useDelay(tournamentIds);

  useSubscription<GameDataSubscriptionResponse>(GAME_DATA_SUBSCRIPTION_QUERY, {
    skip: !enabled,
    variables: { matchId },
    onData: ({ data, client }) => {
      const updatedMatch = data.data?.gameDataUpdated;
      if (!updatedMatch) {
        return;
      }

      setTimeout(() => {
        const updatedGameData: GameDataQueryResponse = { gameData: { data: [updatedMatch] } };
        client.writeQuery<GameDataQueryResponse>({
          query: GAME_DATA_QUERY,
          variables: { matchId },
          data: updatedGameData,
        });
      }, delayTime);
    },
  });
}
