import { StylesConfig } from 'react-select';

import { OptionType } from './SimulatedPointsDropdown';

export const customStyles: StylesConfig<OptionType, false> = {
  control: (base) => ({
    ...base,
    backgroundColor: '#987c4b',
    border: 'none',
    borderRadius: '2px',
    minHeight: 'unset',
    padding: '2px',
    boxShadow: 'none',
    textTransform: 'capitalize',
    cursor: 'pointer',
    '&:hover': {
      border: 'none',
    },
  }),
  valueContainer: (base) => ({
    ...base,
    padding: '0 4px',
  }),
  input: (base) => ({
    ...base,
    color: 'white',
    margin: 0,
    padding: 0,
  }),
  singleValue: (base) => ({
    ...base,
    color: 'white',
    fontSize: '10px',
    lineHeight: '110%',
    fontWeight: 'bold',
    margin: 0,
  }),
  placeholder: (base) => ({
    ...base,
    color: 'white',
    fontSize: '10px',
    lineHeight: '110%',
    fontWeight: 'bold',
    margin: 0,
  }),
  menu: (base) => ({
    ...base,
    backgroundColor: 'white',
    fontSize: '10px',
    lineHeight: '110%',
    fontWeight: 'bold',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
    border: '1px solid #e5e5e5',
    borderRadius: '2px',
    marginTop: '4px',
  }),
  menuList: (base) => ({
    ...base,
    padding: 0,
  }),
  menuPortal: (base) => ({
    ...base,
    zIndex: 9999,
  }),
  option: (base, { isSelected, isFocused, isDisabled }) => ({
    ...base,
    backgroundColor: isDisabled ? 'white' : isSelected ? '#987c4b' : isFocused ? '#f5f5f5' : 'white',
    color: isDisabled ? '#cccccc' : isSelected ? 'white' : '#333333',
    padding: '8px',
    fontSize: '10px',
    lineHeight: '110%',
    fontWeight: 'bold',
    cursor: isDisabled ? 'not-allowed' : 'pointer',
    opacity: isDisabled ? 0.6 : 1,
    '&:active': {
      backgroundColor: isDisabled ? 'white' : '#987c4b',
      color: isDisabled ? '#cccccc' : 'white',
    },
    '&:hover': {
      backgroundColor: isDisabled ? 'white' : isSelected ? '#987c4b' : '#f5f5f5',
      color: isDisabled ? '#cccccc' : isSelected ? 'white' : '#333333',
    },
  }),
  indicatorSeparator: () => ({
    display: 'none',
  }),
  dropdownIndicator: (base) => ({
    ...base,
    color: 'white',
    padding: '0 4px',
    '&:hover': {
      color: 'white',
    },
  }),
};
