'use client';

import { AnimatePresence, motion } from 'motion/react';
import { RefObject, useRef } from 'react';
import { GoTriangleDown } from 'react-icons/go';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import { LocalizedLink } from '@/components/LocalizedLink';
import { NavigationItem } from '@/strapi/types/helper/navigationItem';

interface Props {
  label: string;
  items: NavigationItem[];
  isEmbedded?: boolean;
  onClick: () => void;
}

export const PreviousTournamentsDropdown = ({ label, items, isEmbedded, onClick }: Props) => {
  const [isOpen, toggleIsOpen, setIsOpen] = useToggle(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref as RefObject<HTMLElement>, () => setIsOpen(false));

  return (
    <div className="bg-white-dirty flex flex-col gap-1 rounded-lg">
      <div className="bg-gray-easy flex cursor-pointer items-center gap-3 p-3" onClick={toggleIsOpen}>
        <span className="material-symbols-outlined text-dark-default shrink-0 text-[24px]! lg:text-[26px]! lg:group-hover:text-[var(--color-gold-primary)]!">
          history
        </span>
        <span className="font-base text-dark-default mt-px text-[13px] leading-none font-bold capitalize">{label}</span>
        <GoTriangleDown className={`text-dark-default ms-auto transition ${isOpen ? 'rotate-180' : ''}`} size={24} />
      </div>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            animate={{ height: 'auto' }}
            className="overflow-hidden"
            exit={{ height: 0 }}
            initial={{ height: 0 }}
            layout
            transition={{ bounce: 0, duration: 0.1 }}
          >
            {items.map((i) => (
              <LocalizedLink
                brazeEventProperties={{
                  location: 'Mobile Bottom Navigation - Previous Tournaments Dropdown',
                  button_name: `Previous Tournament Link (${i.url})`,
                }}
                className="bg-white-dirty flex cursor-pointer items-center gap-3 p-3"
                href={i.url}
                key={i.url}
                target={isEmbedded ? '_parent' : undefined}
                onClick={onClick}
              >
                <span className="material-symbols-outlined text-dark-default shrink-0 text-[24px]! lg:text-[26px]! lg:group-hover:text-[var(--color-gold-primary)]!">
                  history
                </span>
                <span className="font-base text-dark-default text-[13px] leading-none font-bold capitalize">
                  {i.title}
                </span>
              </LocalizedLink>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
