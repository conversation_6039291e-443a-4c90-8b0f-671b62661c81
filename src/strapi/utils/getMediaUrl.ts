import { STRAPI_BASE_URL } from '@/config/env/client';

export const getMediaUrl = (mediaPath: string, isFullUrl = false) => {
  const isLocalMedia = mediaPath.includes('/uploads');
  // on local env, strapi doesn't return the full url (/uploads/media.png), while on cloud it does (https://some-env.media.strapiapp.com/media.png)
  if (isLocalMedia) {
    // for some components (eg. RTE) strapi returns full url (http://localhost:1337/uploads/media.png) instead of path (/uploads/media.png)
    return isFullUrl ? mediaPath : `${STRAPI_BASE_URL}${mediaPath}`;
  }
  return mediaPath;
};
