'use client';

import groupBy from 'lodash/groupBy';
import React from 'react';

import { useDateLocaleFormatter } from '@/hooks/i18n/useDateLocale';

import { RoundRobinGroup } from '../../../utils/roundRobin/types';
import { ContestantRow } from './ContestantRow';
import { MatchRow } from './MatchRow';

export const GroupTable = ({ name, contestants, matches }: RoundRobinGroup) => {
  const format = useDateLocaleFormatter();

  const matchesByDay = groupBy(matches, (m) => format(m.startTime, 'MMMM d'));
  const sortedContestants = contestants.toSorted((a, b) => (b.wins === a.wins ? b.diff - a.diff : b.wins - a.wins));

  return (
    <div className="bg-white-dirty flex flex-col gap-0.5 rounded-lg p-2 shadow-[0px_8px_24px_0px_#FFFFFF4D]">
      <p className="bg-dark-default font-primary flex justify-center rounded-sm p-1 text-sm leading-none font-bold text-white uppercase md:text-lg">
        {name}
      </p>
      {sortedContestants.map((c, i) => (
        <ContestantRow key={c.id} rank={i + 1} {...c} />
      ))}
      {Object.keys(matchesByDay).map((date) => (
        <React.Fragment key={date}>
          <p className="font-primary text-dark-default flex justify-center p-1 text-[10px] leading-none font-bold uppercase">
            {date}
          </p>
          <div className="flex flex-col gap-0.5">
            {matchesByDay[date].map((m) => (
              <MatchRow key={m.id} {...m} />
            ))}
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};
