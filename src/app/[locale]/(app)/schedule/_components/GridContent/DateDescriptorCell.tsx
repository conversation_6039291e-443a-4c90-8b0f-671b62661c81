import clsx from 'clsx';
import { formatISO, parseISO } from 'date-fns';
import { formatDate } from 'date-fns/format';

import { useDateLocale } from '@/hooks/i18n/useDateLocale';

import { JsonFieldType } from '../../../../../../strapi/types/helper';

export const DateDescriptorCell = ({
  date,
  translations,
  isSelected,
}: {
  date: string;
  translations: JsonFieldType | null;
  isSelected?: boolean;
}) => {
  const locale = useDateLocale();
  const today = formatISO(Date.now(), { representation: 'date' });
  return (
    <div
      className={clsx('flex w-full flex-col gap-0.5 rounded-sm ps-2 pb-1', date === today ? 'pt-0' : 'pt-3.5', {
        'bg-dark-default text-white': isSelected,
        'text-dark-default': !isSelected,
        'px-2! py-1!': isSelected && date === today,
      })}
    >
      {date === today && (
        <p className="text-dark-default w-fit rounded-[2px] bg-[#F2C575] px-0.5 pt-[2px] text-[8px] font-extrabold uppercase">
          {translations?.today ?? 'Today'}
        </p>
      )}
      <div className="flex flex-col">
        <p className="font-primary text-lg leading-[1] font-bold">{formatDate(parseISO(date), 'do', { locale })}</p>
        <div className="flex gap-1 text-xs uppercase">
          <p className="font-normal">{formatDate(parseISO(date), 'MMM', { locale })}</p>
          <p className="font-extrabold">{formatDate(parseISO(date), 'iii', { locale })}</p>
        </div>
      </div>
    </div>
  );
};
