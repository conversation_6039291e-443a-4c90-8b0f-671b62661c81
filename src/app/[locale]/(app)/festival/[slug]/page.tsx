import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { BlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getFestivalSinglePageData, getFestivalSinglePageSeo } from '@/strapi/api/collection/festival';
import { redirectAccordingToConfig } from '@/strapi/api/collection/redirect';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale<{ slug: string }>): Promise<Metadata> {
  const { locale, slug } = await params;
  const seo = await getFestivalSinglePageSeo(locale, slug);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function FestivalSinglePage({ params }: PageProps<{ slug: string }>) {
  const { locale, slug } = await params;
  await redirectAccordingToConfig(`/festival/${slug}`, locale);

  const pageData = await getFestivalSinglePageData(locale, slug);
  const siteConfig = await getSiteConfig(locale);

  if (!pageData) {
    return notFound();
  }

  const { blocks } = pageData;

  return (
    <>
      {blocks.map((b) => (
        <BlockRenderer {...b} {...siteConfig} key={`${b.__component}-${b.id}`} />
      ))}
    </>
  );
}
