import clsx from 'clsx';

import { ClubData } from '@/app/[locale]/(app)/club-championship-ranking/_utils/types';
import { RankedCompetition } from '@/blocks/shared/api/fetchClubChampionshipWinner';
import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { StrapiImage } from '@/components/StrapiImage';
import { ClubChampionOverviewBlockType } from '@/strapi/types/block';
import { Only } from '@/ui/components/Only';

import { Table } from './Table';

const borderStyles = 'rounded-lg md:rounded-2xl lg:rounded-4xl';

export type ClubChampionshipTableProps = ClubChampionOverviewBlockType & {
  winner: ClubData;
  competitions: RankedCompetition[];
};

export const ClubChampionshipTable = (props: ClubChampionshipTableProps) => {
  return (
    <BlockSectionWrapper {...props.section}>
      <Only fallback={<MobileView {...props} />} for="lgAndAbove">
        <DesktopView {...props} />
      </Only>
    </BlockSectionWrapper>
  );
};

const DesktopView = ({
  heroImage,
  button,
  competitions,
  winner,
  moneyWon,
  pointsWon,
  translations,
}: ClubChampionshipTableProps) => {
  return (
    <div className={clsx('relative flex min-h-[917px] justify-end', borderStyles)}>
      {heroImage && (
        <StrapiImage
          alt="2024 winners with the EWC trophy"
          className={clsx('absolute inset-0 size-full object-cover object-bottom', borderStyles)}
          image={heroImage}
          quality={25}
        />
      )}
      <div className={clsx('absolute inset-0 bg-gradient-to-r from-black/0 to-black/33', borderStyles)} />
      <div className="relative p-8">
        <Table
          button={button}
          competitions={competitions}
          moneyWon={moneyWon}
          pointsWon={pointsWon}
          translations={translations}
          winner={winner}
        />
      </div>
    </div>
  );
};

const MobileView = ({
  heroImage,
  button,
  competitions,
  winner,
  moneyWon,
  pointsWon,
  translations,
}: ClubChampionshipTableProps) => {
  return (
    <div className="-mb-[49px] md:-mb-[92px]">
      <div className={clsx('relative aspect-[1.5] w-full', borderStyles)}>
        {heroImage && (
          <StrapiImage
            alt="2024 winners with the EWC trophy"
            className={clsx('absolute inset-0 size-full object-cover object-bottom', borderStyles)}
            image={heroImage}
            quality={25}
          />
        )}
        <div className={clsx('absolute inset-0 bg-gradient-to-r from-black/0 to-black/33', borderStyles)} />
      </div>
      <div className="relative -top-[49px] w-full md:-top-[92px] md:px-8">
        <Table
          button={button}
          competitions={competitions}
          moneyWon={moneyWon}
          pointsWon={pointsWon}
          translations={translations}
          winner={winner}
        />
      </div>
    </div>
  );
};
