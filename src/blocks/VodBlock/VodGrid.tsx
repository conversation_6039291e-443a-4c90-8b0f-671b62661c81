'use client';

import { clsx } from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { useIsClient, useWindowSize } from 'usehooks-ts';

import { VodItemType } from '@/strapi/types/block';
import { JsonFieldType } from '@/strapi/types/helper';

import { VodCard } from './VodCard';

export interface VodEnrichedItem extends VodItemType {
  id: number;
  fetchedTitle: string;
  fetchedThumbnail: string;
  fetchedAuthor?: string;
  fetchedLanguage?: string;
  locale?: string;
  games: { slug: string; title: string }[];
}

type VodGridProps = {
  vods: VodEnrichedItem[];
  itemsPerPage: number | null;
  languageBadge?: boolean | null;
  translations: JsonFieldType | null | undefined;
};

export function VodGrid({ vods, itemsPerPage, languageBadge = false, translations }: VodGridProps) {
  const { width } = useWindowSize();
  const isClient = useIsClient();
  const isSliderMode = isClient && width < 768;

  const effectiveItemsPerPage = itemsPerPage ?? vods.length;

  const [visibleCount, setVisibleCount] = useState(effectiveItemsPerPage);
  const desktopVisibleCountRef = useRef(effectiveItemsPerPage);

  useEffect(() => {
    if (!isClient) return;

    if (isSliderMode) {
      setVisibleCount(vods.length);
    } else {
      setVisibleCount(desktopVisibleCountRef.current);
    }
  }, [isSliderMode, vods.length, isClient]);

  const handleLoadMore = () => {
    const newCount = desktopVisibleCountRef.current + effectiveItemsPerPage;
    desktopVisibleCountRef.current = newCount;
    setVisibleCount(newCount);
  };

  const canLoadMore = !isSliderMode && vods.length > visibleCount;
  const visibleVods = isSliderMode ? vods : vods.slice(0, visibleCount);

  return (
    <div className="flex flex-col gap-6">
      <div
        className={clsx(
          isSliderMode
            ? 'hide-scrollbar flex gap-4 overflow-x-auto md:pl-0'
            : 'grid grid-cols-3 gap-4 px-4 sm:grid-cols-4 md:px-0 lg:grid-cols-5',
        )}
      >
        {visibleVods.map((vod) => (
          <div className={isSliderMode ? 'last:mr-4' : undefined} key={vod.id}>
            <VodCard
              isSliderMode={isSliderMode}
              {...vod}
              showLanguageBadge={!!languageBadge}
              translations={translations ?? {}}
            />
          </div>
        ))}
      </div>
      {canLoadMore && (
        <div
          className="hidden self-start rounded-xl bg-[#eaeaea] px-8 py-4 text-sm text-[13px] font-extrabold text-black uppercase md:flex"
          onClick={handleLoadMore}
        >
          {translations?.['loadMore'] ?? 'Load More'}
        </div>
      )}
    </div>
  );
}
