import { Locale } from '@/hooks/i18n/const';
import { NavigationData } from '@/strapi/api/single/navigation';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { Only } from '@/ui/components/Only';

import { DesktopSidebar } from './DesktopSidebar';
import { MobileBottomNav } from './MobileBottomNav';

export interface NavigationProps {
  data: NavigationData;
  siteConfig: SiteConfigData | null;
  disabledLocales?: Locale[];
  isEmbedded?: boolean;
}

export function Navigation(props: NavigationProps) {
  return (
    <Only fallback={<MobileBottomNav {...props} />} for="lgAndAbove">
      <DesktopSidebar {...props} />
    </Only>
  );
}
