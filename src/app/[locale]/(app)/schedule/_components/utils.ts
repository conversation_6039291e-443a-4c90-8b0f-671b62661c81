import { addDays, formatISO } from 'date-fns';

import { IndicatorStatusType } from '@/app/[locale]/(app)/schedule/_components/GridContent/shared/GameEventStatusIndicator';

export interface WeekDates {
  week: number;
  dates: string[];
}

function generateDatesBetween(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const currentDate = new Date(startDate);
  const end = new Date(endDate);

  while (currentDate <= end) {
    const dateString = formatISO(currentDate, { representation: 'date' });
    dates.push(dateString);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

function chunkDatesIntoWeeks(dates: string[]): WeekDates[] {
  const result: WeekDates[] = [];

  for (let i = 0; i < dates.length; i += 7) {
    result.push({
      week: result.length + 1,
      dates: dates.slice(i, i + 7),
    });
  }

  return result;
}

export function generateWeekDatesBetween(startDate: string, endDate: string, numberOfWeeks?: number): WeekDates[] {
  let dates = generateDatesBetween(startDate, endDate);

  if (numberOfWeeks) {
    dates = dates.slice(0, numberOfWeeks * 7);
  }

  return chunkDatesIntoWeeks(dates);
}

export function getEventStatus(startDate: string | null, endDate: string | null): IndicatorStatusType | null {
  if (!startDate || !endDate) return null;

  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (start > now) return 'upcoming';
  if (end < now) return 'completed';
  if (start <= now && end >= now) return 'live';

  return null;
}

export function isDateInRange(date: Date, rangeStart: string, rangeEnd: string): boolean {
  const start = new Date(rangeStart);
  const end = new Date(rangeEnd);

  return date >= start && date <= end;
}

// compares only the date part (YYYY-MM-DD), ignoring time
export function isDayInRange(date: Date, rangeStart: string, rangeEnd: string): boolean {
  const normalize = (d: Date | string) => {
    const dt = typeof d === 'string' ? new Date(d) : d;
    return (
      dt.getFullYear() + '-' + String(dt.getMonth() + 1).padStart(2, '0') + '-' + String(dt.getDate()).padStart(2, '0')
    );
  };
  const normalizedDate = normalize(date);
  const normalizedStart = normalize(rangeStart);
  const normalizedEnd = normalize(rangeEnd);

  return normalizedDate >= normalizedStart && normalizedDate <= normalizedEnd;
}

export function getGridColumnsForEvent(
  start: string,
  end: string,
): {
  gridColumnStart: string;
  gridColumnEnd: string;
} {
  const startDate = formatISO(new Date(start), { representation: 'date' });
  const endDate = formatISO(addDays(new Date(end), 1), { representation: 'date' });

  return {
    gridColumnStart: `D${startDate}`,
    gridColumnEnd: `D${endDate}`,
  };
}

const DEFAULT_GRID_WIDTH = 150;
export function getScheduleGridColumnWidth(strapiWidth: number | null): number {
  if (!strapiWidth || strapiWidth < 100) {
    return DEFAULT_GRID_WIDTH;
  }
  return strapiWidth;
}

export function filterEventsByDateRange<
  T extends {
    startDate?: string | null;
    endDate?: string | null;
    startDateTime?: string | null;
    endDateTime?: string | null;
  },
>(events: T[], tournamentStartDate: string, tournamentEndDate: string): T[] {
  return events.filter((event) => {
    const startDate = event.startDateTime ?? event.startDate;
    const endDate = event.endDateTime ?? event.endDate;

    return (
      startDate &&
      endDate &&
      new Date(startDate) <= new Date(tournamentEndDate) &&
      new Date(endDate) >= new Date(tournamentStartDate)
    );
  });
}
