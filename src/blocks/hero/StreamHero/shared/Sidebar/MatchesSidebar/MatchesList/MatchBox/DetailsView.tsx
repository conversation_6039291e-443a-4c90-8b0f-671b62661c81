import clsx from 'clsx';
import Image from 'next/image';

import Trophy from '@/ui/assets/icons/trophy.svg';
import ContestantPlaceholderImage from '@/ui/assets/images/ewc-placeholder.png';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { Member } from '../../../../../../../../services/graphql/types/tournament';
import { JsonFieldType } from '../../../../../../../../strapi/types/helper';
import { cn } from '../../../../../../../../utils/cn';
import { Contestant, MapResult as MapResultType } from '../../../../types/StreamHeroMatch';

export const DetailsView = ({
  contestants,
  mapResults,
  status,
  translations,
}: {
  contestants: Contestant[];
  mapResults: MapResultType[];
  status: LocalMatchStatus;
  translations?: JsonFieldType;
}) => {
  return (
    <div className="bg-white-dirty flex flex-col gap-0.5">
      <div className="flex gap-0.5">
        <ContestantCard contestant={contestants[0]} status={status} />
        <ContestantCard contestant={contestants[1]} flip status={status} />
      </div>
      <Stats contestants={contestants} mapResults={mapResults} translations={translations} />
    </div>
  );
};

const ContestantCard = ({
  contestant,
  flip,
  status,
}: {
  contestant?: Contestant;
  flip?: boolean;
  status: LocalMatchStatus;
}) => {
  return (
    <div
      className={clsx('flex flex-1 items-center justify-between gap-2 rounded-sm bg-white px-[9px]', {
        'flex-row-reverse': flip,
      })}
    >
      <div
        className={clsx('flex grow flex-col items-center py-2.5', {
          'opacity-30': !contestant?.isWinner && status === LocalMatchStatus.COMPLETED,
        })}
      >
        <Image
          alt=""
          className="object-containt size-10"
          height={40}
          src={contestant?.logoUrl ?? ContestantPlaceholderImage}
          width={40}
        />
        <span className="font-primary w-full text-center text-[10px] font-bold break-all uppercase">
          {contestant?.name}
        </span>
      </div>
      <div
        className={cn(
          'font-primary flex size-[49px] items-center justify-center rounded-[3px] p-2 text-[24px] font-bold',
          { 'bg-[#f40f30] text-white': status === LocalMatchStatus.LIVE },
          { 'bg-gray text-dark-default': status === LocalMatchStatus.UPCOMING },
          { 'bg-gray text-dark-default': status === LocalMatchStatus.COMPLETED && !contestant?.isWinner },
          { 'bg-dark-default text-white': contestant?.isWinner },
        )}
      >
        {contestant?.score ?? '-'}
      </div>
    </div>
  );
};

const Stats = ({
  contestants,
  mapResults,
  translations,
}: {
  contestants: Contestant[];
  mapResults: MapResultType[];
  translations?: JsonFieldType;
}) => {
  return (
    <div className="flex gap-0.5">
      {contestants[0] && (
        <Roster
          className="max-w-[100px] basis-[calc(50%-68px)]"
          members={contestants[0].members}
          translations={translations}
        />
      )}
      <div className="flex min-w-[136px] grow flex-col items-center gap-0.5">
        {mapResults.map((m) => (
          <MapResult contestants={contestants} key={m.id} mapResult={m} />
        ))}
      </div>
      {contestants[1] && (
        <Roster
          className="max-w-[100px] basis-[calc(50%-68px)]"
          flip
          members={contestants[1].members}
          translations={translations}
        />
      )}
    </div>
  );
};

const MapResult = ({ mapResult, contestants }: { mapResult: MapResultType; contestants: Contestant[] }) => {
  if (!mapResult.results.length) return null;

  const result1 = mapResult.results.find((r) => r.contestantId === contestants[0]?.id);
  const result2 = mapResult.results.find((r) => r.contestantId === contestants[1]?.id);

  return (
    <div className="grid w-full grid-cols-3 items-center gap-0.5 rounded-sm px-0.5 py-1.5 odd:bg-white">
      {result1 && <Result isWinner={result1.isWinner} score={result1.score} />}
      <div className="font-base text-center text-[9px] font-extrabold">{mapResult.name ?? 'TBD'}</div>
      {result2 && <Result flip isWinner={result2.isWinner} score={result2.score} />}
    </div>
  );
};

const Result = ({ score, isWinner, flip }: { score: number | null; isWinner: boolean; flip?: boolean }) => {
  return (
    <div className={clsx('font-primary grid grid-cols-[auto_auto] items-center gap-1 text-[14px] font-bold')}>
      {!flip && <Trophy className={clsx({ 'opacity-0': !isWinner })} height={16} width={16} />}
      <span className={clsx('w-6 text-center', { 'text-gray-dark': !isWinner })}>{score ?? '-'}</span>
      {flip && <Trophy className={clsx({ 'opacity-0': !isWinner })} height={16} width={16} />}
    </div>
  );
};

const Roster = ({
  members,
  flip,
  translations,
  className,
}: {
  members: Member[];
  flip?: boolean;
  translations?: JsonFieldType;
  className?: string;
}) => {
  // Sort members so coaches appear last
  const sortedMembers = [...members].sort((a, b) => {
    if (a.role?.name === 'Coach' && b.role?.name !== 'Coach') return 1;
    if (a.role?.name !== 'Coach' && b.role?.name === 'Coach') return -1;
    return 0;
  });

  return (
    <div className={cn('flex grow flex-col gap-px rounded-sm bg-white p-0.5', className)}>
      {sortedMembers.map((member, index) => (
        <div
          className={clsx('font-base text-dark-default flex gap-1 px-0.5 text-[10px] font-bold', {
            'flex-row-reverse': flip,
          })}
          key={member.player.name}
        >
          <span className="uppercase">
            {member.role?.name === 'Coach' ? (translations?.['coachAbbreviation'] ?? 'c') : index + 1}
          </span>
          <span>{member.player.name}</span>
        </div>
      ))}
    </div>
  );
};
