'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useToggle } from 'usehooks-ts';

import { confirmSignUp } from '@/services/auth/cognito';
import { ConfirmEmailPageData } from '@/strapi/api/single/authPages/types';
import { Button } from '@/ui/components/Button';
import { TextInput } from '@/ui/components/TextInput';
import { showErrorToast, showInfoToast } from '@/ui/components/Toast';

type Props = ConfirmEmailPageData & { username: string };

export const VerifyEmailSection = ({ username, codeFieldPlaceholder, verifyButtonText }: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [confirmationCode, setConfirmationCode] = useState('');
  const [isLoading, toggleIsLoading] = useToggle(false);

  async function verifyEmail() {
    toggleIsLoading();
    try {
      const { isSignUpComplete, nextStep } = await confirmSignUp({ username, confirmationCode });

      if (isSignUpComplete) {
        const isExternalLogin = !!searchParams.get('client_id');
        router.push(`${isExternalLogin ? 'auth' : 'login'}?${searchParams.toString()}`);
        return;
      }

      showInfoToast({ title: 'Signup not completed!', description: `Next step: ${nextStep.signUpStep}.` });
    } catch (error: any) {
      showErrorToast({ title: 'Confirm signup error!', description: error.message });
    }
    toggleIsLoading();
  }

  const isCodeLengthInvalid = confirmationCode.length !== 6;
  return (
    <form
      className="flex flex-col gap-4"
      onSubmit={(e) => {
        e.preventDefault();
        verifyEmail();
      }}
    >
      <TextInput
        placeholder={codeFieldPlaceholder ?? 'Enter code'}
        value={confirmationCode}
        onChange={setConfirmationCode}
      />
      <Button
        brazeEventProperties={{ button_name: `Verify Email Button`, location: `Confirm Email Modal` }}
        isDisabled={isCodeLengthInvalid || isLoading}
        isFullWidth
        text={verifyButtonText ?? 'verify email'}
        type="submit"
      />
    </form>
  );
};
