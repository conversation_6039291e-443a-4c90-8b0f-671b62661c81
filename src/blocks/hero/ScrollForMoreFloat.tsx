import { MdSouth } from 'react-icons/md';

export const ScrollForMoreFloat = ({ text }: { text: string }) => (
  <div className="absolute inset-x-0 bottom-7">
    <div className="mx-auto w-fit rounded-[18px] bg-white/15 p-1.5 ps-[25px] backdrop-blur-[32px]">
      <div className="flex items-center gap-8">
        <p className="text-xs leading-tight text-white uppercase">{text}</p>
        <div className="flex size-[42px] items-center justify-center overflow-hidden rounded-full bg-white/10">
          <div className="relative -top-[15px] flex flex-col items-center gap-4">
            {[...new Array(2)].map((_, i) => (
              <MdSouth className="animate-scroll-for-more-drop size-4 text-white" key={i} />
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
);
