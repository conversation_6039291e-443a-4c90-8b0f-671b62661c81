import { MatchSeriesMetadata } from '../../../../../../../../services/graphql/types/matchSeries';
import { JsonFieldType } from '../../../../../../../../strapi/types/helper';
import { getLocalizedMatchDescriptor } from '../../../../../../../../utils/localization/getLocalizedMatchDescriptor';

interface FFAContestantRowProps {
  groupA: string | null;
  groupB: string | null;
  metadata: MatchSeriesMetadata[];
  apiTranslations?: JsonFieldType;
}

export const FFAContestantRow = ({ groupA, groupB, metadata, apiTranslations }: FFAContestantRowProps) => {
  return (
    <div className="flex items-center justify-center gap-2.5 py-2">
      {groupA && groupB && (
        <>
          <Text>{groupA}</Text>
          <span className="font-primary text-dark-default text-[10px] font-bold">VS</span>
          <Text>{groupB}</Text>
        </>
      )}
      {(!groupA || !groupB) && <Text>{getLocalizedMatchDescriptor(metadata, apiTranslations ?? {})}</Text>}
    </div>
  );
};

const Text = ({ children }: { children: React.ReactNode }) => {
  return <span className="font-primary text-dark-default text-[18px] font-bold">{children}</span>;
};
