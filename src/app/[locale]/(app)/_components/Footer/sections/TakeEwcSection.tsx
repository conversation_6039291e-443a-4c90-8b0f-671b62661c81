import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { FooterSection } from '@/strapi/api/single/footer';

export const TakeEwcSection = ({ label, links }: FooterSection) => {
  return (
    <div className="flex flex-col items-center justify-between gap-3 md:max-lg:items-end">
      {label && <p className="font-primary text-gold-primary text-xs leading-normal font-bold uppercase">{label}</p>}
      <div className="flex gap-2.5 lg:max-xl:flex-col">
        {links.map(
          (l) =>
            l.image && (
              <LocalizedLink
                brazeEventProperties={{ location: 'Footer - Take EWC Section', button_name: `Link (${l.url})` }}
                href={l.url}
                key={l.id}
                target="_blank"
              >
                <StrapiImage alt="Store logo" className="h-10 w-[139px]" image={l.image} />
              </LocalizedLink>
            ),
        )}
      </div>
    </div>
  );
};
