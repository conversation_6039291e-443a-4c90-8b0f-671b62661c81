import clsx from 'clsx';

import { JsonFieldType } from '@/strapi/types/helper';

const defaultStyles =
  'bg-dark-default px-2.5 py-2 whitespace-nowrap text-white font-primary text-[10px] leading-none uppercase md:text-xs';

export const Header = ({ translations }: { translations: JsonFieldType }) => {
  return (
    <thead>
      <tr>
        <th
          className={clsx(
            defaultStyles,
            'w-[50px] rounded-ss-lg ps-2.5 text-start md:w-[84px] md:rounded-ss-2xl md:ps-6',
          )}
        >
          {translations['rank'] ?? 'Rank'}
        </th>
        <th className={clsx(defaultStyles, 'w-auto ps-[42px] text-start md:ps-[54px]')}>
          {translations['club'] ?? 'club'}
        </th>
        <th className={clsx(defaultStyles, 'w-[100px] text-end md:w-[140px]')}>
          {translations['prizePool'] ?? 'prizepool'}
        </th>
        <th
          className={clsx(
            defaultStyles,
            'w-[100px] text-end md:w-[140px]',
            'rounded-se-lg pe-2.5 md:rounded-se-2xl md:pe-6',
          )}
        >
          {translations['clubPoints'] ?? 'club points'}
        </th>
      </tr>
    </thead>
  );
};
