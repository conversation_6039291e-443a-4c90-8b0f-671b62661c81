'use client';

import { FormProvider } from 'react-hook-form';

import { FormDatePicker } from '@/components/form/FormDatePicker';
import { FormSingleSelect } from '@/components/form/FormSingleSelect';
import { FormTextInput } from '@/components/form/FormTextInput';
import { useUpdateUser } from '@/services/auth/hooks';
import { User } from '@/services/auth/types';
import { ProfilePageData } from '@/strapi/api/single/profile/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { showErrorToast } from '@/ui/components/Toast';
import { getProfaneAttributes } from '@/utils/profanity';

import { ProfilePageSectionContainer } from '../ProfilePageSectionContainer';
import { BasicProfileFormValues, useBasicProfileForm } from './hooks';

interface Props extends ProfilePageData {
  user: User;
  translations?: JsonFieldType | null;
}

export const BasicDataSection = ({ user, basicInfo, translations }: Props) => {
  const formMethods = useBasicProfileForm(user, translations);
  const { handleSubmit, formState, reset, setError } = formMethods;

  const { mutateAsync } = useUpdateUser();

  async function validateAndUpdateUser(values: BasicProfileFormValues) {
    const profaneAttributes = getProfaneAttributes(values);
    if (profaneAttributes.length === 0) {
      return updateUser(values);
    }

    for (const attr of profaneAttributes) {
      setError(attr, {
        message:
          translations?.['profanityErrorMessage'] ??
          'This input contains inappropriate language. Please remove any profanity.',
      });
    }
  }

  async function updateUser(values: BasicProfileFormValues) {
    try {
      await mutateAsync(values);
      reset(values);
    } catch (error: any) {
      showErrorToast({ title: 'User update error!', description: error.message });
    }
  }

  const { isDirty, isValid, isSubmitting } = formState;
  return (
    <FormProvider {...formMethods}>
      <form onSubmit={handleSubmit(validateAndUpdateUser)}>
        <ProfilePageSectionContainer subtitle={basicInfo?.subtitle} title={basicInfo?.title}>
          <div className="grid gap-2 md:grid-cols-2 md:gap-x-4 md:gap-y-4 lg:gap-x-8">
            <FormTextInput isDisabled label={basicInfo?.emailFieldLabel ?? 'Email address'} name="email" />
            <FormTextInput label={basicInfo?.usernameFieldLabel ?? 'Username'} name="username" />
            <FormTextInput label={basicInfo?.nameFieldLabel ?? 'Name'} name="name" />
            <FormTextInput label={basicInfo?.surnameFieldLabel ?? 'Surname'} name="surname" />
            <FormDatePicker
              areFutureDatesDisabled
              label={basicInfo?.birthdateFieldLabel ?? 'Date of birth'}
              name="birthdate"
            />
            <FormSingleSelect
              label={basicInfo?.genderFieldLabel ?? 'Gender'}
              name="gender"
              options={[
                { value: 'male', label: translations?.['male'] ?? 'Male' },
                { value: 'female', label: translations?.['female'] ?? 'Female' },
                { value: 'no_answer', label: translations?.['preferNotToAnswer'] ?? 'Prefer not to answer' },
              ]}
            />
          </div>
          <div className="bg-gray h-px" />
          <div className="flex gap-2 max-md:flex-col md:gap-4">
            <div>
              <Button
                brazeEventProperties={{
                  button_name: `Save Changes Button`,
                  location: `Basic Data Section - Update Basic Profile Form`,
                }}
                isDisabled={!isDirty || !isValid || isSubmitting}
                isFullWidth
                text={basicInfo?.saveButtonText ?? 'save'}
                type="submit"
              />
            </div>
            <div>
              <Button
                brazeEventProperties={{
                  button_name: `Change Password Button`,
                  location: `Basic Data Section - Update Basic Profile Form`,
                }}
                isDisabled={isSubmitting}
                isFullWidth
                link="/change-password"
                text={basicInfo?.changePasswordButtonText ?? 'change password'}
                type="button"
                variant="secondary"
              />
            </div>
          </div>
        </ProfilePageSectionContainer>
      </form>
    </FormProvider>
  );
};
