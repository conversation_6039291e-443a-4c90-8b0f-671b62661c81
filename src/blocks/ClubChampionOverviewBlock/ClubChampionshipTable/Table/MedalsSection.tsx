import { medalsMap, MedalType } from './const';

interface Props {
  bronze: number;
  silver: number;
  gold: number;
}

export const MedalsSection = ({ bronze, silver, gold }: Props) => (
  <div className="flex justify-evenly border-y border-[#D9D9D9] py-4 md:justify-around md:max-lg:px-20 lg:justify-center">
    <MedalWithTotal points={bronze} type="bronze" />
    <MedalWithTotal points={silver} type="silver" />
    <MedalWithTotal points={gold} type="gold" />
  </div>
);

interface MedalWithTotalProps {
  type: MedalType;
  points: number;
}

const MedalWithTotal = ({ type, points }: MedalWithTotalProps) => {
  const MedalComponent = medalsMap[type];
  return (
    <div className="flex items-center gap-2 px-5">
      <MedalComponent className="size-8" />
      <p className="font-primary text-dark-default text-[21px] leading-[1] font-bold">{points}</p>
    </div>
  );
};
