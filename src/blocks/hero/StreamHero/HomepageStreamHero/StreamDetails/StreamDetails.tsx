import clsx from 'clsx';
import { memo } from 'react';

import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';

import { GameStreamDetails, SimpleStreamDetails } from '../../shared/StreamDetails';
import { GameStream } from '../../shared/types/GameStream';
import { StreamHeroMatch } from '../../shared/types/StreamHeroMatch';
import { CustomStreamDetails } from './CustomStreamDetails';

interface Props {
  selectedMatchSeries?: StreamHeroMatch | null;
  selectedGameStream: GameStream | null;
  apiTranslations: JsonFieldType;
  translations: JsonFieldType;
}

export const StreamDetails = memo(
  ({ selectedMatchSeries, selectedGameStream, apiTranslations, translations }: Props) => {
    const isMatchSelected = !!selectedMatchSeries;

    const isCustomStream = !!selectedGameStream?.isCustomStream;
    const isCustomStreamLive = isCustomStream && new Date().toISOString() > (selectedGameStream.startTime ?? '');

    const homepageButtonUrl = isCustomStream
      ? selectedGameStream.buttonUrl
      : selectedGameStream?.slug
        ? `/competitions/${selectedGameStream.slug}`
        : null;

    const homepageButtonText = isCustomStream
      ? (selectedGameStream.buttonText ?? '')
      : (translations?.['visitGame'] ?? 'visit game');

    const ButtonComponent = <StreamDetailsButton text={homepageButtonText} url={homepageButtonUrl} />;
    switch (true) {
      case isMatchSelected:
        return (
          <GameStreamDetails
            apiTranslations={apiTranslations}
            button={ButtonComponent}
            gameName={selectedGameStream?.title}
            selectedMatchSeries={selectedMatchSeries}
            translations={translations}
          />
        );
      case isCustomStreamLive:
        return (
          <CustomStreamDetails
            customSchedule={selectedGameStream.customSchedule}
            title={selectedGameStream.title}
            translations={translations}
          />
        );
      default:
        return <SimpleStreamDetails button={ButtonComponent} text={selectedGameStream?.intermissionText} />;
    }
  },
);

StreamDetails.displayName = 'StreamDetails';

interface StreamDetailsProps {
  text: string;
  url?: string | null;
}

const StreamDetailsButton = ({ text, url }: StreamDetailsProps) => {
  return (
    <div className={clsx('md:ms-auto md:w-fit', !url && 'invisible')}>
      <Button
        brazeEventProperties={{
          button_name: `Visit Game Link (${url})`,
          location: 'Homepage Stream Hero - Stream Details',
        }}
        isFullWidth
        link={url}
        text={text}
        variant="white"
      />
    </div>
  );
};
