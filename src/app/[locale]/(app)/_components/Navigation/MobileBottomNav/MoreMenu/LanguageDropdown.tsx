'use client';

import { AnimatePresence, motion } from 'motion/react';
import { RefObject, useRef } from 'react';
import { FaGlobeAmericas } from 'react-icons/fa';
import { GoTriangleDown } from 'react-icons/go';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import { localeTextMap } from '@/components/LanguageDropdown/const';
import { useCurrentLocale, useReplaceLocale } from '@/hooks/i18n';
import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';
import { logButtonClickedEvent } from '@/services/braze';

interface Props {
  disabledLocales?: Locale[];
  onClick: () => void;
}

export const LanguageDropdown = ({ disabledLocales, onClick }: Props) => {
  const [isOpen, toggleIsOpen, setIsOpen] = useToggle(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref as RefObject<HTMLElement>, () => setIsOpen(false));

  const locale = useCurrentLocale();
  const replaceLocale = useReplaceLocale();
  const otherLocales = ALL_LOCALES.filter((l) => l !== locale && !disabledLocales?.includes(l));

  return (
    <div className="bg-white-dirty flex flex-col gap-1 rounded-lg">
      <div className="bg-gray-easy flex cursor-pointer items-center gap-3 p-3" onClick={toggleIsOpen}>
        <div className="size-6 shrink-0 p-[1.5px]">
          <FaGlobeAmericas className="text-dark-default size-full" />
        </div>
        <span className="font-base text-dark-default mt-px text-[13px] leading-none font-bold capitalize">
          {localeTextMap[locale]}
        </span>
        <GoTriangleDown className={`text-dark-default ms-auto transition ${isOpen ? 'rotate-180' : ''}`} size={24} />
      </div>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            animate={{ height: 'auto' }}
            className="overflow-hidden"
            exit={{ height: 0 }}
            initial={{ height: 0 }}
            layout
            transition={{ bounce: 0, duration: 0.1 }}
          >
            {otherLocales.map((l) => (
              <div
                className="bg-white-dirty flex cursor-pointer items-center gap-3 p-3"
                key={l}
                onClick={() => {
                  replaceLocale(l);
                  onClick();
                  logButtonClickedEvent({
                    location: 'Mobile Bottom Navigation - Language Dropdown',
                    button_name: `Change Language to (${localeTextMap[l]})`,
                  });
                }}
              >
                <div className="size-6 shrink-0 p-[1.5px]">
                  <FaGlobeAmericas className="text-dark-default size-full" />
                </div>
                <span className="font-base text-dark-default text-[13px] leading-none font-bold capitalize">
                  {localeTextMap[l]}
                </span>
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
