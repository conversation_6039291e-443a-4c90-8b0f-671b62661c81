'use client';

import { createPortal } from 'react-dom';

import { TournamentEventDetails } from '@/app/[locale]/(app)/schedule/_components/GridContent/TournamentEventDialog/TournamentEventDetails';
import { TournamentEventHeader } from '@/app/[locale]/(app)/schedule/_components/GridContent/TournamentEventDialog/TournamentEventHeader';
import { VerticalScrollFadeWrapper } from '@/app/[locale]/(app)/schedule/_components/GridContent/VerticalScrollWrapper';
import { useLockBodyScroll } from '@/app/[locale]/(app)/schedule/hooks/useLockBodyScroll';
import { GameScheduleBlock } from '@/blocks/GameScheduleBlock';
import { GameType } from '@/strapi/types/collection/game';
import { GameEventType } from '@/strapi/types/collection/gameEvent';
import { GAME_SCHEDULE_BLOCK_KEY } from '@/strapi/types/gamePageBlock';
import { JsonFieldType } from '@/strapi/types/helper';

type Props = {
  gameEvent: GameEventType;
  game: GameType;
  translations: JsonFieldType | null;
  onCloseAction: () => void;
};

export function TournamentEventDialog({ gameEvent, game, translations, onCloseAction }: Props) {
  useLockBodyScroll(true);

  const modal = (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center" onClick={onCloseAction}>
      <div className="absolute inset-0 z-[9998] bg-black/85" />

      <div
        className="bg-white-dirty relative z-[10000] flex h-full w-full flex-col overflow-hidden px-6 py-6 md:h-auto md:max-h-[95vh] md:w-[780px] md:rounded-xl md:shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        <TournamentEventHeader game={game} translations={translations} onClose={onCloseAction} />
        <TournamentEventDetails game={game} gameEvent={gameEvent} translations={translations} />
        {gameEvent.tournamentId && (
          <VerticalScrollFadeWrapper className="bg-white-dirty flex-grow overflow-y-auto py-6" fadeHeight="h-12">
            <GameScheduleBlock
              __component={GAME_SCHEDULE_BLOCK_KEY}
              competitions={[{ competitionName: game?.title ?? '', competitionId: gameEvent.tournamentId }]}
              gameLink={game?.isLinkingToGamePageEnabled && game.slug ? `/competitions/${game.slug}` : undefined}
              id={Math.random()}
              liveUpdatesDisabled
              section={null}
              translations={translations}
            />
          </VerticalScrollFadeWrapper>
        )}
      </div>
    </div>
  );

  return typeof window !== 'undefined' ? createPortal(modal, document.body) : null;
}
