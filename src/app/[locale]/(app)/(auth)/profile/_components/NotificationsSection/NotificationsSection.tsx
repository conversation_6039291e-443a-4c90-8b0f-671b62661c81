import { User } from '@/services/auth/types';
import { ProfilePageData } from '@/strapi/api/single/profile/types';
import { JsonFieldType } from '@/strapi/types/helper';

import { ProfilePageSectionContainer } from '../ProfilePageSectionContainer';
import { ChannelPermissionsList } from './NotificationList';

interface Props extends ProfilePageData {
  user: User;
  translations?: JsonFieldType | null;
}

export const NotificationsSection = ({ user, notifications, translations }: Props) => {
  return (
    <ProfilePageSectionContainer
      subtitle={notifications?.subtitle ?? 'Tailor your notifications to stay connected, only when it matters.'}
      title={notifications?.title ?? 'Notifications'}
    >
      <div className="flex flex-col gap-4">
        <ChannelPermissionsList
          emailNotificationsLabel={translations?.['email'] ?? 'Email'}
          emailNotificationsPermitted={user['custom:notification_email'] === 'true' ? true : false}
          pushNotificationsLabel={translations?.['pushNotifications'] ?? 'Push Notifications'}
          pushNotificationsPermitted={user['custom:notification_push'] === 'true' ? true : false}
          subtitle={notifications?.channelSectionSubtitle}
          title={notifications?.channelSectionTitle}
        />
      </div>
    </ProfilePageSectionContainer>
  );
};
