import { format, Locale } from 'date-fns';
import React, { memo, useMemo } from 'react';

import { ClubData } from '@/app/[locale]/(app)/club-championship-ranking/_utils/types';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { JsonFieldType } from '@/strapi/types/helper';
import { LocalMatchStatus } from '@/utils/matchSeries';

import { GameStream } from '../../types/GameStream';
import { StreamHeroMatch } from '../../types/StreamHeroMatch';
import { CCRankingsSection } from '../CCRankingsSection';
import { useScrollHandlers } from './hooks';
import { MatchesList } from './MatchesList';
import { ScrollLabel } from './ScrollLabel';

interface MatchesSidebarProps {
  matches: StreamHeroMatch[];
  todaysCCRankings: ClubData[] | null | undefined;
  selectedMatchId: string;
  onMatchSelect: (id: string) => void;
  translations?: JsonFieldType;
  apiTranslations?: JsonFieldType;
  isLoading: boolean;
  error?: string;
  selectedGameStream: GameStream | null;
  slug: string | null;
}

export const MatchesSidebar = memo(
  ({
    matches,
    todaysCCRankings,
    translations,
    apiTranslations,
    isLoading,
    error,
    selectedGameStream,
    slug,
    ...rest
  }: MatchesSidebarProps) => {
    const locale = useDateLocale();

    const completedMatches = matches?.filter((m) => m.status === LocalMatchStatus.COMPLETED) ?? [];
    const liveMatches = useMemo(() => matches?.filter((m) => m.status === LocalMatchStatus.LIVE) ?? [], [matches]);
    const upcomingMatches = matches?.filter((m) => m.status === LocalMatchStatus.UPCOMING) ?? [];
    const groupedUpcomingMatches = groupMatchesByStartTime(upcomingMatches, locale);

    const {
      scrollContainerRef,
      combinedLiveClusterRef,
      upcomingClusterRef,
      scrolledFromTop,
      livePosition,
      atBottom,
      scrollToLive,
    } = useScrollHandlers(selectedGameStream, liveMatches.length, upcomingMatches.length, isLoading);

    if (!matches) return null;
    const isLoadingOrError = isLoading || !!error;

    return (
      <div className="flex flex-1/4 flex-col gap-6 bg-white pt-4 pb-2 lg:max-w-[450px] lg:pt-8 lg:pb-5">
        <CCRankingsSection todaysCCRankings={todaysCCRankings} translations={translations} />
        <div
          className="relative flex items-stretch gap-4 px-4 max-lg:overflow-x-auto lg:flex-col lg:overflow-y-auto lg:px-6"
          ref={scrollContainerRef}
        >
          {isLoadingOrError && <div>{isLoading ? 'loading...' : error}</div>}
          {!isLoadingOrError && (
            <>
              {scrolledFromTop && livePosition !== 'above' && completedMatches.length !== 0 && (
                <ScrollLabel
                  bgColor="bg-dark-default"
                  position="top"
                  text={translations?.['completedMatches'] ?? 'Completed Matches'}
                  textColor="text-white"
                />
              )}
              {livePosition === 'above' && (
                <ScrollLabel
                  bgColor="bg-red-accent"
                  position="top"
                  text={translations?.['backToLive'] ?? 'Back to Live'}
                  textColor="text-white"
                  onClick={scrollToLive}
                />
              )}
              {completedMatches.length !== 0 && (
                <MatchesList
                  apiTranslations={apiTranslations}
                  matches={completedMatches}
                  selectedGameStream={selectedGameStream}
                  slug={slug}
                  status={LocalMatchStatus.COMPLETED}
                  translations={translations}
                  {...rest}
                />
              )}
              {liveMatches.length !== 0 && (
                <MatchesList
                  matches={liveMatches}
                  selectedGameStream={selectedGameStream}
                  status={LocalMatchStatus.LIVE}
                  translations={translations}
                  {...rest}
                  apiTranslations={apiTranslations}
                  ref={combinedLiveClusterRef}
                  slug={slug}
                />
              )}
              {groupedUpcomingMatches.length !== 0 &&
                groupedUpcomingMatches.map((gm, i) => (
                  <MatchesList
                    apiTranslations={apiTranslations}
                    matches={gm.matches}
                    ref={i === 0 ? upcomingClusterRef : null}
                    selectedGameStream={selectedGameStream}
                    startTime={gm.startTime}
                    status={LocalMatchStatus.UPCOMING}
                    translations={translations}
                    {...rest}
                    key={gm.startTime}
                    slug={slug}
                  />
                ))}
              {livePosition === 'below' && (
                <ScrollLabel
                  bgColor="bg-red-accent"
                  position="bottom"
                  text={translations?.['backToLive'] ?? 'Back to Live'}
                  textColor="text-white"
                  onClick={scrollToLive}
                />
              )}
              {!atBottom && livePosition !== 'below' && groupedUpcomingMatches.length !== 0 && (
                <ScrollLabel
                  bgColor="bg-dark-default"
                  position="bottom"
                  text={translations?.['scrollForMore'] ?? 'Scroll for More'}
                  textColor="text-white"
                />
              )}
            </>
          )}
        </div>
      </div>
    );
  },
);

MatchesSidebar.displayName = 'MatchesSidebar';

//! matches should already be sorted in the consuming component
function groupMatchesByStartTime(matches: StreamHeroMatch[], locale: Locale) {
  const matchesWithStartTime: { startTime: string; matches: StreamHeroMatch[] }[] = [];

  for (const match of matches) {
    const formattedStartTime = format(match.startTime, 'MMM do', { locale });
    const groupedMatches = matchesWithStartTime.find((m) => m.startTime === formattedStartTime);

    if (groupedMatches) {
      groupedMatches.matches.push(match);
    } else {
      matchesWithStartTime.push({ startTime: formattedStartTime, matches: [match] });
    }
  }

  return matchesWithStartTime;
}
