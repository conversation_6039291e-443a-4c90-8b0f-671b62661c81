import clsx from 'clsx';

import { PostBlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { PageType } from '@/strapi/types/collection/page';

import { Locale } from '../../../../../hooks/i18n/const';
import { RenderHero } from './RenderHero';

export const PostPage = ({
  header,
  blocks,
  hero,
  locale,
  ...rest
}: PageType & Partial<SiteConfigData> & { locale: Locale }) => {
  const shouldRenderHeader = !hero && header;
  return (
    <div className={clsx('flex flex-col items-center gap-8 py-30 lg:gap-12', { 'pt-0': !shouldRenderHeader })}>
      <RenderHero header={header} hero={hero} locale={locale} translations={rest.translations ?? {}} type={rest.type} />
      {blocks.map((b) => (
        <PostBlockRenderer {...b} key={`${b.__component}-${b.id}`} {...rest} />
      ))}
    </div>
  );
};
