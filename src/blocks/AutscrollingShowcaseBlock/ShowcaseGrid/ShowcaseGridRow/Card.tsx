import clsx from 'clsx';

import { StrapiImage } from '@/components/StrapiImage';
import { MediaType } from '@/strapi/types/media';

interface CardProps {
  logo: MediaType | null;
  name: string | null;
}

export const Card = ({ name, logo }: CardProps) => {
  return (
    <div className={containerStyles}>
      <div className="h-28 w-28 overflow-hidden">
        {logo && <StrapiImage className="h-full w-auto justify-self-center object-contain" image={logo} />}
      </div>
      {name && (
        <span className="font-primary text-dark-default truncate text-center text-xs leading-normal font-bold whitespace-nowrap uppercase md:text-sm">
          {name}
        </span>
      )}
    </div>
  );
};

export const CardLoader = () => {
  return <div className={containerStyles} />;
};

const containerStyles = clsx(
  'rounded-2xl bg-white shadow-[0px_4px_10px_0px_#0000000F] xl:rounded-4xl',
  'px-[19px] pt-3 pb-5',
  'xl:px-[28.5px] xl:pt-[14px] xl:pb-[22px]',
  'relative flex h-[182px] flex-col items-center justify-center gap-3',
);
