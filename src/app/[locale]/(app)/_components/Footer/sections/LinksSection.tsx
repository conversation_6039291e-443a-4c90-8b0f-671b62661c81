import { LocalizedLink } from '@/components/LocalizedLink';
import { LinkType } from '@/strapi/types/helper';

interface Props {
  links: LinkType[];
}

export const LinksSection = ({ links }: Props) => (
  <div className="font-primary text-gold-primary flex flex-col items-center gap-2 text-xs leading-normal font-bold uppercase md:items-start">
    {links.map((l) => (
      <LocalizedLink
        brazeEventProperties={{ location: 'Footer - Links Section', button_name: `Link (${l.text})` }}
        href={l.url}
        key={l.id}
      >
        {l.text}
      </LocalizedLink>
    ))}
  </div>
);
