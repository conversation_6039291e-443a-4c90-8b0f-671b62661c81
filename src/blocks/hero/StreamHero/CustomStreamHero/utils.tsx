import { StreamType } from '@/strapi/types/collection/stream';

export function getActiveOrUpcomingDailyStream(dailyStreams: StreamType[] | null | undefined): StreamType | null {
  if (!dailyStreams) return null;

  const now = Date.now();
  let activeStream: StreamType | null = null;
  let upcomingStream: StreamType | null = null;
  let earliestUpcomingTime = Infinity;

  dailyStreams.forEach((stream) => {
    const streamStartTime = stream.startTime ? new Date(stream.startTime).getTime() : 0;
    const streamEndTime = stream.endTime ? new Date(stream.endTime).getTime() : Infinity;

    // Check if stream is currently live
    if (streamStartTime <= now && streamEndTime > now) {
      // If we find a live stream, use it immediately
      activeStream = stream;
      return; // Exit the forEach early since we found a live stream
    }

    // If stream is upcoming and starts sooner than our current upcoming stream
    if (streamStartTime > now && streamStartTime < earliestUpcomingTime) {
      upcomingStream = stream;
      earliestUpcomingTime = streamStartTime;
    }
  });

  // Return the live stream if we found one, otherwise return the earliest upcoming stream
  return activeStream || upcomingStream;
}
