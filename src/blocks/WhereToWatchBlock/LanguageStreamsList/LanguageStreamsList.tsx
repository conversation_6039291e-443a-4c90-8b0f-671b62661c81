'use client';

import clsx from 'clsx';
import { MdChevronLeft } from 'react-icons/md';
import { useToggle } from 'usehooks-ts';

import { StreamProviderType } from '@/strapi/types/collection/streamProvider';
import { JsonFieldType } from '@/strapi/types/helper';

import { StreamsList } from './StreamsList';

interface Props {
  language: string;
  streams: string[];
  providers: StreamProviderType[];
  translations: JsonFieldType;
  isExpanded?: boolean;
}

export const LanguageStreamsList = ({ language, streams, providers, translations, isExpanded = false }: Props) => {
  const [isOpen, toggleIsOpen] = useToggle(isExpanded);

  return (
    <div className="flex flex-col gap-0.5">
      <div
        className={clsx(
          'flex cursor-pointer items-center justify-between rounded-t-sm bg-white p-3 shadow-md md:rounded-t-lg md:p-4 md:ps-6',
          !isOpen && 'rounded-b-sm md:rounded-b-lg',
        )}
        onClick={toggleIsOpen}
      >
        <p className="font-primary text-dark-default text-sm leading-[1.1] font-bold md:text-[21px]">{language}</p>
        <div className="flex items-center gap-2 md:gap-4">
          <div
            className={clsx(
              'bg-dark-default flex items-center gap-1.5 rounded-[3px] p-0.5 pe-1.5',
              'font-primary text-[10px] leading-none font-bold md:text-xs',
            )}
          >
            <div className="text-dark-default flex size-[15px] items-center justify-center rounded-xs bg-white md:size-[21px]">
              <p>{streams.length}</p>
            </div>
            <p className="text-white capitalize">{translations.sources ?? 'sources'}</p>
          </div>
          <MdChevronLeft
            className={clsx('text-dark-default size-5 -rotate-90 transition-transform', isOpen && 'rotate-90')}
          />
        </div>
      </div>
      {isOpen && <StreamsList providers={providers} streams={streams} />}
    </div>
  );
};
