import { Locale } from '@/hooks/i18n/const';
import { VodItemType } from '@/strapi/types/block';
import { StreamIntermissionType, StreamType } from '@/strapi/types/collection/stream';

import { getStrapiDefaultParams, strapiClient } from '../client';
import { enrichVodItems } from '../utils/vod';
import { getVodData, populateVods } from './vods';

export const STREAMS_COLLECTION_KEY = 'streams';

export async function getGameStreams(slug: string, locale: Locale) {
  try {
    const streamsData = await strapiClient.collection(STREAMS_COLLECTION_KEY).find({
      filters: {
        game: {
          slug: {
            $eq: slug,
          },
        },
      },
      populate: {
        game: {
          fields: ['title', 'slug'],
        },
        vods: populateVods,
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = streamsData.data as StreamType[];
    if (!data) {
      return [];
    }

    const streams = assembleWithVodItems(data, locale, slug);
    return streams;
  } catch (error) {
    console.error('Game streams not fetched!', error);
    throw new Error('Game streams not fetched!');
  }
}

async function assembleWithVodItems(streams: StreamType[], locale: Locale, slug: string) {
  const isAnyVodStream = streams.some((s) => s.intermissionType === StreamIntermissionType.VOD);
  if (!isAnyVodStream) {
    return streams;
  }

  const vods = (await getVodData(locale, slug)) as VodItemType[];
  const enrichedVods = await enrichVodItems(vods);
  return streams.map((d) => ({ ...d, vods: enrichedVods }));
}
