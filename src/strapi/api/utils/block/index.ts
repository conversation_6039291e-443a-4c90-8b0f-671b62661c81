import { Locale } from '@/hooks/i18n/const';
import { DynamicZoneBlock } from '@/strapi/types/block';

import { assembleCompetitionsGridBlocks } from './assembleCompetitionsGridBlocks';
import { assembleGameCompetitionsBlocks } from './assembleGameCompetitionsBlocks';
import { assembleNewsBlocks } from './assembleNewsBlocks';
import { assembleVodBlocks } from './assembleVodBlocks';
import { assembleWhereToWatchBlocks } from './assembleWhereToWatchBlocks';
import { assembleFestivalFilterBlocks, assembleFestivalListBlocks, assembleGameScheduleBlocks } from './utils';

/**
 * Assembles various types of blocks with additional data based on the provided locale and optional data.
 *
 * This function processes the list of blocks by invoking several block-specific assembly functions.
 * The purpose is to enrich blocks with additional data where needed (eg. news, game competitions, VOD content...)
 * The function mutates the provided blocks.
 *
 * @param blocks - The array of dynamic zone blocks to be assembled and enriched.
 * @param locale - The content locale.
 * @param data - Optional additional data that may be required by some block assembly functions.
 *
 * @returns A promise that resolves when all asynchronous block assembly operations are complete.
 */
export async function assembleBlocksWithAdditionalData(blocks: DynamicZoneBlock[], locale: Locale, data?: any) {
  await assembleNewsBlocks(blocks, locale);
  await assembleWhereToWatchBlocks(blocks, locale);
  await assembleVodBlocks(blocks, locale, data);

  await assembleCompetitionsGridBlocks(blocks, locale);
  await assembleGameCompetitionsBlocks(blocks, locale);

  assembleGameScheduleBlocks(blocks, data);
  assembleFestivalListBlocks(blocks, data);
  assembleFestivalFilterBlocks(blocks, data);
}
