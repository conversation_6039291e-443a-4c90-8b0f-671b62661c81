/* eslint-disable @next/next/no-page-custom-font */

import '@/ui/styles/globals.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import clsx from 'clsx';
import { notFound } from 'next/navigation';

import { LayoutProps } from '@/config/types/page';
import { DEFAULT_LOCALE } from '@/hooks/i18n/const';
import { getNavigationData } from '@/strapi/api/single/navigation';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import QueryClientProvider from '@/ui/providers/QueryClientProvider';
import { ScreenTypeProvider } from '@/ui/providers/ScreenTypeProvider';
import { fontStyles } from '@/ui/styles/fonts';
import { webviewHideScript } from '@/utils/webviewHideScript';

import i18nConfig from '../../../../i18nConfig';
import { IconsMeta } from '../(app)/_components/IconsMeta';
import { Navigation } from '../(app)/_components/Navigation';

export default async function RootLayout({ params }: LayoutProps) {
  const locale = (await params).locale;

  if (!i18nConfig.locales.includes(locale)) {
    notFound();
  }

  const navigationData = await getNavigationData(locale);
  const siteConfig = await getSiteConfig(locale);

  if (locale !== DEFAULT_LOCALE && siteConfig?.featureFlags?.disabledLocales?.includes(locale)) {
    notFound();
  }

  return (
    <html
      className={clsx(fontStyles, locale === 'ar' && 'font-arabic')}
      dir={locale === 'ar' ? 'rtl' : 'ltr'}
      lang={locale}
    >
      <head>
        <script dangerouslySetInnerHTML={webviewHideScript} />
        <link
          href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0&display=block"
          rel="stylesheet"
        />
        <IconsMeta />
      </head>
      <body className={clsx('flex min-h-screen flex-col antialiased', fontStyles)}>
        <QueryClientProvider>
          <ScreenTypeProvider>
            {navigationData && (
              <Navigation
                data={navigationData}
                disabledLocales={siteConfig?.featureFlags?.disabledLocales}
                isEmbedded
                siteConfig={siteConfig}
              />
            )}
          </ScreenTypeProvider>
        </QueryClientProvider>
      </body>
    </html>
  );
}
