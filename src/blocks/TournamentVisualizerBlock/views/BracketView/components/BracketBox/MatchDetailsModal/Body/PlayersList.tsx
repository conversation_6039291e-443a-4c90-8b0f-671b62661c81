import clsx from 'clsx';

import { MatchDetailsPlayer } from '../types';

interface PlayersListProps {
  players: MatchDetailsPlayer[];
  isReversed?: boolean;
}

export const PlayersList = ({ players, isReversed }: PlayersListProps) => {
  return (
    <div className={clsx('flex flex-1 flex-col gap-[4.6px] rounded-sm bg-white p-1', isReversed && 'items-end')}>
      {players.map((p, i) => (
        <PlayerRow {...p} isReversed={isReversed} key={`${p.name}-${i + 1}`} number={i + 1} />
      ))}
    </div>
  );
};

type PlayerRowProps = MatchDetailsPlayer & { number: number; isReversed?: boolean };

const PlayerRow = ({ name, number, isCoach, isReversed }: PlayerRowProps) => {
  return (
    <div
      className={clsx(
        'font-base text-dark-default flex items-center gap-1 leading-none font-extrabold',
        isReversed && 'flex-row-reverse',
      )}
    >
      <p className="text-[10px]">{isCoach ? 'C' : number}</p>
      <p className="text-xs">{name}</p>
    </div>
  );
};
