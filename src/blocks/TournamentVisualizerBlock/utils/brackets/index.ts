import { MatchSeries, MatchSeriesGameResult } from '@/services/graphql/types/matchSeries';

import { extractMetadata } from '../helpers';
import { Config } from '../types';
import { createBracket, createGroup, createMatch, createRound, patchGroupsStatus, sortMatchGroups } from './helpers';
import { BracketGroup, BracketMatch, BracketMatchType, Round } from './types';

export function getBracketViewGroups(matches: MatchSeries[], config: Config) {
  const groups: BracketGroup[] = [];
  for (const match of matches) {
    const meta = extractMetadata(match);

    let group = groups.find((g) => g.id === meta.groupMeta.id);
    if (!group) {
      group = createGroup(meta, match, config);
      groups.push(group);
      continue;
    }

    let bracket = group.brackets.find((b) => b.id === meta.bracketMeta.id);
    if (!bracket) {
      bracket = createBracket(meta, match, config);
      group.brackets.push(bracket);
      continue;
    }

    let round = bracket.rounds.find((r) => r.id === meta.roundMeta.id);
    if (!round) {
      round = createRound(meta, match, config);
      bracket.rounds.push(round);
      continue;
    }

    round.matches.push(createMatch(meta, match, config));
  }

  sortMatchGroups(groups);
  patchGroupsStatus(groups);

  return groups;
}

export function addWinnerRoundToGroups(
  groups: BracketGroup[],
  { translations }: Config,
  isHokSpecialCasePhase: boolean,
) {
  const allBrackets = groups.flatMap((g) => g.brackets);

  for (const bracket of allBrackets) {
    const finalRound = bracket.rounds.at(-1) as Round;

    const winnerId = 'winner';
    const isFinalsMatch = finalRound.matches.some((m) => m.type === BracketMatchType.FINALS);
    const isThirdPlaceMatch = finalRound.matches.some((m) => m.type === BracketMatchType.THIRD_PLACE);

    const finalRoundMatches: BracketMatch[] = [];

    finalRound.matches.forEach((match, i) => {
      const winner = match.contestants.find((c) =>
        [MatchSeriesGameResult.WIN, MatchSeriesGameResult.DEFAULT_WIN].includes(c.result as MatchSeriesGameResult),
      );

      const contestants = [
        winner ?? {
          id: winnerId,
          score: null,
          logoUrl: null,
          name: translations.tbd ?? 'tbd',
          players: [],
          result: '',
        },
      ];

      const finalRoundMatch = {
        id: `${winnerId}-${i}`,
        contestants,
        matches: [],
        position: i,
        status: '',
        descriptor: '',
        startTime: '',
        topText: isHokSpecialCasePhase
          ? (translations.firstSeed ?? 'first seed')
          : isFinalsMatch
            ? (translations.winner ?? 'winner')
            : isThirdPlaceMatch
              ? (translations.thirdPlace ?? '3rd place')
              : (translations.advances ?? 'advances'),
        type: isFinalsMatch ? BracketMatchType.WINNER : BracketMatchType.QUALIFIED,
      };

      finalRoundMatches.push(finalRoundMatch);
    });

    const winnerRound: Round = {
      id: winnerId,
      name:
        isFinalsMatch || isThirdPlaceMatch
          ? (translations.winner ?? 'winner')
          : (translations.qualified ?? 'qualified'),
      matches: finalRoundMatches,
      position: 99,
    };

    bracket.rounds.push(winnerRound);
  }
}
