import { SIMPLE_FFA_TOURNAMENTS } from '@/config/tournaments';
import { JsonFieldType } from '@/strapi/types/helper';

import { Navigation } from '../NavigationComponent';
import { BracketGroup } from '../utils/brackets/types';
import { OVERALL_TABLE_ID } from '../utils/ffa/const';
import { FfaGroup, FfaMatch } from '../utils/ffa/types';
import { RoundRobinGroup } from '../utils/roundRobin/types';
import { Phase, ViewType } from '../utils/types';
import { BracketView } from './BracketView';
import { FreeForAllTable } from './FreeForAllTable';
import { useSelectedGroup, useSelectedMatch } from './hooks';
import { RoundRobinView } from './RoundRobinView';

interface Props {
  phase: Phase;
  translations: JsonFieldType;
}

export const ViewRenderer = ({ phase, translations }: Props) => {
  const { selectedGroup, setSelectedGroupId } = useSelectedGroup(phase.groups);
  const { selectedMatch, setSelectedMatchId } = useSelectedMatch((selectedGroup as FfaGroup).matches ?? []);

  let View = null;
  switch (phase.type) {
    case ViewType.FFA:
      const isConstrictedTable = SIMPLE_FFA_TOURNAMENTS.includes(phase.id);
      View = (
        <div className="p-4 md:p-8">
          <FreeForAllTable
            {...(selectedMatch as FfaMatch)}
            isConstrictedTable={isConstrictedTable}
            translations={translations}
          />
        </div>
      );
      break;
    case ViewType.BRACKETS:
      View = (
        <div className="py-4 md:py-8">
          <div className="overflow-x-auto">
            <BracketView brackets={(selectedGroup as BracketGroup)?.brackets ?? []} translations={translations} />
          </div>
        </div>
      );
      break;
    case ViewType.ROUND_ROBIN:
    default:
      View = (
        <div className="p-1 md:p-4">
          <RoundRobinView groups={phase.groups as RoundRobinGroup[]} />
        </div>
      );
  }

  const showGroupsNav = [ViewType.FFA, ViewType.BRACKETS].includes(phase.type);
  const showMatchesNav = phase.type === ViewType.FFA && selectedMatch;

  return (
    <div className="flex flex-col gap-0.5">
      {showGroupsNav && (
        <Navigation
          items={phase.groups}
          selectedItemId={selectedGroup.id}
          type="secondary"
          onSelect={(id) => {
            setSelectedGroupId(id);
            if (showMatchesNav) {
              setSelectedMatchId(OVERALL_TABLE_ID);
            }
          }}
        />
      )}
      {showMatchesNav && (
        <Navigation
          items={(selectedGroup as FfaGroup).matches.map((m) => ({ name: m.title, id: m.id }))}
          selectedItemId={selectedMatch.id}
          type="secondary"
          onSelect={setSelectedMatchId}
        />
      )}
      <div className="rounded-b-lg bg-white md:rounded-b-2xl">{View}</div>
    </div>
  );
};
