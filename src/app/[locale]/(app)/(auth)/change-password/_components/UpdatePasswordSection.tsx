'use client';

import { useRouter } from 'next/navigation';
import { FormProvider } from 'react-hook-form';

import { FormPasswordInput } from '@/components/form/FormPasswordInput';
import { updatePassword } from '@/services/auth/cognito';
import { ChangePasswordPageData } from '@/strapi/api/single/authPages/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { showErrorToast, showSuccessToast } from '@/ui/components/Toast';

import { UpdatePasswordFormValues, useUpdatePasswordForm } from './hooks';

type Props = ChangePasswordPageData & { translations?: JsonFieldType | null };

export const UpdatePasswordSection = ({
  currentPasswordFieldPlaceholder,
  newPasswordFieldPlaceholder,
  passwordConfirmFieldPlaceholder,
  updatePasswordButtonText,
  translations,
}: Props) => {
  const router = useRouter();

  const formMethods = useUpdatePasswordForm(translations);
  const { handleSubmit, formState } = formMethods;

  async function handlePasswordUpdate({ oldPassword, password }: UpdatePasswordFormValues) {
    try {
      await updatePassword({ oldPassword, newPassword: password });
      showSuccessToast({ title: translations?.['passwordUpdatedToastMessage'] ?? 'Password updated successfully!' });
      router.push('profile');
    } catch (error: any) {
      showErrorToast({ title: 'Update password error!', description: error.message });
    }
  }

  const { isValid, isSubmitting } = formState;
  return (
    <FormProvider {...formMethods}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(handlePasswordUpdate)}>
        <FormPasswordInput name="oldPassword" placeholder={currentPasswordFieldPlaceholder ?? 'Current password'} />
        <FormPasswordInput name="password" placeholder={newPasswordFieldPlaceholder ?? 'New password'} />
        <FormPasswordInput name="passwordConfirm" placeholder={passwordConfirmFieldPlaceholder ?? 'Confirm password'} />
        <Button
          brazeEventProperties={{ button_name: `Update Password Button`, location: `Change Password Modal` }}
          isDisabled={!isValid || isSubmitting}
          isFullWidth
          text={updatePasswordButtonText ?? 'update password'}
          type="submit"
        />
      </form>
    </FormProvider>
  );
};
