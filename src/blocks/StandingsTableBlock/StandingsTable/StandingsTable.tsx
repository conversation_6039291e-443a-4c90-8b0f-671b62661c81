import { JsonFieldType } from '@/strapi/types/helper';
import SpinnerIcon from '@/ui/assets/icons/spinner.svg';

import { TableStanding } from '../types';
import { Header } from './Header';
import { Row } from './Row';

interface StandingsTableProps {
  standings: TableStanding[] | null;
  translations: JsonFieldType;
  isLoading: boolean;
}

export const StandingsTable = ({ standings, translations, isLoading }: StandingsTableProps) => {
  return (
    <div className="w-full overflow-x-auto">
      <table className="w-full shadow-md">
        <Header translations={translations} />
        <tbody>{!isLoading && standings ? standings.map((s) => <Row {...s} key={s.prize} />) : <Loader />}</tbody>
      </table>
    </div>
  );
};

const Loader = () => {
  return (
    <tr>
      <td colSpan={100}>
        <div className="flex h-[600px] items-center justify-center rounded-b-lg bg-white md:rounded-b-2xl">
          <SpinnerIcon className="text-dark-default size-12" />
        </div>
      </td>
    </tr>
  );
};
