'use client';

import clsx from 'clsx';
import { motion } from 'framer-motion';

import { Prizes } from '@/app/[locale]/(app)/club-championship-ranking/_utils/types';
import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { JsonFieldType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import BlankMedalIcon from '@/ui/assets/icons/blank-medal.svg';
import BronzeMedalIcon from '@/ui/assets/icons/bronze-medal';
import GoldMedalIcon from '@/ui/assets/icons/gold-medal';
import SilverMedalIcon from '@/ui/assets/icons/silver-medal';

import { SimulatedPointsDropdown } from './SimulatedPointsDropdown';

interface Props {
  competitionName: string;
  rank: {
    rank: number | null;
    prizes: Prizes | null;
  }[];
  logoDark: MediaType | null;
  logoLight: MediaType | null;
  slug: string | null;
  translations?: JsonFieldType | null;
  isSimulation: boolean;
  simulatedPoints: number | null;
  isFinished: boolean;
  arePointsVisible?: boolean;
  onSimulatePoints?: (competitionSlug: string, points: number | null) => void;
  clubId: string;
}

export const CompetitionCard = ({
  competitionName,
  logoDark,
  logoLight,
  rank,
  slug,
  translations,
  isSimulation,
  simulatedPoints,
  isFinished,
  arePointsVisible = true,
  onSimulatePoints,
  clubId,
}: Props) => {
  const bestRank = rank
    .filter((r) => r.rank !== null)
    .reduce<{ rank: number | null; prizes: Prizes | null } | null>((best, curr) => {
      if (!best || (curr.rank !== null && best.rank !== null && curr.rank < best.rank)) {
        return curr;
      }
      return best;
    }, null) ?? { rank: null, prizes: null };

  if (!bestRank) return null;

  const getMedalIcon = () => {
    switch (bestRank.rank) {
      case 1:
        return <GoldMedalIcon className="h-[41px] w-[48px]" />;
      case 2:
        return <SilverMedalIcon className="h-[41px] w-[48px]" />;
      case 3:
        return <BronzeMedalIcon className="h-[41px] w-[48px]" />;
      case null:
        return <BlankMedalIcon className="h-[31px] w-[36px] text-[#FFFFFF1A]" />;
      default:
        return <BlankMedalIcon className="h-[31px] w-[36px] text-[#1515151A]" />;
    }
  };

  const content =
    isSimulation && !isFinished ? (
      <div className="text-gold-primary flex h-full min-w-[120px] flex-col items-center gap-2 rounded-sm bg-white p-1 pt-2 text-center font-bold">
        <span className="text-[10px] leading-[110%] capitalize">{translations?.predictNow ?? 'predict now'}</span>
        {logoDark && (
          <div className="flex max-h-[33px] max-w-[112px] grow justify-center p-1">
            <StrapiImage className="object-contain object-center" image={logoDark} />
          </div>
        )}
        <div className="flex w-full flex-col gap-2">
          <div className="font-primary flex flex-col items-center font-bold">
            <span className="text-[24px] leading-[110%]">{simulatedPoints ?? 0}</span>
            <span className="text-[10px] leading-[110%]">{translations?.simulatedPoints ?? 'simulated points'}</span>
          </div>
          {onSimulatePoints && slug && (
            <SimulatedPointsDropdown
              clubId={clubId}
              competitionSlug={slug}
              initialPoints={bestRank.prizes?.XTS ?? 0}
              initialValue={simulatedPoints}
              translations={translations}
              onPointsChange={onSimulatePoints}
            />
          )}
        </div>
      </div>
    ) : (
      <motion.div
        className={clsx('group flex h-full min-h-[147px] w-[84px] flex-col gap-2 rounded-sm px-0.5 py-2', {
          'bg-[radial-gradient(50%_100%_at_50%_0%,_#AD9761_0%,_#907C4B_100%)]': bestRank.rank === 1,
          'bg-[radial-gradient(50%_100%_at_50%_0%,_#A6A6A6_0%,_#727272_100%)]': bestRank.rank === 2,
          'bg-[radial-gradient(50%_100%_at_50%_0%,_#93551B_0%,_#683C13_100%)]': bestRank.rank === 3,
          'bg-white': bestRank.rank !== null && bestRank.rank > 3,
          'bg-dark-default': bestRank.rank === null,
        })}
        transition={{ duration: 0.2 }}
        whileHover={{ scale: 1.05 }}
      >
        <div
          className={clsx('font-primary text-center text-[10px] leading-[110%] font-bold capitalize', {
            'text-black': bestRank.rank !== null && bestRank.rank > 3,
            'text-white': bestRank.rank === null || bestRank.rank <= 3,
          })}
        >
          {bestRank.rank ? (
            <>
              {translations?.position ?? 'position'} #{bestRank.rank}
            </>
          ) : (
            <>{translations?.upcoming ?? 'upcoming'}</>
          )}
        </div>
        <div
          className={clsx('relative flex size-20 items-center justify-center rounded-sm', {
            //   '': rank === 1,
            //   '': rank === 2,
            'bg-[#151515]': bestRank.rank !== null && bestRank.rank <= 3,
            'bg-[#1515150D]': bestRank.rank !== null && bestRank.rank > 3,
            'bg-[#FFFFFF12]': bestRank.rank === null,
          })}
        >
          {getMedalIcon()}
          {arePointsVisible && bestRank.rank !== null && bestRank.prizes && (
            <div
              className={clsx(
                'font-primary absolute inset-x-0 -bottom-1 mx-auto w-fit rounded-xs bg-white px-1 py-0.5 text-[10px] leading-[110%] font-bold capitalize',
                {
                  'text-[#8F7B4A]': bestRank.rank === 1,
                  'text-[#727272]': bestRank.rank === 2 || (bestRank.rank !== null && bestRank.rank > 3),
                  'text-[#683C13]': bestRank.rank === 3,
                },
              )}
            >
              {bestRank.prizes?.XTS ?? 0} {translations?.points ?? 'points'}
            </div>
          )}
        </div>

        {bestRank.rank !== null && logoDark && bestRank.rank > 3 && (
          <StrapiImage
            className={clsx('h-6 w-20 object-contain', { 'group-hover:hidden': !isSimulation })}
            image={logoDark}
          />
        )}
        {bestRank.rank !== null && logoLight && bestRank.rank <= 3 && (
          <StrapiImage
            className={clsx('h-6 w-20 object-contain', { 'group-hover:hidden': !isSimulation })}
            image={logoLight}
          />
        )}
        {bestRank.rank === null && logoLight && (
          <StrapiImage
            className={clsx('h-6 w-20 object-contain', { 'group-hover:hidden': !isSimulation })}
            image={logoLight}
          />
        )}
        {!isSimulation && (
          <div className="text-button-default bg-dark-default font-primary hidden rounded-sm px-2 py-1 text-center text-[8px] leading-none font-bold text-white uppercase hover:bg-[#212121] lg:group-hover:block">
            {translations?.['visitGame'] ?? 'visit game'}
          </div>
        )}
      </motion.div>
    );

  if (slug && !isSimulation) {
    return (
      <LocalizedLink
        brazeEventProperties={{
          button_name: `Competition Card - ${competitionName} Link`,
          location: 'Club Championship Ranking table',
        }}
        href={`/competitions/${slug}`}
      >
        {content}
      </LocalizedLink>
    );
  }

  return content;
};
