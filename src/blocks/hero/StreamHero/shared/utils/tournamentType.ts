import { TournamentVariant } from '@/services/graphql/types/matchSeries';

enum TournamentType {
  PVP = 'PVP',
  FFA = 'FFA',
}

/**
 * Utility function to determine if a tournament variant is PVP or FFA
 * @param variant - The tournament variant to check
 * @returns TournamentType.PVP for team-based/head-to-head variants, TournamentType.FFA for free-for-all variants
 */
export function getTournamentType(variant: TournamentVariant): TournamentType {
  const ffaVariants = [TournamentVariant.ffa_squad, TournamentVariant.ffa_duo, TournamentVariant.ffa_solo];

  return ffaVariants.includes(variant) ? TournamentType.FFA : TournamentType.PVP;
}

/**
 * Utility function to check if a tournament variant is PVP
 * @param variant - The tournament variant to check
 * @returns true if the variant is PVP, false otherwise
 */
export function isPVPTournament(variant: TournamentVariant): boolean {
  return getTournamentType(variant) === TournamentType.PVP;
}

/**
 * Utility function to check if a tournament variant is FFA
 * @param variant - The tournament variant to check
 * @returns true if the variant is FFA, false otherwise
 */
export function isFFATournament(variant: TournamentVariant): boolean {
  return getTournamentType(variant) === TournamentType.FFA;
}
