import clsx from 'clsx';

import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { NewsArticleType } from '@/strapi/types/collection/news';

import { Badge } from './Badge';

export const NewsThumbnailCard = ({ title, slug, summary, cover, tags, isArchived }: NewsArticleType) => {
  const isArchivedStyle = isArchived || tags.some((t) => t.name === '2024');
  const summaryFirstChild = summary?.[0].children[0];
  const summaryContent = summaryFirstChild?.type === 'text' ? summaryFirstChild.text : undefined;

  return (
    <Wrapper slug={slug}>
      <div className="bg-dark-default/20 overflow-hidden rounded-tl-lg rounded-tr-lg xl:rounded-tl-4xl xl:rounded-tr-4xl">
        {cover && (
          <StrapiImage
            className={clsx(
              'aspect-video object-cover transition-transform duration-300 group-hover:scale-110',
              'rounded-tl-lg rounded-tr-lg xl:rounded-tl-4xl xl:rounded-tr-4xl',
              isArchivedStyle && 'opacity-60 grayscale-100',
            )}
            image={cover}
          />
        )}
      </div>
      <div className="relative grow">
        <div className="absolute start-5 -top-3 flex gap-1.5">
          {tags.map((t) => (
            <Badge key={t.id} text={t.displayName ?? ''} variant={t.name === '2024' ? 'black' : 'gold'} />
          ))}
          {isArchivedStyle && <Badge text="archived" variant="black" />}
        </div>
        <div className="p-[14px] pb-[22px] xl:px-[26px] xl:py-[22px]">
          <div className="flex flex-col gap-1">
            {title && (
              <p className="font-primary text-dark-default line-clamp-2 text-base leading-[18px] font-bold uppercase">
                {title}
              </p>
            )}
            {summaryContent && (
              <p className="line-clamp-3 text-sm leading-snug font-medium text-[#858585]">{summaryContent}</p>
            )}
          </div>
        </div>
      </div>
    </Wrapper>
  );
};

const Wrapper = ({ slug, children }: React.PropsWithChildren<{ slug: string | null }>) => {
  const className = clsx(
    'group min-w-[240px] lg:max-w-[340px]',
    'flex flex-1 flex-col self-stretch',
    'rounded-lg bg-white shadow-md xl:rounded-4xl',
    slug && 'cursor-pointer',
  );

  return slug ? (
    <LocalizedLink
      brazeEventProperties={{ location: 'News Thumbnail Card', button_name: `Card Link (news/${slug})` }}
      className={className}
      href={`news/${slug}`}
    >
      {children}
    </LocalizedLink>
  ) : (
    <div className={className}>{children}</div>
  );
};
