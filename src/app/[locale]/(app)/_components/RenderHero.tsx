import React from 'react';

import { HomepageStreamHero } from '@/blocks/hero/StreamHero/HomepageStreamHero';
import { TournamentFinishedHero } from '@/blocks/hero/TournamentFinishedHero';
import { TournamentUpcomingHero } from '@/blocks/hero/TournamentUpcomingHero';
import { Locale } from '@/hooks/i18n/const';
import { JsonFieldType } from '@/strapi/types/helper';
import {
  HERO_TOURNAMENT_FINISHED_BLOCK_KEY,
  HERO_TOURNAMENT_UPCOMING_BLOCK_KEY,
  HeroTournamentFinishedBlockType,
  HeroTournamentUpcomingBlockType,
  HOMEPAGE_STREAM_HERO_BLOCK_KEY,
  HomepageStreamHeroType,
} from '@/strapi/types/hero';

interface Props {
  hero: HomepageStreamHeroType | HeroTournamentUpcomingBlockType | HeroTournamentFinishedBlockType | null;
  translations: JsonFieldType;
  apiTranslations: JsonFieldType;
  locale: Locale;
}

export const RenderHero = async ({ hero, translations, apiTranslations }: Props) => {
  if (!hero) {
    return null;
  }

  if (hero.__component === HOMEPAGE_STREAM_HERO_BLOCK_KEY) {
    return (
      <HomepageStreamHero
        {...(hero as HomepageStreamHeroType)}
        apiTranslations={apiTranslations}
        translations={translations}
      />
    );
  }

  if (hero.__component === HERO_TOURNAMENT_UPCOMING_BLOCK_KEY) {
    return <TournamentUpcomingHero {...(hero as HeroTournamentUpcomingBlockType)} translations={translations} />;
  }
  if (hero.__component === HERO_TOURNAMENT_FINISHED_BLOCK_KEY) {
    return <TournamentFinishedHero {...(hero as HeroTournamentFinishedBlockType)} />;
  }

  return null;
};
