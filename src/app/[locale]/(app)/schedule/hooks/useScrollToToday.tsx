import { useEffect, useRef } from 'react';

import { useContentDirection } from '@/hooks/i18n';

interface UseScrollToTodayProps {
  allDates: string[];
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  dayWidth: number;
  isEnabled?: boolean;
}

export const useScrollToToday = ({
  allDates,
  scrollContainerRef,
  dayWidth,
  isEnabled = true,
}: UseScrollToTodayProps) => {
  const { isRTL } = useContentDirection();
  const hasScrolledRef = useRef(false);

  useEffect(() => {
    if (!isEnabled || hasScrolledRef.current || !scrollContainerRef.current) return;

    const today = new Date().toISOString().split('T')[0];
    const index = allDates.findIndex((d) => d === today);

    if (index !== -1) {
      const scrollPosition = index * dayWidth;

      scrollContainerRef.current.scrollTo({
        left: isRTL ? -scrollPosition : scrollPosition,
        behavior: 'smooth',
      });

      hasScrolledRef.current = true;
    }
  }, [allDates, scrollContainerRef, dayWidth, isEnabled, isRTL]);
};
