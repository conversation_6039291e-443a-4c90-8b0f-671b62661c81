import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getNewsPageData, getNewsPageSeo } from '@/strapi/api/single/news';

import { getSiteConfig } from '../../../../strapi/api/single/siteConfig';
import { NewsAggregator } from './_components/NewsAggregator';
import { NewsFilter } from './_components/NewsAggregator';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const seo = await getNewsPageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | News',
    description: seo?.metaDescription ?? 'Esports World Cup | News',
  };
}

export default async function NewsPage({ params, searchParams }: PageProps) {
  const locale = (await params).locale;
  const { page, search, filter } = await searchParams;

  const pageNumber = isNaN(Number(page)) ? 1 : Number(page);
  const searchValue = typeof search === 'string' && search.length > 0 ? search : undefined;
  const filterValue =
    typeof filter === 'string' && ['all', 'latest', 'archived', 'pr'].includes(filter)
      ? (filter as NewsFilter)
      : ('all' as NewsFilter);

  const newsPageData = await getNewsPageData(locale, pageNumber, searchValue, filterValue);
  const siteConfig = await getSiteConfig(locale);

  if (!newsPageData) {
    return notFound();
  }

  const { pageData, articlesData } = newsPageData;

  const { title, subtitle } = pageData;

  const isHeaderVisible = title || subtitle;
  return (
    <div className="px-4 py-15 lg:px-8">
      <div className="relative mx-auto flex max-w-[1400px] flex-col gap-5 md:gap-8">
        {isHeaderVisible && (
          <section className="text-dark-default flex flex-col gap-1">
            {title && <h2 className="text-h3">{title}</h2>}
            {subtitle && <p className="font-base text-sm leading-[1.6] md:text-[18px]">{subtitle}</p>}
          </section>
        )}
        {articlesData && (
          <NewsAggregator
            articles={articlesData.articles}
            currentPage={articlesData.meta.pagination?.page ?? 1}
            filter={filterValue}
            pages={articlesData.meta.pagination?.pageCount ?? 1}
            searchValue={searchValue ?? ''}
            translations={siteConfig?.translations}
          />
        )}
      </div>
    </div>
  );
}
