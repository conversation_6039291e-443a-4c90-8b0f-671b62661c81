import Image from 'next/image';

import { useDateLocaleFormatter } from '@/hooks/i18n/useDateLocale';
import { JsonFieldType } from '@/strapi/types/helper';
import PlaceholderImage from '@/ui/assets/images/ewc-placeholder.png';

import { StreamHeroMatch } from '../../../types/StreamHeroMatch';
import { isFFATournament } from '../../../utils/tournamentType';
import { getStage } from './utils';

export const MatchCard = ({
  metadata,
  startTime,
  type,
  contestants,
  apiTranslations,
  translations,
}: StreamHeroMatch & { apiTranslations: JsonFieldType; translations: JsonFieldType }) => {
  const descriptor = getStage(metadata, apiTranslations);
  const format = useDateLocaleFormatter();
  const [team1, team2] = contestants;

  return (
    <div className="flex w-[195px] flex-col rounded-sm bg-white p-1 pb-2 shadow-sm xl:w-[266px] xl:gap-2 xl:rounded-lg xl:p-2">
      <div className="flex items-center justify-between">
        <Badge text={descriptor ?? ''} />
        <Badge text={format(startTime, 'hh:mm a')} />
      </div>
      {isFFATournament(type) ? (
        <div className="flex h-25 w-[250px] items-center justify-center">
          <p className="text-h5 text-dark-default">{descriptor}</p>
        </div>
      ) : (
        <div className="flex items-center xl:gap-3">
          <TeamDescriptor logoUrl={team1.logoUrl} name={team1.name} />
          <p className="text-h6 text-dark-default uppercase">{translations.vs ?? 'vs'}</p>
          <TeamDescriptor logoUrl={team2.logoUrl} name={team2.name} />
        </div>
      )}
    </div>
  );
};

const Badge = ({ text }: { text: string }) => (
  <p className="bg-dark-default font-primary rounded-xs p-[3px] text-[10px] leading-none font-bold text-white uppercase xl:p-1 xl:text-xs">
    {text}
  </p>
);

const TeamDescriptor = ({ name, logoUrl }: { name: string; logoUrl: string | null }) => (
  <div className="flex flex-col items-center">
    <div className="px-2.5">
      <Image
        alt=""
        className="size-15 object-contain xl:size-20"
        height={80}
        src={logoUrl ?? PlaceholderImage}
        width={80}
      />
    </div>
    <p className="text-tag text-dark-default max-w-[80px] truncate xl:max-w-[100px]">{name}</p>
  </div>
);
